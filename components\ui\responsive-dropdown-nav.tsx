'use client';

import { useState, useEffect, useRef } from 'react';
import { ChevronDown, Menu, X, Home, Package, CreditCard, Users, Phone, Info, Flame } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from './button';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';

interface NavItem {
  href: string;
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
}

interface ResponsiveDropdownNavProps {
  className?: string;
  variant?: 'tablet-landscape' | 'mobile';
  t?: (key: string) => string;
  handleProductsNavigation?: (e: React.MouseEvent) => void;
}

export function ResponsiveDropdownNav({ className, variant = 'tablet-landscape', t: propT, handleProductsNavigation }: ResponsiveDropdownNavProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();
  const { t: settingsT } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const t = propT || settingsT;

  useEffect(() => {
    setMounted(true);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Close dropdown on route change
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  if (!mounted) return null;

  const navItems: NavItem[] = [
    {
      href: '/',
      label: t?.('home') || 'الرئيسية',
      icon: Home,
    },
    {
      href: '/hot-deals',
      label: t?.('hotDeals') || 'العروض الساخنة',
      icon: Flame,
    },
    {
      href: '/products',
      label: t?.('products') || 'المنتجات',
      icon: Package,
    },
    {
      href: '/payment-methods',
      label: t?.('paymentMethods') || 'طرق الدفع',
      icon: CreditCard,
    },
    {
      href: '/follow-us',
      label: t?.('followUs') || 'تابعنا',
      icon: Users,
    },
    {
      href: '/about',
      label: t?.('aboutUs') || 'من نحن',
      icon: Info,
    },
    {
      href: '/contact',
      label: t?.('contactUs') || 'اتصل بنا',
      icon: Phone,
    },
  ];

  const isTabletLandscape = variant === 'tablet-landscape';
  const isMobile = variant === 'mobile';

  return (
    <div className={cn('relative responsive-dropdown-nav', className)} ref={dropdownRef}>
      {/* Dropdown Trigger Button */}
      <Button
        variant="ghost"
        size={isTabletLandscape ? 'sm' : 'default'}
        className={cn(
          'flex items-center gap-2 transition-all duration-200',
          isTabletLandscape && 'h-8 px-3 text-sm font-medium',
          isMobile && 'h-10 px-4',
          isOpen && 'bg-accent text-accent-foreground'
        )}
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <Menu className={cn(
          isTabletLandscape ? 'h-4 w-4' : 'h-5 w-5'
        )} />
        <span className={cn(
          'font-medium',
          isTabletLandscape && 'text-xs',
          isMobile && 'text-sm'
        )}>
          {t?.('menu') || 'القائمة'}
        </span>
        <ChevronDown className={cn(
          'transition-transform duration-200',
          isTabletLandscape ? 'h-3 w-3' : 'h-4 w-4',
          isOpen && 'rotate-180'
        )} />
      </Button>

      {/* Sidebar Menu */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-black/50 z-40 sidebar-backdrop" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Sidebar Content */}
          <div className={cn(
            'fixed top-0 right-0 h-full bg-white shadow-xl z-50 sidebar-content',
            'transform transition-transform duration-300 ease-in-out sidebar-enter',
            'w-80 max-w-[85vw]',
            'overflow-y-auto'
          )}>
            {/* Sidebar Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-100 bg-gray-50">
              <h3 className="font-semibold text-gray-900 text-lg">
                {(t as any)?.('navigation') || 'التنقل'}
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="h-8 w-8 p-0 hover:bg-gray-200"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Navigation Items */}
            <nav className="py-4 px-2">
              {navItems.map((item, index) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;
                const isProductsLink = item.href === '/products';
                
                if (isProductsLink && handleProductsNavigation) {
                  return (
                    <button
                      key={item.href}
                      onClick={(e) => {
                        handleProductsNavigation(e);
                        setIsOpen(false);
                      }}
                      className={cn(
                        'w-full flex items-center gap-4 px-4 py-4 text-base transition-all duration-200',
                        'hover:bg-gray-100 hover:text-gray-900 rounded-lg mx-2 mb-1',
                        'focus:bg-gray-100 focus:text-gray-900 focus:outline-none',
                        isActive && 'bg-blue-50 text-blue-600 font-medium shadow-sm'
                      )}
                      style={isActive ? { color: primaryColor } : {}}
                    >
                      {Icon && (
                        <Icon className="flex-shrink-0 h-5 w-5" />
                      )}
                      <span className="flex-1 text-right font-medium">{item.label}</span>
                      {isActive && (
                        <div className="w-2 h-2 rounded-full flex-shrink-0" style={{ backgroundColor: primaryColor }} />
                      )}
                    </button>
                  );
                }
                
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      'flex items-center gap-4 px-4 py-4 text-base transition-all duration-200',
                      'hover:bg-gray-100 hover:text-gray-900 rounded-lg mx-2 mb-1',
                      'focus:bg-gray-100 focus:text-gray-900 focus:outline-none',
                      isActive && 'bg-blue-50 text-blue-600 font-medium shadow-sm'
                    )}
                    style={isActive ? { color: primaryColor } : {}}
                  >
                    {Icon && (
                      <Icon className="flex-shrink-0 h-5 w-5" />
                    )}
                    <span className="flex-1 text-right font-medium">{item.label}</span>
                    {isActive && (
                      <div className="w-2 h-2 rounded-full flex-shrink-0" style={{ backgroundColor: primaryColor }} />
                    )}
                  </Link>
                );
              })}
            </nav>


          </div>
        </>
      )}
    </div>
  );
}

// Export a specialized version for tablet landscape
export function TabletLandscapeDropdownNav({ 
  className, 
  t, 
  handleProductsNavigation 
}: { 
  className?: string;
  t?: (key: string) => string;
  handleProductsNavigation?: (e: React.MouseEvent) => void;
}) {
  return (
    <ResponsiveDropdownNav 
      className={className} 
      variant="tablet-landscape"
      t={t}
      handleProductsNavigation={handleProductsNavigation}
    />
  );
}

// Export a specialized version for mobile
export function MobileDropdownNav({ className }: { className?: string }) {
  return (
    <ResponsiveDropdownNav 
      variant="mobile" 
      className={className}
    />
  );
}