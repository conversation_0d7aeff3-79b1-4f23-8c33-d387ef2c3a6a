"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";

interface CampaignItem {
  CampaignId: string;
  MainTitle: string;
  DiscountTitle: string;
  CoverPictureUrl: string;
  Body?: string;
}

const CompaignSection = () => {
  const [campaignList, setCampaignList] = useState<CampaignItem[]>([]);
  const [adminPanelBaseURL] = useState<string>(
    process.env.NEXT_PUBLIC_ADMIN_BASE_URL || ""
  );

  useEffect(() => {
    const getCampaignList = async () => {
      try {
        // Prepare parameters and headers according to API requirements
        const param = {
          requestParameters: {
            PageNo: 1,
            PageSize: 12, // Fetch more campaigns (12 allows for 4 rows of 3)
            recordValueJson: "[]",
          },
        };

        const headers = {
          "Content-Type": "application/json",
          Accept: "application/json",
        };

        // Import MakeApiCallAsync from api-helper
        const { MakeApiCallAsync } = await import("@/lib/api-helper");

        // Use the API helper to make the call
        const response = await MakeApiCallAsync(
          "get-web-campaign-list",
          null,
          param,
          headers,
          "POST",
          true
        );

        // Process the response
        if (response?.data?.data) {
          try {
            const parsedData = JSON.parse(response.data.data);
            console.log("Campaign data:", parsedData);

            if (Array.isArray(parsedData) && parsedData.length > 0) {
              // Transform the API data to match our CampaignItem interface
              const transformedCampaigns = parsedData.map((item) => ({
                CampaignId: item.CampaignId?.toString() || "",
                MainTitle: item.MainTitle || item.Title || "",
                DiscountTitle: item.DiscountTitle || item.SubTitle || "",
                CoverPictureUrl:
                  item.CoverPictureUrl ||
                  item.ImageUrl ||
                  "/images/campaign/placeholder.jpg",
                Body: item.Body || "", // Include Body field from API response
              }));

              setCampaignList(transformedCampaigns);
            } else {
              // If no campaigns found, use mock data with more items
              const mockData: CampaignItem[] = [
                {
                  CampaignId: "1",
                  MainTitle: "Summer Collection",
                  DiscountTitle: "Up to 50% Off",
                  CoverPictureUrl: "/images/campaign/summer.jpg",
                  Body: "https://example.com/summer-collection", // Example external URL
                },
                {
                  CampaignId: "2",
                  MainTitle: "Winter Essentials",
                  DiscountTitle: "Save 30%",
                  CoverPictureUrl: "/images/campaign/winter.jpg",
                  Body: "", // Empty body will use the default campaign page
                },
                {
                  CampaignId: "3",
                  MainTitle: "New Arrivals",
                  DiscountTitle: "Fresh Styles",
                  CoverPictureUrl: "/images/campaign/new.jpg",
                  Body: "https://example.com/new-arrivals", // Example external URL
                },
                {
                  CampaignId: "4",
                  MainTitle: "Medical Courses",
                  DiscountTitle: "Best Prices",
                  CoverPictureUrl: "/images/campaign/medical.jpg",
                  Body: "", // Empty body will use the default campaign page
                },
                {
                  CampaignId: "5",
                  MainTitle: "E-Books Collection",
                  DiscountTitle: "Digital Library",
                  Body: "https://example.com/ebooks", // Example external URL
                  CoverPictureUrl: "/images/campaign/ebooks.jpg",
                },
                {
                  CampaignId: "6",
                  MainTitle: "Printed Books",
                  DiscountTitle: "Physical Copies",
                  CoverPictureUrl: "/images/campaign/books.jpg",
                },
                {
                  CampaignId: "7",
                  MainTitle: "Cardiology Special",
                  DiscountTitle: "Heart Health",
                  CoverPictureUrl: "/images/campaign/cardiology.jpg",
                },
                {
                  CampaignId: "8",
                  MainTitle: "Surgery Guides",
                  DiscountTitle: "Expert Techniques",
                  CoverPictureUrl: "/images/campaign/surgery.jpg",
                },
                {
                  CampaignId: "9",
                  MainTitle: "Pediatrics Course",
                  DiscountTitle: "Child Care",
                  CoverPictureUrl: "/images/campaign/pediatrics.jpg",
                },
              ];
              setCampaignList(mockData);
            }
          } catch (parseError) {
            console.error("Error parsing campaign data:", parseError);
            // Use mock data as fallback
            const mockData: CampaignItem[] = [
              {
                CampaignId: "1",
                MainTitle: "Summer Collection",
                DiscountTitle: "Up to 50% Off",
                CoverPictureUrl: "/images/campaign/summer.jpg",
              },
              {
                CampaignId: "2",
                MainTitle: "Winter Essentials",
                DiscountTitle: "Save 30%",
                CoverPictureUrl: "/images/campaign/winter.jpg",
              },
              {
                CampaignId: "3",
                MainTitle: "New Arrivals",
                DiscountTitle: "Fresh Styles",
                CoverPictureUrl: "/images/campaign/new.jpg",
              },
              {
                CampaignId: "4",
                MainTitle: "Medical Courses",
                DiscountTitle: "Best Prices",
                CoverPictureUrl: "/images/campaign/medical.jpg",
              },
              {
                CampaignId: "5",
                MainTitle: "E-Books Collection",
                DiscountTitle: "Digital Library",
                CoverPictureUrl: "/images/campaign/ebooks.jpg",
              },
              {
                CampaignId: "6",
                MainTitle: "Printed Books",
                DiscountTitle: "Physical Copies",
                CoverPictureUrl: "/images/campaign/books.jpg",
              },
            ];
            setCampaignList(mockData);
          }
        } else {
          console.error("Invalid or empty response from API");
          // Use mock data as fallback
          const mockData: CampaignItem[] = [
            {
              CampaignId: "1",
              MainTitle: "Summer Collection",
              DiscountTitle: "Up to 50% Off",
              CoverPictureUrl: "/images/campaign/summer.jpg",
            },
            {
              CampaignId: "2",
              MainTitle: "Winter Essentials",
              DiscountTitle: "Save 30%",
              CoverPictureUrl: "/images/campaign/winter.jpg",
            },
            {
              CampaignId: "3",
              MainTitle: "New Arrivals",
              DiscountTitle: "Fresh Styles",
              CoverPictureUrl: "/images/campaign/new.jpg",
            },
            {
              CampaignId: "4",
              MainTitle: "Medical Courses",
              DiscountTitle: "Best Prices",
              CoverPictureUrl: "/images/campaign/medical.jpg",
            },
            {
              CampaignId: "5",
              MainTitle: "E-Books Collection",
              DiscountTitle: "Digital Library",
              CoverPictureUrl: "/images/campaign/ebooks.jpg",
            },
            {
              CampaignId: "6",
              MainTitle: "Printed Books",
              DiscountTitle: "Physical Copies",
              CoverPictureUrl: "/images/campaign/books.jpg",
            },
          ];
          setCampaignList(mockData);
        }
      } catch (error) {
        console.error("Error fetching campaign data:", error);
        // Use mock data as fallback
        const mockData: CampaignItem[] = [
          {
            CampaignId: "1",
            MainTitle: "Summer Collection",
            DiscountTitle: "Up to 50% Off",
            CoverPictureUrl: "/images/campaign/summer.jpg",
          },
          {
            CampaignId: "2",
            MainTitle: "Winter Essentials",
            DiscountTitle: "Save 30%",
            CoverPictureUrl: "/images/campaign/winter.jpg",
          },
          {
            CampaignId: "3",
            MainTitle: "New Arrivals",
            DiscountTitle: "Fresh Styles",
            CoverPictureUrl: "/images/campaign/new.jpg",
          },
          {
            CampaignId: "4",
            MainTitle: "Medical Courses",
            DiscountTitle: "Best Prices",
            CoverPictureUrl: "/images/campaign/medical.jpg",
          },
          {
            CampaignId: "5",
            MainTitle: "E-Books Collection",
            DiscountTitle: "Digital Library",
            CoverPictureUrl: "/images/campaign/ebooks.jpg",
          },
          {
            CampaignId: "6",
            MainTitle: "Printed Books",
            DiscountTitle: "Physical Copies",
            CoverPictureUrl: "/images/campaign/books.jpg",
          },
        ];
        setCampaignList(mockData);
      }
    };

    getCampaignList();
  }, []);

  // No longer needed as we're using direct paths
  // const getLanguageCodeFromSession = (): string => {
  //   return "en"; // Default to English, in a real app this would get the actual language code
  // };

  return (
    <section className="py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {campaignList &&
            campaignList.map((item, i) => (
              <Link
                key={item.CampaignId || i}
                href={item.Body && item.Body.startsWith('http') ? item.Body : `/campaign/${item.CampaignId}/${encodeURIComponent(item.MainTitle.replace(/\s+/g, '-').toLowerCase())}`}
                target={item.Body && item.Body.startsWith('http') ? "_blank" : "_self"}
                rel={item.Body && item.Body.startsWith('http') ? "noopener noreferrer" : ""}
                className="relative overflow-hidden rounded-lg group block shadow-md hover:shadow-lg transition-shadow duration-300"
              >
                <div className="aspect-[4/3] relative">
                  <Image
                    src={adminPanelBaseURL + item.CoverPictureUrl}
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                    alt={item.MainTitle}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    onError={(e) => {
                      // If image fails to load, replace with a placeholder
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://placehold.co/400x300/cccccc/666666?text=Campaign+Image';
                    }}
                  />
                  
                  {/* Overlay with campaign info */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-4 left-4 right-4 text-white">
                      <h3 className="text-lg font-bold mb-1">{item.MainTitle}</h3>
                      <p className="text-sm opacity-90">{item.DiscountTitle}</p>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
        </div>
      </div>
    </section>
  );
};

export { CompaignSection };
