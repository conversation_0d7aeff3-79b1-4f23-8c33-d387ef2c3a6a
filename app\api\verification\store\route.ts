import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber, verificationCode, expirationMinutes = 10 } = await request.json();

    console.log('📥 Verification store API called with:', { phoneNumber, verificationCode, expirationMinutes });
    console.log('🔧 Environment variables:', {
      ADMIN_BASE_URL: process.env.ADMIN_BASE_URL,
      NEXT_PUBLIC_ADMIN_BASE_URL: process.env.NEXT_PUBLIC_ADMIN_BASE_URL
    });

    if (!phoneNumber || !verificationCode) {
      console.log('❌ Missing required fields:', { phoneNumber: !!phoneNumber, verificationCode: !!verificationCode });
      return NextResponse.json(
        { success: false, error: 'Phone number and verification code are required' },
        { status: 400 }
      );
    }

    // Get client IP and User Agent for security
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Call .NET API to store verification code
    const rawBase = process.env.ADMIN_BASE_URL || process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'https://admin.codemedicalapps.com';
    const base = rawBase.endsWith('/') ? rawBase.slice(0, -1) : rawBase;
    const backendUrl = `${base}/api/v1/verification/store`;
    const requestBody = {
      PhoneNumber: phoneNumber,        // Capital P to match backend
      VerificationCode: verificationCode,  // Capital V and C to match backend
      ExpirationMinutes: expirationMinutes, // Capital E and M to match backend
      IPAddress: ipAddress,
      UserAgent: userAgent
    };

    console.log('📤 Calling backend API:', backendUrl);
    console.log('📤 Request body:', JSON.stringify(requestBody, null, 2));

    const requestBodyString = JSON.stringify(requestBody);
    const apiResponse = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': requestBodyString.length.toString(),
        'User-Agent': 'NextJS-Frontend/1.0',
        'Accept': 'application/json',
      },
      body: requestBodyString
    });

    console.log('📥 Backend response status:', apiResponse.status);
    console.log('📥 Backend response headers:', Object.fromEntries(apiResponse.headers.entries()));

    if (!apiResponse.ok) {
      const errorText = await apiResponse.text();
      console.log('❌ Backend error response:', errorText);
      console.log('❌ Backend error status:', apiResponse.status);
      console.log('❌ Backend error headers:', Object.fromEntries(apiResponse.headers.entries()));

      // Try to parse error as JSON for more details
      try {
        const errorJson = JSON.parse(errorText);
        console.log('❌ Backend error JSON:', errorJson);
        throw new Error(`Backend API failed: ${apiResponse.status} - ${errorJson.error || errorJson.message || errorText}`);
      } catch {
        throw new Error(`Backend API failed: ${apiResponse.status} - ${errorText}`);
      }
    }

    const result = await apiResponse.json();
    console.log('📥 Backend response data:', result);

    return NextResponse.json({
      success: true,
      message: 'Verification code stored successfully'
    });

  } catch (error) {
    console.error('❌ Error storing verification code:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    // Add more detailed error info for production debugging
    console.error('❌ Full error details:', {
      message: errorMessage,
      stack: error instanceof Error ? error.stack : 'No stack trace',
      backendUrl: `${process.env.ADMIN_BASE_URL || process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'https://admin.codemedicalapps.com'}/api/v1/verification/store`,
      envVars: {
        ADMIN_BASE_URL: process.env.ADMIN_BASE_URL ? 'SET' : 'NOT SET',
        NEXT_PUBLIC_ADMIN_BASE_URL: process.env.NEXT_PUBLIC_ADMIN_BASE_URL ? 'SET' : 'NOT SET'
      }
    });

    return NextResponse.json(
      {
        success: false,
        error: `Failed to store verification code: ${errorMessage}`,
        debug: process.env.NODE_ENV === 'development' ? {
          backendUrl: `${process.env.ADMIN_BASE_URL || process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'https://admin.codemedicalapps.com'}/api/v1/verification/store`,
          errorMessage
        } : undefined
      },
      { status: 500 }
    );
  }
}