'use client';

import { useState } from 'react';
import { useContactInfo } from '@/contexts/contact-info';
import { WhatsAppIcon } from './whatsapp-icon';
import { ColorPicker } from './color-picker';
import { Palette, X } from 'lucide-react';

export function WhatsAppButton() {
  const whatsappLink = 'https://wa.me/+9647836071686';

  const openWhatsApp = () => {
    window.open(whatsappLink, '_blank');
  };

  return (
    <button
      className="fixed bottom-20 right-4 md:bottom-6 md:right-6 z-50 flex h-12 w-12 md:h-14 md:w-14 items-center justify-center rounded-full bg-[#25D366] text-white shadow-lg transition-all duration-300 hover:bg-[#128C7E] hover:scale-110 active:scale-95"
      onClick={openWhatsApp}
      aria-label="Chat on WhatsApp"
    >
      <WhatsAppIcon className="h-6 w-6 md:h-7 md:w-7" />
    </button>
  );
}