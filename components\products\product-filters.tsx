'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { MakeApiCallAsync } from '@/lib/api-helper';
import { useSettings } from '@/contexts/settings-context';

interface Category {
  CategoryID: number;
  Name: string;
  ParentCategoryID?: number;
  ProductCount?: number;
}

interface Brand {
  ManufacturerID: number;
  Name: string;
  ProductCount?: number;
}

interface Tag {
  TagID: number;
  Name: string;
  ProductCount?: number;
}



interface ProductFiltersProps {
  onFilterChange: (filterType: string, value: any) => void;
  selectedCategories: number[];
  selectedBrands: number[];
  selectedTags: number[];
  priceRange: [number, number] | null;
  rating: number | null;
}

export function ProductFilters({
  onFilterChange,
  selectedCategories,
  selectedBrands,
  selectedTags,
  priceRange,
  rating
}: ProductFiltersProps) {
  const { t } = useSettings();
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [minMaxPrice, setMinMaxPrice] = useState<[number, number]>([0, 1000]);
  const [currentPriceRange, setCurrentPriceRange] = useState<[number, number]>([0, 1000]);

  useEffect(() => {
    // Set default min-max price range
    setMinMaxPrice([0, 1000]);
    setCurrentPriceRange([0, 1000]);

    // Fetch filter data from API
    const fetchFilterData = async () => {
      try {
        // Fetch categories
        const categoriesResponse = await MakeApiCallAsync(
          'api/v1/dynamic/dataoperation/get-categories-list',
          null,
          {
            requestParameters: {
              PageNo: 1,
              PageSize: 100
            }
          },
          {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          "POST",
          true
        );

        if (categoriesResponse?.data?.data) {
          try {
            const parsedCategories = JSON.parse(categoriesResponse.data.data);
            console.log('Categories from API:', parsedCategories);
            setCategories(parsedCategories);
          } catch (error) {
            console.error('Error parsing categories data:', error);
            setCategories([]);
          }
        }

        // Fetch brands/manufacturers
        const brandsResponse = await MakeApiCallAsync(
          'api/v1/dynamic/dataoperation/get-manufacturers-list',
          null,
          {
            requestParameters: {
              PageNo: 1,
              PageSize: 100
            }
          },
          {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          "POST",
          true
        );

        if (brandsResponse?.data?.data) {
          try {
            const parsedBrands = JSON.parse(brandsResponse.data.data);
            console.log('Brands from API:', parsedBrands);
            setBrands(parsedBrands);
          } catch (error) {
            console.error('Error parsing brands data:', error);
            setBrands([]);
          }
        }

        // Fetch tags
        const tagsResponse = await MakeApiCallAsync(
          'api/v1/dynamic/dataoperation/get-tags-list',
          null,
          {
            requestParameters: {
              PageNo: 1,
              PageSize: 100
            }
          },
          {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          "POST",
          true
        );

        if (tagsResponse?.data?.data) {
          try {
            const parsedTags = JSON.parse(tagsResponse.data.data);
            console.log('Tags from API:', parsedTags);
            setTags(parsedTags);
          } catch (error) {
            console.error('Error parsing tags data:', error);
            setTags([]);
          }
        }
      } catch (error) {
        console.error('Error fetching filter data:', error);
        // Set empty arrays if API calls fail
        setCategories([]);
        setBrands([]);
        setTags([]);
      }
    };

    fetchFilterData();
  }, []);

  const handleCategoryChange = (categoryId: number) => {
    onFilterChange('category', categoryId);
  };

  const handleBrandChange = (brandId: number) => {
    onFilterChange('brand', brandId);
  };

  const handleTagChange = (tagId: number) => {
    onFilterChange('tag', tagId);
  };



  const handlePriceChange = (values: number[]) => {
    setCurrentPriceRange([values[0], values[1]]);
  };

  const handlePriceChangeEnd = () => {
    onFilterChange('price', `${currentPriceRange[0]}-${currentPriceRange[1]}`);
  };

  const handleRatingChange = (rating: number) => {
    onFilterChange('rating', rating);
  };

  return (
    <div className="space-y-6">
      <Card className="p-4">
        <Accordion type="multiple" defaultValue={['categories', 'price']}>
          {/* Categories Filter */}
          <AccordionItem value="categories">
            <AccordionTrigger className="text-lg font-semibold">Categories</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2 mt-2">
                {categories.map((category) => (
                  <div key={category.CategoryID} className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${category.CategoryID}`}
                      checked={selectedCategories.includes(category.CategoryID)}
                      onCheckedChange={() => handleCategoryChange(category.CategoryID)}
                    />
                    <Label
                      htmlFor={`category-${category.CategoryID}`}
                      className="text-sm cursor-pointer"
                    >
                      {category.Name} {category.ProductCount && `(${category.ProductCount})`}
                    </Label>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Price Range Filter */}
          <AccordionItem value="price">
            <AccordionTrigger className="text-lg font-semibold">Price Range</AccordionTrigger>
            <AccordionContent>
              <div className="px-2 py-4">
                <Slider
                  defaultValue={[minMaxPrice[0], minMaxPrice[1]]}
                  value={[currentPriceRange[0], currentPriceRange[1]]}
                  max={minMaxPrice[1]}
                  step={1}
                  onValueChange={handlePriceChange}
                  onValueCommit={handlePriceChangeEnd}
                  className="mb-6"
                />
                <div className="flex justify-between text-sm">
                  <span>${currentPriceRange[0]}</span>
                  <span>${currentPriceRange[1]}</span>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Brands Filter */}
          <AccordionItem value="brands">
            <AccordionTrigger className="text-lg font-semibold">Brands</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2 mt-2">
                {brands.map((brand) => (
                  <div key={brand.ManufacturerID} className="flex items-center space-x-2">
                    <Checkbox
                      id={`brand-${brand.ManufacturerID}`}
                      checked={selectedBrands.includes(brand.ManufacturerID)}
                      onCheckedChange={() => handleBrandChange(brand.ManufacturerID)}
                    />
                    <Label
                      htmlFor={`brand-${brand.ManufacturerID}`}
                      className="text-sm cursor-pointer"
                    >
                      {brand.Name} {brand.ProductCount && `(${brand.ProductCount})`}
                    </Label>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>



          {/* Tags Filter */}
          <AccordionItem value="tags">
            <AccordionTrigger className="text-lg font-semibold">Tags</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2 mt-2">
                {tags.map((tag) => (
                  <div key={tag.TagID} className="flex items-center space-x-2">
                    <Checkbox
                      id={`tag-${tag.TagID}`}
                      checked={selectedTags.includes(tag.TagID)}
                      onCheckedChange={() => handleTagChange(tag.TagID)}
                    />
                    <Label
                      htmlFor={`tag-${tag.TagID}`}
                      className="text-sm cursor-pointer"
                    >
                      {tag.Name} {tag.ProductCount && `(${tag.ProductCount})`}
                    </Label>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </Card>
    </div>
  );
}
