'use client';

import { useState, useEffect, useCallback } from 'react';

export interface ColorTheme {
  primary: string;
  primaryForeground: string;
  name: string;
}

export const DEFAULT_THEMES: ColorTheme[] = [
  {
    primary: '#0074b2',
    primaryForeground: '#ffffff',
    name: 'Default Blue'
  },
  {
    primary: '#dc2626',
    primaryForeground: '#ffffff',
    name: 'Red'
  },
  {
    primary: '#16a34a',
    primaryForeground: '#ffffff',
    name: 'Green'
  },
  {
    primary: '#ca8a04',
    primaryForeground: '#ffffff',
    name: 'Yellow'
  },
  {
    primary: '#9333ea',
    primaryForeground: '#ffffff',
    name: '<PERSON>'
  },
  {
    primary: '#ea580c',
    primaryForeground: '#ffffff',
    name: 'Orange'
  },
  {
    primary: '#0891b2',
    primaryForeground: '#ffffff',
    name: '<PERSON><PERSON>'
  },
  {
    primary: '#be185d',
    primaryForeground: '#ffffff',
    name: 'Pink'
  }
];

const COLOR_THEME_COOKIE = 'color_theme';
const COOKIE_EXPIRY_DAYS = 365;

export function useColorTheme() {
  const [currentTheme, setCurrentTheme] = useState<ColorTheme>(DEFAULT_THEMES[0]);
  const [isLoading, setIsLoading] = useState(true);

  // Get cookie value
  const getCookie = useCallback((name: string): string | null => {
    if (typeof document === 'undefined') return null;
    
    const nameEQ = name + '=';
    const cookies = document.cookie.split(';');
    
    for (let cookie of cookies) {
      let c = cookie.trim();
      if (c.indexOf(nameEQ) === 0) {
        return decodeURIComponent(c.substring(nameEQ.length));
      }
    }
    return null;
  }, []);

  // Set cookie value
  const setCookie = useCallback((name: string, value: string, days: number = COOKIE_EXPIRY_DAYS): void => {
    if (typeof document === 'undefined') return;
    
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    
    let cookieString = `${name}=${encodeURIComponent(value)}; expires=${expires.toUTCString()}; path=/; SameSite=strict`;
    
    if (process.env.NODE_ENV === 'production') {
      cookieString += '; Secure';
    }
    
    document.cookie = cookieString;
    
    // Debug logging
    console.log('🍪 Color theme cookie set:', {
      name,
      value,
      cookieString,
      allCookies: document.cookie
    });
    
    // Verify cookie was set
     setTimeout(() => {
       const verification = getCookie(name);
       if (verification) {
         console.log('✅ Cookie verification successful:', verification);
       } else {
         console.warn('❌ Cookie verification failed - cookie not found after setting');
       }
     }, 100);
   }, [getCookie]);

  // Apply theme to CSS variables
  const applyTheme = useCallback((theme: ColorTheme) => {
    if (typeof document === 'undefined') return;
    
    const root = document.documentElement;
    
    // Convert hex to HSL for CSS variables
    const hexToHsl = (hex: string) => {
      const r = parseInt(hex.slice(1, 3), 16) / 255;
      const g = parseInt(hex.slice(3, 5), 16) / 255;
      const b = parseInt(hex.slice(5, 7), 16) / 255;

      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      let h = 0, s = 0, l = (max + min) / 2;

      if (max !== min) {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        
        switch (max) {
          case r: h = (g - b) / d + (g < b ? 6 : 0); break;
          case g: h = (b - r) / d + 2; break;
          case b: h = (r - g) / d + 4; break;
        }
        h /= 6;
      }

      return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(l * 100)}%`;
    };

    const primaryHsl = hexToHsl(theme.primary);
    const primaryForegroundHsl = hexToHsl(theme.primaryForeground);
    
    root.style.setProperty('--primary', primaryHsl);
    root.style.setProperty('--primary-foreground', primaryForegroundHsl);
    
    // Update meta theme-color
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', theme.primary);
    }
  }, []);

  // Load theme from cookie on mount
  useEffect(() => {
    const savedTheme = getCookie(COLOR_THEME_COOKIE);
    
    if (savedTheme) {
      try {
        const parsedTheme = JSON.parse(savedTheme) as ColorTheme;
        setCurrentTheme(parsedTheme);
        applyTheme(parsedTheme);
      } catch (error) {
        console.warn('Failed to parse saved color theme:', error);
        applyTheme(DEFAULT_THEMES[0]);
      }
    } else {
      applyTheme(DEFAULT_THEMES[0]);
    }
    
    setIsLoading(false);
  }, [getCookie, applyTheme]);

  // Change theme function
  const changeTheme = useCallback((theme: ColorTheme) => {
    console.log('🎨 Changing theme to:', theme);
    setCurrentTheme(theme);
    applyTheme(theme);
    setCookie(COLOR_THEME_COOKIE, JSON.stringify(theme));
  }, [applyTheme, setCookie]);

  // Create custom theme from hex color
  const createCustomTheme = useCallback((primaryColor: string, name: string = 'Custom') => {
    console.log('🎨 Creating custom theme:', { primaryColor, name });
    const customTheme: ColorTheme = {
      primary: primaryColor,
      primaryForeground: '#ffffff',
      name
    };
    
    changeTheme(customTheme);
    return customTheme;
  }, [changeTheme]);

  return {
    currentTheme,
    availableThemes: DEFAULT_THEMES,
    changeTheme,
    createCustomTheme,
    isLoading
  };
}