/**
 * Utility functions for generating canonical URLs and meta tags
 */

const BASE_URL = 'https://www.codemedicalapps.com';

/**
 * Generate canonical URL for a given path
 * @param path - The path relative to the base URL
 * @returns Complete canonical URL
 */
export function generateCanonicalUrl(path: string): string {
  // Remove leading slash if present
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  
  // Remove trailing slash except for root
  const normalizedPath = cleanPath === '' ? '' : cleanPath.replace(/\/$/, '');
  
  return `${BASE_URL}${normalizedPath ? '/' + normalizedPath : ''}`;
}

/**
 * Generate canonical URL for product pages
 * @param productId - Product ID
 * @param productName - Product name (optional, for SEO-friendly URLs)
 * @returns Canonical URL for product
 */
export function generateProductCanonicalUrl(productId: string, productName?: string): string {
  if (productName) {
    const slug = productName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim();
    return generateCanonicalUrl(`product/${productId}/${slug}`);
  }
  return generateCanonicalUrl(`product/${productId}`);
}

/**
 * Generate canonical URL for campaign pages
 * @param campaignId - Campaign ID
 * @param campaignTitle - Campaign title (optional, for SEO-friendly URLs)
 * @returns Canonical URL for campaign
 */
export function generateCampaignCanonicalUrl(campaignId: string, campaignTitle?: string): string {
  if (campaignTitle) {
    const slug = campaignTitle
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim();
    return generateCanonicalUrl(`campaign/${campaignId}/${slug}`);
  }
  return generateCanonicalUrl(`campaign/${campaignId}`);
}

/**
 * Generate canonical URL for category pages
 * @param categoryId - Category ID
 * @param categoryName - Category name (optional, for SEO-friendly URLs)
 * @returns Canonical URL for category
 */
export function generateCategoryCanonicalUrl(categoryId: string, categoryName?: string): string {
  if (categoryName) {
    const slug = categoryName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim();
    return generateCanonicalUrl(`category/${categoryId}/${slug}`);
  }
  return generateCanonicalUrl(`category/${categoryId}`);
}

/**
 * Generate meta tags object with canonical URL
 * @param canonicalUrl - The canonical URL
 * @param title - Page title
 * @param description - Page description
 * @param additionalMeta - Additional meta properties
 * @returns Meta tags object for Next.js metadata
 */
export function generateMetaWithCanonical(
  canonicalUrl: string,
  title: string,
  description: string,
  additionalMeta: Record<string, any> = {}
) {
  return {
    title,
    description,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title,
      description,
      url: canonicalUrl,
      siteName: 'Code Medical Apps',
      type: 'website',
      ...additionalMeta.openGraph,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      ...additionalMeta.twitter,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
      },
    },
    ...additionalMeta,
  };
}

/**
 * Clean and normalize URL path
 * @param path - URL path to clean
 * @returns Cleaned path
 */
export function cleanUrlPath(path: string): string {
  return path
    .replace(/\/+/g, '/') // Replace multiple slashes with single
    .replace(/\/$/, '') // Remove trailing slash
    .toLowerCase();
}

/**
 * Check if URL should be indexed
 * @param path - URL path
 * @returns Boolean indicating if URL should be indexed
 */
export function shouldIndexUrl(path: string): boolean {
  const noIndexPaths = [
    '/api/',
    '/debug',
    '/test',
    '/_next/',
    '/admin/',
    '/private/',
  ];
  
  return !noIndexPaths.some(noIndexPath => path.startsWith(noIndexPath));
}