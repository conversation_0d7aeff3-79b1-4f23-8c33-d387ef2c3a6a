'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Package } from 'lucide-react';

interface Product {
  ProductId: number;
  ProductName: string;
  ShortDescription?: string;
  Price: number;
  DiscountPrice?: number;
  StockQuantity: number;
  CategoryName?: string;
  ProductImagesJson?: any[];
}

export default function CategoryPage() {
  const params = useParams() as { id: string } | null;
  const categoryId = params?.id as string;
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [categoryName, setCategoryName] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCategoryProducts();
  }, [categoryId]);

  const fetchCategoryProducts = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // This would typically fetch products by category from your API
      // For now, we'll show a placeholder message
      setProducts([]);
      setCategoryName('Category');
    } catch (error) {
      console.error('Error fetching category products:', error);
      setError('Failed to load category products');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg text-muted-foreground">Loading category...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/products">Products</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{categoryName || `Category ${categoryId}`}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="max-w-6xl mx-auto">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="outline" size="sm" asChild>
            <Link href="/products">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">{categoryName || `Category ${categoryId}`}</h1>
        </div>

        {error ? (
          <Card className="p-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="h-8 w-8 text-red-500" />
              </div>
              <h3 className="text-lg font-medium mb-2">Error Loading Category</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={fetchCategoryProducts}>
                Try Again
              </Button>
            </div>
          </Card>
        ) : products.length === 0 ? (
          <Card className="p-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">Category Products Coming Soon</h3>
              <p className="text-muted-foreground mb-4">
                This category page is under development. Products will be displayed here once the category API is implemented.
              </p>
              <Button asChild>
                <Link href="/products">
                  View All Products
                </Link>
              </Button>
            </div>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <Card key={product.ProductId} className="overflow-hidden">
                <div className="aspect-square bg-muted flex items-center justify-center">
                  <Package className="h-12 w-12 text-muted-foreground" />
                </div>
                <div className="p-4">
                  <h3 className="font-medium mb-2">{product.ProductName}</h3>
                  <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                    {product.ShortDescription}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-bold text-primary">
                        ${(product.DiscountPrice || product.Price).toFixed(2)}
                      </span>
                      {product.DiscountPrice && (
                        <span className="text-sm text-muted-foreground line-through">
                          ${product.Price.toFixed(2)}
                        </span>
                      )}
                    </div>
                    <Button size="sm" asChild>
                      <Link href={`/product/${product.ProductId}`}>
                        View
                      </Link>
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}