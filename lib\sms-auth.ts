// SMS Authentication utility functions to replace Firebase Auth

interface SMSAuthResponse {
  success: boolean;
  message: string;
  messageId?: string;
  error?: string;
}

interface VerificationResponse {
  success: boolean;
  message: string;
  error?: string;
}

/**
 * Send SMS verification code to phone number
 */
export async function sendSMSVerification(phoneNumber: string): Promise<SMSAuthResponse> {
  try {
    const response = await fetch('/api/sms/send-verification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phoneNumber }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.error || 'Failed to send verification code',
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message,
      messageId: data.messageId,
    };
  } catch (error) {
    console.error('Error sending SMS verification:', error);
    return {
      success: false,
      message: 'Network error occurred',
      error: 'Network error',
    };
  }
}

/**
 * Verify SMS code
 */
export async function verifySMSCode(phoneNumber: string, code: string): Promise<VerificationResponse> {
  try {
    const response = await fetch('/api/sms/verify-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phoneNumber, code }),
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: data.error || 'Verification failed',
        error: data.error,
      };
    }

    return {
      success: true,
      message: data.message,
    };
  } catch (error) {
    console.error('Error verifying SMS code:', error);
    return {
      success: false,
      message: 'Network error occurred',
      error: 'Network error',
    };
  }
}

/**
 * Format phone number for international format
 */
export function formatPhoneNumber(phoneNumber: string, countryCode: string = '+1'): string {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // If it doesn't start with country code, add it
  if (!cleaned.startsWith(countryCode.replace('+', ''))) {
    return `${countryCode}${cleaned}`;
  }
  
  return `+${cleaned}`;
}

/**
 * Validate phone number format
 */
export function validatePhoneNumber(phoneNumber: string): boolean {
  // Basic validation for international phone numbers
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(phoneNumber);
}