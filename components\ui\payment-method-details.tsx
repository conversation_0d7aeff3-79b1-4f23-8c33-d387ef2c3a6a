"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "./button";
import { Card } from "./card";
import {
  Copy,
  Check,
  ExternalLink,
  CreditCard,
  Phone,
  Building,
  Gift,
  Truck,
} from "lucide-react";
import { toast } from "sonner";
import { useColorThemeContext } from "@/contexts/color-theme-context";

interface PaymentMethodDetailsProps {
  paymentMethodName: string;
  paymentMethodId: number;
}

interface PaymentMethodData {
  id: number;
  name: string;
  image: string;
  type: string;
  instructions: string;
  location: string;
  number?: string;
  link?: string;
  email?: string;
  accountNumber?: string;
  bankName?: string;
}

// Enhanced payment methods data with more comprehensive information
const paymentMethodsData: Record<string, PaymentMethodData> = {
  "Zain cash (Iraq)": {
    id: 1,
    name: "Zain cash (Iraq)",
    image: "/Zaincash iraq.png",
    number: "***********",
    type: "phone",
    instructions:
      "Send payment to the Zain Cash number below and keep the transaction receipt for your records.",
    location: "Inside Iraq",
  },
  "Zain Cash": {
    id: 1,
    name: "Zain Cash",
    image: "/Zaincash iraq.png",
    number: "***********",
    type: "phone",
    instructions:
      "Send payment to the Zain Cash number below and keep the transaction receipt for your records.",
    location: "Inside Iraq",
  },
  "Rafidain Bank": {
    id: 2,
    name: "Rafidain Bank",
    image: "/Qicard iraq.png",
    number: "**********",
    accountNumber: "**********",
    bankName: "Rafidain Bank",
    type: "account",
    instructions:
      "Transfer the payment amount to the Rafidain Bank account number below.",
    location: "Inside Iraq",
  },
  "Asia Pay": {
    id: 3,
    name: "Asia Pay",
    image: "/Asia pay.png",
    number: "***********",
    type: "phone",
    instructions:
      "Send payment to the Asia Pay number below and save the transaction confirmation.",
    location: "Inside Iraq",
  },
  PayPal: {
    id: 4,
    name: "PayPal",
    image: "/Paypal.png",
    link: "https://paypal.me/FatimahNaser?country.x=JO&locale.x=en_US",
    type: "link",
    instructions:
      "Click the PayPal button below to complete your payment securely through PayPal.",
    location: "Outside Iraq",
  },
  "Amazon Gift": {
    id: 5,
    name: "Amazon Gift",
    image: "/Amazon gift card.png",
    link: "https://www.amazon.com/Amazon-eGift-Card-Logo-Animated/dp/B07PCMWTSG/ref=mp_s_a_1_1?adgrpid=************&dib=eyJ2IjoiMSJ9.y343JC2nqCCCtAt_MaFdYdSEoDk1IL8C8OVn3MADfESEozTH6jWzFIJ4WqlXn7_W-n2IrnPR-rfE3Spk4QYVuOOL7cvbuK9Esy0CXQivH6v0c4KW6RfZeH8pYn15Gdj-s62p0V-fiHzAE12D4YOgeY2zQf3sUuAQF30eHiR7nSfSyvGj9P0M79Suz3VRAqqxS64beG-r2SJhB_Y_apq-6Q.gbfTjpxr2hWpO9dWg-U8dthgvZM21cwxR6PrsZBpG38&dib_tag=se&hvadid=692707382867&hvdev=m&hvlocphy=9211521&hvnetw=g&hvqmt=e&hvrand=15003258399388157606&hvtargid=kwd-2389411675177&hydadcr=22339_13333066&keywords=amazon%27+gift+card&mcid=90ecf431d9b83733b420d0f87065fc78&qid=1748814128&sr=8-1",
    email: "<EMAIL>",
    type: "gift_card",
    instructions:
      "Purchase an Amazon eGift Card using the link below and send it to the specified email address.",
    location: "Outside Iraq",
  },
  "Amazon Gift Card": {
    id: 5,
    name: "Amazon Gift Card",
    image: "/Amazon gift card.png",
    link: "https://www.amazon.com/Amazon-eGift-Card-Logo-Animated/dp/B07PCMWTSG/ref=mp_s_a_1_1?adgrpid=************&dib=eyJ2IjoiMSJ9.y343JC2nqCCCtAt_MaFdYdSEoDk1IL8C8OVn3MADfESEozTH6jWzFIJ4WqlXn7_W-n2IrnPR-rfE3Spk4QYVuOOL7cvbuK9Esy0CXQivH6v0c4KW6RfZeH8pYn15Gdj-s62p0V-fiHzAE12D4YOgeY2zQf3sUuAQF30eHiR7nSfSyvGj9P0M79Suz3VRAqqxS64beG-r2SJhB_Y_apq-6Q.gbfTjpxr2hWpO9dWg-U8dthgvZM21cwxR6PrsZBpG38&dib_tag=se&hvadid=692707382867&hvdev=m&hvlocphy=9211521&hvnetw=g&hvqmt=e&hvrand=15003258399388157606&hvtargid=kwd-2389411675177&hydadcr=22339_13333066&keywords=amazon%27+gift+card&mcid=90ecf431d9b83733b420d0f87065fc78&qid=1748814128&sr=8-1",
    email: "<EMAIL>",
    type: "gift_card",
    instructions:
      "Purchase an Amazon eGift Card using the link below and send it to the specified email address.",
    location: "Outside Iraq",
  },
  "Cash on delivery": {
    id: 6,
    name: "Cash on delivery",
    image: "/Cash on delivery.png",
    type: "cash",
    instructions:
      "Pay in cash when your order is delivered. Available for all provinces within Iraq.",
    location: "Inside Iraq",
  },
  "Cash on Delivery": {
    id: 6,
    name: "Cash on Delivery",
    image: "/Cash on delivery.png",
    type: "cash",
    instructions:
      "Pay in cash when your order is delivered. Available for all provinces within Iraq.",
    location: "Inside Iraq",
  },
};

export function PaymentMethodDetails({
  paymentMethodName,
  paymentMethodId,
}: PaymentMethodDetailsProps) {
  const [copiedItems, setCopiedItems] = useState<Record<string, boolean>>({});

  // Try to find payment data by exact name match or partial match
  const paymentData =
    paymentMethodsData[paymentMethodName] ||
    Object.values(paymentMethodsData).find(
      (method) =>
        method.name.toLowerCase().includes(paymentMethodName.toLowerCase()) ||
        paymentMethodName.toLowerCase().includes(method.name.toLowerCase())
    );

  if (!paymentData) {
    return (
      <Card className="mt-4 p-4 bg-gray-50 border border-gray-200">
        <div className="text-center py-4">
          <CreditCard className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600">
            Payment method details for "{paymentMethodName}" are not available.
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Please contact support for payment instructions.
          </p>
        </div>
      </Card>
    );
  }

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItems((prev) => ({ ...prev, [label]: true }));
      toast.success(`${label} copied to clipboard!`, {
        duration: 2000,
        style: {
          background: "#10B981",
          color: "white",
          border: "none",
        },
      });

      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setCopiedItems((prev) => ({ ...prev, [label]: false }));
      }, 2000);
    } catch (error) {
      // Fallback for browsers that don't support clipboard API
      try {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);

        setCopiedItems((prev) => ({ ...prev, [label]: true }));
        toast.success(`${label} copied to clipboard!`);

        setTimeout(() => {
          setCopiedItems((prev) => ({ ...prev, [label]: false }));
        }, 2000);
      } catch (fallbackError) {
        toast.error("Failed to copy to clipboard. Please copy manually.");
      }
    }
  };

  const openLink = (url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
  };

  const getPaymentIcon = (type: string) => {
    switch (type) {
      case "phone":
        return <Phone className="h-4 w-4" />;
      case "account":
        return <Building className="h-4 w-4" />;
      case "gift_card":
        return <Gift className="h-4 w-4" />;
      case "cash":
        return <Truck className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;

  return (
    <Card className="mt-4 p-6 shadow-lg" style={{ background: `linear-gradient(to bottom right, ${primaryColor}10, ${primaryColor}05, ${primaryColor}08)`, border: `1px solid ${primaryColor}40` }}>
      <div className="flex items-start gap-6">
        {/* Payment Method Icon */}
        <div className="flex-shrink-0" style={{ display: "none" }}>
          <div className="w-20 h-20 rounded-xl bg-white border border-gray-200 shadow-sm flex items-center justify-center">
            {getPaymentIcon(paymentData.type)}
          </div>
        </div>

        {/* Payment Details */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-3">
              <h4 className="font-bold text-xl" style={{ color: primaryColor }}>
                {paymentData.name}
              </h4>
              {getPaymentIcon(paymentData.type)}
            </div>
            <span className="text-xs px-3 py-1 rounded-full font-medium" style={{ backgroundColor: `${primaryColor}20`, color: primaryColor }}>
              {paymentData.location}
            </span>
          </div>

          <p className="text-sm mb-6 leading-relaxed" style={{ color: primaryColor }}>
            {paymentData.instructions}
          </p>

          {/* Payment Information */}
          <div className="space-y-4">
            {/* Phone Number or Account Number */}
            {paymentData.number && (
              <div className="group">
                <div className="flex items-center gap-3 p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200" style={{ border: `1px solid ${primaryColor}40` }}>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      {paymentData.type === "phone" ? (
                        <Phone className="h-4 w-4" style={{ color: primaryColor }} />
                      ) : (
                        <Building className="h-4 w-4" style={{ color: primaryColor }} />
                      )}
                      <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">
                        {paymentData.type === "phone"
                          ? "Phone Number"
                          : paymentData.type === "account"
                          ? "Account Number"
                          : "Number"}
                      </p>
                    </div>
                    <p className="font-mono text-xl font-bold text-gray-900 tracking-wider">
                      {paymentData.number}
                    </p>
                    {paymentData.bankName && (
                      <p className="text-sm text-gray-600 mt-1">
                        Bank: {paymentData.bankName}
                      </p>
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      copyToClipboard(
                        paymentData.number!,
                        paymentData.type === "phone"
                          ? "Phone Number"
                          : "Account Number"
                      );
                    }}
                    className="flex items-center gap-2 transition-colors hover:bg-opacity-10" style={{ backgroundColor: `${primaryColor}10` }}
                  >
                    {copiedItems[
                      paymentData.type === "phone"
                        ? "Phone Number"
                        : "Account Number"
                    ] ? (
                      <>
                        <Check className="h-4 w-4 text-green-600" />
                        <span className="text-green-600 font-medium">
                          Copied!
                        </span>
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4" />
                        <span>Copy</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}

            {/* Email Address */}
            {paymentData.email && (
              <div className="group">
                <div className="flex items-center gap-3 p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200" style={{ border: `1px solid ${primaryColor}40` }}>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <svg
                        className="h-4 w-4"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        style={{ color: primaryColor }}
                      >
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                      </svg>
                      <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">
                        Email Address
                      </p>
                    </div>
                    <p className="font-mono text-lg font-semibold text-gray-900 break-all">
                      {paymentData.email}
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      copyToClipboard(paymentData.email!, "Email Address");
                    }}
                    className="flex items-center gap-2 transition-colors hover:bg-opacity-10" style={{ backgroundColor: `${primaryColor}10` }}
                  >
                    {copiedItems["Email Address"] ? (
                      <>
                        <Check className="h-4 w-4 text-green-600" />
                        <span className="text-green-600 font-medium">
                          Copied!
                        </span>
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4" />
                        <span>Copy</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}

            {/* Payment Link/Button */}
            {paymentData.link && (
              <div className="space-y-3">
                <Button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    openLink(paymentData.link!);
                  }}
                  className="w-full text-white flex items-center justify-center gap-3 py-3 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200" style={{ background: `linear-gradient(to right, ${primaryColor}, ${primaryColor}dd)` }}
                >
                  <ExternalLink className="h-5 w-5" />
                  {paymentData.name.includes("PayPal")
                    ? "Pay with PayPal"
                    : paymentData.name.includes("Amazon")
                    ? "Buy Amazon Gift Card"
                    : "Open Payment Link"}
                </Button>

                {/* Copy Link Option */}
                <div className="bg-white rounded-xl border border-gray-200 p-3">
                  <p className="text-xs font-medium text-gray-600 mb-2 uppercase tracking-wide">
                    Payment Link
                  </p>
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={paymentData.link}
                      readOnly
                      className="flex-1 px-3 py-2 text-xs bg-gray-50 border border-gray-200 rounded-lg font-mono text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        copyToClipboard(paymentData.link!, "Payment Link");
                      }}
                      className="flex items-center gap-1 transition-colors hover:bg-opacity-10" style={{ backgroundColor: `${primaryColor}10` }}
                    >
                      {copiedItems["Payment Link"] ? (
                        <>
                          <Check className="h-3 w-3 text-green-600" />
                          <span className="text-green-600 text-xs font-medium">
                            Copied!
                          </span>
                        </>
                      ) : (
                        <>
                          <Copy className="h-3 w-3" />
                          <span className="text-xs">Copy</span>
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Cash on Delivery Info */}
            {paymentData.type === "cash" && (
              <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <Truck className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-green-700 mb-1">
                      ✅ No advance payment required
                    </p>
                    <p className="text-sm text-green-700">
                      Pay in cash when your order is delivered to your doorstep.
                    </p>
                    <p className="text-xs text-green-600 mt-2">
                      📍 Available for all provinces within Iraq. Additional
                      delivery fees may apply based on your location.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Additional Instructions */}
          <div className="mt-6 p-4 bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-xl">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                <svg
                  className="h-4 w-4 text-amber-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <p className="text-sm font-semibold text-amber-700 mb-1">
                  Important Notice
                </p>
                <p className="text-sm text-amber-700">
                  After completing your payment, we will contact you within 24
                  hours to confirm your order and arrange delivery details.
                </p>
                <p className="text-xs text-amber-600 mt-1">
                  Please keep your payment receipt/confirmation for reference.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
