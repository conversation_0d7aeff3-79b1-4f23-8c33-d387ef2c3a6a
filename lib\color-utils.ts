/**
 * Utility functions for color manipulation and contrast calculation
 */

/**
 * Calculate the appropriate text color (black or white) based on background color
 * @param bgColor - Background color in hex format (e.g., '#ff0000')
 * @returns Text color in hex format ('#000000' or '#ffffff')
 */
export function calculateTextColor(bgColor: string): string {
  // Remove # if present
  const hex = bgColor.replace('#', '');
  
  // Convert hex to RGB
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);

  // Calculate relative luminance using WCAG formula
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // Return black for light backgrounds, white for dark backgrounds
  // Using 0.5 as threshold for better contrast
  return luminance > 0.5 ? '#000000' : '#ffffff';
}

/**
 * Calculate contrast ratio between two colors
 * @param color1 - First color in hex format
 * @param color2 - Second color in hex format
 * @returns Contrast ratio (1-21)
 */
export function calculateContrastRatio(color1: string, color2: string): number {
  const getLuminance = (color: string) => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.slice(0, 2), 16) / 255;
    const g = parseInt(hex.slice(2, 4), 16) / 255;
    const b = parseInt(hex.slice(4, 6), 16) / 255;

    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
}

/**
 * Check if a color combination meets WCAG accessibility standards
 * @param bgColor - Background color in hex format
 * @param textColor - Text color in hex format
 * @param level - WCAG level ('AA' or 'AAA')
 * @returns Boolean indicating if the combination meets the standard
 */
export function meetsWCAGStandard(bgColor: string, textColor: string, level: 'AA' | 'AAA' = 'AA'): boolean {
  const contrastRatio = calculateContrastRatio(bgColor, textColor);
  const threshold = level === 'AAA' ? 7 : 4.5;
  return contrastRatio >= threshold;
}

/**
 * Get the best text color for a given background color with WCAG compliance
 * @param bgColor - Background color in hex format
 * @param level - WCAG level ('AA' or 'AAA')
 * @returns Best text color in hex format
 */
export function getBestTextColor(bgColor: string, level: 'AA' | 'AAA' = 'AA'): string {
  const whiteContrast = calculateContrastRatio(bgColor, '#ffffff');
  const blackContrast = calculateContrastRatio(bgColor, '#000000');
  
  const threshold = level === 'AAA' ? 7 : 4.5;
  
  // If both meet the standard, choose the one with higher contrast
  if (whiteContrast >= threshold && blackContrast >= threshold) {
    return whiteContrast > blackContrast ? '#ffffff' : '#000000';
  }
  
  // If only one meets the standard, use that one
  if (whiteContrast >= threshold) return '#ffffff';
  if (blackContrast >= threshold) return '#000000';
  
  // If neither meets the standard, use the one with higher contrast
  return whiteContrast > blackContrast ? '#ffffff' : '#000000';
}

/**
 * Generate a lighter or darker shade of a color
 * @param color - Base color in hex format
 * @param amount - Amount to lighten (positive) or darken (negative) (-100 to 100)
 * @returns Modified color in hex format
 */
export function adjustColorBrightness(color: string, amount: number): string {
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  
  let r = (num >> 16) + amount;
  let g = (num >> 8 & 0x00FF) + amount;
  let b = (num & 0x0000FF) + amount;
  
  r = Math.max(0, Math.min(255, r));
  g = Math.max(0, Math.min(255, g));
  b = Math.max(0, Math.min(255, b));
  
  return '#' + ((r << 16) | (g << 8) | b).toString(16).padStart(6, '0');
}