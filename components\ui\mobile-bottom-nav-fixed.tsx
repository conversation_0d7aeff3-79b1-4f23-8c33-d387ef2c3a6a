'use client';

import { Home, Search, Package, User, ShoppingCart, Heart, Grid3X3, ChevronDown, ChevronUp } from 'lucide-react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { useState, useEffect } from 'react';
import { Button } from './button';
import { MakeApiCallAsync, Config, GetTokenForHeader } from "@/lib/api-helper";

interface Category {
  CategoryID: string | number;
  ParentCategoryID: string | number | null;
  Name: string;
  AttachmentURL?: string;
  // Add other category properties as needed
}

interface NavItem {
  href: string;
  icon: any;
  label: string;
  isActive: boolean;
  onClick: (() => void) | null;
  showBadge: boolean;
  badgeCount?: number;
}

export function MobileBottomNav() {
  const pathname = usePathname();
  const router = useRouter();
  const { totalItems: cartCount } = useCart();
  const { totalItems: wishlistCount } = useWishlist();
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const [mounted, setMounted] = useState(false);
  const [showCategories, setShowCategories] = useState(false);
  const [allCategories, setAllCategories] = useState<Category[]>([]);
  const [parentCategories, setParentCategories] = useState<Category[]>([]);
  const [expandedParents, setExpandedParents] = useState<Record<string | number, boolean>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);

  // Initialize component
  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch categories from API
  const fetchCategories = async () => {
    if (parentCategories.length > 0) {
      setShowCategories(true);
      return;
    }

    setIsLoadingCategories(true);
    try {
      const param = {
        PageNumber: 1,
        PageSize: 100,
        SortColumn: "Name",
        SortOrder: "ASC"
      };
      
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + (await GetTokenForHeader() || '')
      };
      
      const categoriesResponse = await MakeApiCallAsync(
        Config.END_POINT_NAMES.GET_CATEGORIES_LIST, 
        null, 
        param, 
        headers, 
        "POST", 
        true
      );

      if (categoriesResponse?.data?.data) {
        try {
          const parsedData = JSON.parse(categoriesResponse.data.data);
          if (Array.isArray(parsedData)) {
            setAllCategories(parsedData);
            // Get parent categories (those without a ParentCategoryID)
            const parents = parsedData.filter((cat: Category) => !cat.ParentCategoryID);
            setParentCategories(parents);
            setShowCategories(true);
          }
        } catch (parseError) {
          console.error('Error parsing categories data:', parseError);
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setIsLoadingCategories(false);
    }
  };

  const handleCategoryClick = (category: Category) => {
    const childCategories = allCategories.filter(cat => cat.ParentCategoryID === category.CategoryID);
    if (childCategories.length > 0) {
      setExpandedParents(prev => ({
        ...prev,
        [category.CategoryID]: !prev[category.CategoryID]
      }));
    } else {
      router.push(`/products/?category=${category.CategoryID}`);
      setShowCategories(false);
    }
  };

  const handleChildCategoryClick = (category: Category) => {
    router.push(`/products/?category=${category.CategoryID}`);
    setShowCategories(false);
  };

  const renderCategories = () => {
    if (isLoadingCategories) {
      return (
        <div className="p-8 text-center">
          <div 
            className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" 
            style={{ borderColor: primaryColor }}
          />
          <p className="text-gray-500">جاري التحميل...</p>
        </div>
      );
    }

    // Filter categories based on search query
    const filteredParentCategories = searchQuery.trim() === '' 
      ? parentCategories 
      : parentCategories.filter(cat => 
          cat.Name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          allCategories.some(
            child => 
              child.ParentCategoryID === cat.CategoryID && 
              child.Name.toLowerCase().includes(searchQuery.toLowerCase())
          )
        );

    return (
      <div className="h-full flex flex-col">
        {/* Search Bar */}
        <div className="p-3 border-b">
          <div className="relative">
            <input
              type="text"
              placeholder="ابحث عن تصنيف..."
              className="w-full p-2 pr-10 border rounded-lg focus:outline-none focus:ring-2"
              style={{ borderColor: primaryColor, paddingRight: '2.5rem' }}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          </div>
        </div>
        
        {/* Categories List */}
        <div className="flex-1 overflow-y-auto">
          {filteredParentCategories.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              لا توجد تصنيفات مطابقة
            </div>
          ) : (
            <div className="divide-y">
              {filteredParentCategories.map((category) => {
                const hasChildren = allCategories.some(cat => cat.ParentCategoryID === category.CategoryID);
                const isExpanded = expandedParents[category.CategoryID];
                const childCategories = hasChildren 
                  ? allCategories.filter(cat => cat.ParentCategoryID === category.CategoryID).sort((a, b) => a.Name.localeCompare(b.Name))
                  : [];
                
                // Filter child categories based on search query
                const filteredChildCategories = searchQuery.trim() === ''
                  ? childCategories
                  : childCategories.filter(child => 
                      child.Name.toLowerCase().includes(searchQuery.toLowerCase())
                    );

                return (
                  <div key={category.CategoryID} className="border-b">
                    <button
                      onClick={() => handleCategoryClick(category)}
                      className="w-full text-right p-4 hover:bg-gray-50 flex justify-between items-center"
                    >
                      <span className="font-medium flex-1 text-right">{category.Name}</span>
                      {hasChildren && (
                        isExpanded ? (
                          <ChevronUp className="h-4 w-4 text-gray-400 mr-2" />
                        ) : (
                          <ChevronDown className="h-4 w-4 text-gray-400 mr-2" />
                        )
                      )}
                    </button>
                    
                    {hasChildren && isExpanded && (
                      <div className="pl-4 bg-gray-50">
                        <div className="grid grid-cols-2 gap-1 p-2">
                          {filteredChildCategories.map((child) => (
                            <button
                              key={child.CategoryID}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleChildCategoryClick(child);
                              }}
                              className="p-2 text-sm text-right hover:bg-white rounded transition-colors"
                              style={{ color: primaryColor }}
                            >
                              {child.Name}
                            </button>
                          ))}
                        </div>
                        <div className="p-2">
                          <button
                            onClick={() => {
                              router.push(`/products/?category=${category.CategoryID}`);
                              setShowCategories(false);
                            }}
                            className="w-full py-2 text-center text-sm font-medium rounded-md"
                            style={{ 
                              color: 'white',
                              backgroundColor: primaryColor 
                            }}
                          >
                            عرض الكل في {category.Name}
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    );
  };

  if (!mounted) return null;

  // Debug logging for cart and wishlist counts
  console.log('Mobile Nav Debug:', { cartCount, wishlistCount, mounted, primaryColor });
  console.log('Creating badges for cart and wishlist items');
  
  // Ensure cart and wishlist always have values
  const safeCartCount = cartCount || 0;
  const safeWishlistCount = wishlistCount || 0;

  const navItems: NavItem[] = [
    {
      href: '/',
      icon: Home,
      label: t('home') || 'الرئيسية',
      isActive: pathname === '/',
      onClick: null,
      showBadge: false
    },
    {
      href: '#',
      icon: Grid3X3,
      label: t('categories') || 'التصنيفات',
      isActive: false,
      onClick: fetchCategories,
      showBadge: false
    },
    {
      href: '/cart',
      icon: ShoppingCart,
      label: t('cart') || 'سلة التسوق',
      isActive: pathname === '/cart',
      onClick: null,
      showBadge: true,
      badgeCount: safeCartCount  // Use safeCartCount instead of cartCount
    },
    {
      href: '/wishlist',
      icon: Heart,
      label: t('wishlist') || 'المفضلة',
      isActive: pathname === '/wishlist',
      onClick: null,
      showBadge: true,
      badgeCount: safeWishlistCount  // Use safeWishlistCount instead of wishlistCount
    },
    {
      href: '/login',
      icon: User,
      label: t('login') || 'حسابي',
      isActive: pathname === '/login' || pathname === '/signup',
      onClick: null,
      showBadge: false
    }
  ];

  return (
    <>
      {/* Mobile Navigation Bar */}
      <div className="mobile-bottom-nav lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb">
        <div className="flex items-center justify-center py-2 px-4">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isButton = !!item.onClick;
            const className = "mobile-nav-item flex flex-col items-center justify-center min-w-0 py-2 px-3 mx-1" + 
              (isButton ? " bg-transparent border-none" : "");
            
            const content = (
              <>
                <div className="relative" style={{ overflow: 'visible', position: 'relative' }}>
                  <Icon
                    className="mobile-nav-icon h-6 w-6 mb-1"
                    style={{
                      color: item.isActive ? primaryColor : '#6B7280'
                    }}
                  />
                  {/* Simplified badge with improved visibility */}
                  {item.showBadge && (item.badgeCount ?? 0) > 0 && (
                    <span
                      className="mobile-nav-badge absolute -top-2 -right-2 text-white text-xs rounded-full min-w-[20px] h-5 flex items-center justify-center font-medium border-2 border-white z-50"
                      style={{
                        backgroundColor: primaryColor || '#ef4444',
                        display: 'flex',
                        visibility: 'visible',
                        opacity: 1,
                        zIndex: 999
                      }}
                    >
                      {item.badgeCount}
                    </span>
                  )}
                </div>
                <span
                  className="mobile-nav-text text-xs font-medium text-center leading-tight mt-1"
                  style={{
                    color: item.isActive ? primaryColor : '#6B7280'
                  }}
                >
                  {item.label}
                </span>
              </>
            );

            return isButton ? (
              <button
                key={item.href}
                onClick={item.onClick || undefined}
                className={className}
                type="button"
              >
                {content}
              </button>
            ) : (
              <Link
                key={item.href}
                href={item.href}
                className={className}
              >
                {content}
              </Link>
            );
          })}
        </div>
      </div>

      {/* Categories Modal */}
      {showCategories && (
        <div 
          className="md:hidden fixed inset-0 bg-black/50 z-50 flex items-end"
          onClick={() => {
            setShowCategories(false);
            setExpandedParents({});
          }}
        >
          <div 
            className="w-full max-h-[80vh] bg-white rounded-t-xl overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="sticky top-0 bg-white z-10 border-b">
              <div className="flex items-center justify-between p-4">
                <h2 className="text-lg font-semibold" style={{ color: primaryColor }}>
                  {t('categories') || 'التصنيفات'}
                </h2>
                <button
                  onClick={() => {
                    setShowCategories(false);
                    setExpandedParents({});
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>
            </div>
            {renderCategories()}
          </div>
        </div>
      )}
    </>
  );
}
