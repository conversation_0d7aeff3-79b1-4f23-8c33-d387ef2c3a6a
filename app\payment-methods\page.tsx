'use client';

import { Bread<PERSON>rumb, Bread<PERSON>rumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Card } from '@/components/ui/card';
import { DollarSign } from 'lucide-react';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';

export default function PaymentMethodsPage() {
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;

  return (
    <div className="container mx-auto py-8 px-4">
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">{t('home')}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t('paymentMethods')}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl md:text-3xl font-bold mb-6 md:mb-8 text-center">Payment Methods</h1>

        {/* Inside Iraq Section */}
        <section className="mb-12">
          <h2 className="text-xl md:text-2xl font-semibold mb-4 md:mb-6" style={{ color: primaryColor }}>Inside Iraq</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="p-4 md:p-6">
              <div className="flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4">
                <img src="/Zaincash iraq.png" alt="Zain Cash" className="h-16 w-16 flex-shrink-0 mb-2 md:mb-0" />
                <div>
                  <h3 className="text-base md:text-xl font-semibold mb-1 md:mb-2 break-words">Zain cash (Iraq)
                  </h3>
                  <p className="text-base md:text-lg font-medium break-all">***********</p>
                </div>
              </div>
            </Card>

            <Card className="p-4 md:p-6">
              <div className="flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4">
                <img src="/Qicard iraq.png" alt="Rafidein Account" className="h-16 w-16 flex-shrink-0 mb-2 md:mb-0" />
                <div>
                  <h3 className="text-base md:text-xl font-semibold mb-1 md:mb-2 break-words"> Rafidain Bank</h3>
                  <p className="text-base md:text-lg font-medium break-all">**********</p>
                </div>
              </div>
            </Card>

            <Card className="p-4 md:p-6">
              <div className="flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4">
                <img src="/Asia pay.png" alt="Asia Pay" className="h-16 w-16 flex-shrink-0 mb-2 md:mb-0" />
                <div>
                  <h3 className="text-base md:text-xl font-semibold mb-1 md:mb-2">Asia Pay</h3>
                  <p className="text-base md:text-lg font-medium break-all">***********</p>
                </div>
              </div>
            </Card>
          </div>
        </section>

        {/* Outside Iraq Section */}
        <section>
          <h2 className="text-xl md:text-2xl font-semibold mb-4 md:mb-6 mt-8 md:mt-12" style={{ color: primaryColor }}>Outside Iraq</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <Card className="p-4 md:p-6">
              <div className="flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4">
                <img src="/Paypal.png" alt="PayPal" className="h-16 w-16 flex-shrink-0 mb-2 md:mb-0" />
                <div>
                  <h3 className="text-base md:text-xl font-semibold mb-1 md:mb-2">PayPal</h3>
                  <p className="mb-1 md:mb-2 text-sm md:text-base">You can pay through this link👇</p>
                  <a
                    href="https://paypal.me/FatimahNaser?country.x=JO&locale.x=en_US"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium text-sm md:text-base"
                  >
                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.4-.04c-.524-.03-1.05-.04-1.608-.04h-2.19c-.524 0-.968.382-1.05.9L14.85 14.6c-.082.518.302.937.826.937h2.19c3.73 0 6.607-1.518 7.397-5.897.4-2.22-.1-3.66-1.24-4.723z"/>
                    </svg>
                    Pay with PayPal
                  </a>
                  <p className="text-xs text-gray-500 mt-2">Click to open PayPal payment page</p>
                </div>
              </div>
            </Card>

            <Card className="p-4 md:p-6">
              <div className="flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4">
                <img src="/Amazon gift card.png" alt="Amazon Gift" className="h-16 w-16 flex-shrink-0 mb-2 md:mb-0" />
                <div className="space-y-2 w-full">
                  <h3 className="text-base md:text-xl font-semibold mb-1 md:mb-2">Amazon Gift</h3>
                 
                  <a
                    href="https://www.amazon.com/Amazon-eGift-Card-Logo-Animated/dp/B07PCMWTSG/ref=mp_s_a_1_1?adgrpid=160438626878&dib=eyJ2IjoiMSJ9.y343JC2nqCCCtAt_MaFdYdSEoDk1IL8C8OVn3MADfESEozTH6jWzFIJ4WqlXn7_W-n2IrnPR-rfE3Spk4QYVuOOL7cvbuK9Esy0CXQivH6v0c4KW6RfZeH8pYn15Gdj-s62p0V-fiHzAE12D4YOgeY2zQf3sUuAQF30eHiR7nSfSyvGj9P0M79Suz3VRAqqxS64beG-r2SJhB_Y_apq-6Q.gbfTjpxr2hWpO9dWg-U8dthgvZM21cwxR6PrsZBpG38&dib_tag=se&hvadid=692707382867&hvdev=m&hvlocphy=9211521&hvnetw=g&hvqmt=e&hvrand=15003258399388157606&hvtargid=kwd-2389411675177&hydadcr=22339_13333066&keywords=amazon%27+gift+card&mcid=90ecf431d9b83733b420d0f87065fc78&qid=1748814128&sr=8-1"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline block break-all"
                  >
                    Amazon eGift Card Link
                  </a>
                  <p className="text-gray-700 text-sm md:text-base">
                    Please choose the amount and then send it to this email👇
                  </p>
                  <p className="text-base md:text-lg font-medium break-all"><EMAIL></p>
                </div>
              </div>
            </Card>
          </div>
        </section>

        {/* Cash on Delivery Section */}
        <section className="mt-8">
          <h2 className="text-xl md:text-2xl font-semibold mb-4 md:mb-6 mt-8 md:mt-12" style={{ color: primaryColor }}>Cash on Delivery</h2>
          <Card className="p-2 md:p-4">
            <div className="flex flex-col items-center text-center md:flex-row md:items-start md:text-left">
              <div className="w-32 h-32 md:w-40 md:h-40 rounded-full flex items-center justify-center mb-4 md:mb-0 md:mr-4" style={{ backgroundColor: `${primaryColor}20` }}>
                <img src="/Cash on delivery.png" alt="Cash on Delivery" className="w-full h-full rounded-full bg-white p-2 object-contain" />
              </div>
              <div className="md:flex-1">
                <h3 className="text-base md:text-xl font-semibold mb-1 md:mb-2">Cash on Delivery</h3>
                <p className="text-muted-foreground text-sm md:text-base">
                  Pay in cash upon delivery - we offer delivery to all provinces within Iraq. Additional fees may apply depending on your location.
                </p>
              </div>
            </div>
           
          </Card>
        </section>

        {/* Payment Process */}
        <section className="mt-12">
          <h2 className="text-xl md:text-2xl font-bold mb-4 md:mb-6 mt-8 md:mt-12">Payment Process</h2>
          <Card className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold">1</span>
                </div>
                <h3 className="font-medium text-sm md:text-base mb-1 md:mb-2">Select Products</h3>
                <p className="text-xs md:text-sm text-muted-foreground">Add items to your cart</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold">2</span>
                </div>
                <h3 className="font-medium text-sm md:text-base mb-1 md:mb-2">Shipping Details</h3>
                <p className="text-xs md:text-sm text-muted-foreground">Enter your shipping information</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold">3</span>
                </div>
                <h3 className="font-medium text-sm md:text-base mb-1 md:mb-2">Payment Method</h3>
                <p className="text-xs md:text-sm text-muted-foreground">Choose your payment method</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold">4</span>
                </div>
                <h3 className="font-medium text-sm md:text-base mb-1 md:mb-2">Confirmation</h3>
                <p className="text-xs md:text-sm text-muted-foreground">Review and confirm your order</p>
              </div>
            </div>
          </Card>
        </section>

        {/* Contact Information */}
        <div className="mt-12 text-center">
          <p className="text-gray-700 text-sm md:text-base text-center px-4">
            For payment support, please contact our customer service team.
          </p>
        </div>
      </div>
    </div>
  );
}