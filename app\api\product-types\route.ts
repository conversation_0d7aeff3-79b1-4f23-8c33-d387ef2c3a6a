import { NextResponse } from 'next/server';

const productTypes = [
  {
    id: 1,
    name: 'Courses',
    description: 'Educational courses and training materials'
  },
  {
    id: 2,
    name: 'Books',
    description: 'Medical books and reference materials'
  },
  {
    id: 3,
    name: 'Equipment',
    description: 'Medical equipment and tools'
  },
  {
    id: 4,
    name: 'Software',
    description: 'Medical software and applications'
  }
];

export async function GET() {
  return NextResponse.json(productTypes);
}

export async function POST() {
  // For now, return the same product types data for POST requests
  // This can be enhanced later to handle dynamic product type requests
  return NextResponse.json(productTypes);
}
