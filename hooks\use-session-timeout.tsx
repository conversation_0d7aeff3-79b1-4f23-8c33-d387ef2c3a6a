"use client";

import { useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@/contexts/user-context';
import { useToast } from '@/hooks/use-toast';

interface UseSessionTimeoutProps {
  timeoutMinutes?: number;
  warningMinutes?: number;
  redirectTo?: string;
  onTimeout?: () => void;
  onWarning?: () => void;
}

export function useSessionTimeout({
  timeoutMinutes = 30, // Default 30 minutes
  warningMinutes = 5, // Warning 5 minutes before timeout
  redirectTo = '/login',
  onTimeout,
  onWarning
}: UseSessionTimeoutProps = {}) {
  const { logout, isLoggedIn } = useUser();
  const router = useRouter();
  const { toast } = useToast();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const warningRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());

  const clearTimers = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (warningRef.current) {
      clearTimeout(warningRef.current);
      warningRef.current = null;
    }
  }, []);

  const handleTimeout = useCallback(async () => {
    console.log('🕐 Session timeout triggered');
    
    try {
      await logout();
      
      toast({
        title: "Session Expired",
        description: "Your session has expired due to inactivity. Please log in again.",
        variant: "destructive",
      });
      
      if (onTimeout) {
        onTimeout();
      }
      
      // Redirect to login page with return URL
      const currentPath = window.location.pathname;
      const returnUrl = currentPath !== '/login' ? encodeURIComponent(currentPath) : '';
      const loginUrl = returnUrl ? `${redirectTo}?redirect=${returnUrl}` : redirectTo;
      router.push(loginUrl);
    } catch (error) {
      console.error('Error during session timeout:', error);
      // Force redirect even if logout fails
      // Redirect to login page with return URL for session end
      const currentPath = window.location.pathname;
      const returnUrl = currentPath !== '/login' ? encodeURIComponent(currentPath) : '';
      const loginUrl = returnUrl ? `${redirectTo}?redirect=${returnUrl}` : redirectTo;
      router.push(loginUrl);
    }
  }, [logout, toast, onTimeout, router, redirectTo]);

  const handleWarning = useCallback(() => {
    console.log('⚠️ Session timeout warning triggered');
    
    toast({
      title: "Session Warning",
      description: `Your session will expire in ${warningMinutes} minutes due to inactivity.`,
      variant: "default",
    });
    
    if (onWarning) {
      onWarning();
    }
  }, [toast, warningMinutes, onWarning]);

  const resetTimer = useCallback(() => {
    if (!isLoggedIn) return;
    
    lastActivityRef.current = Date.now();
    clearTimers();
    
    // Set warning timer
    const warningTime = (timeoutMinutes - warningMinutes) * 60 * 1000;
    warningRef.current = setTimeout(handleWarning, warningTime);
    
    // Set timeout timer
    const timeoutTime = timeoutMinutes * 60 * 1000;
    timeoutRef.current = setTimeout(handleTimeout, timeoutTime);
    
    console.log(`🔄 Session timer reset - timeout in ${timeoutMinutes} minutes`);
  }, [isLoggedIn, timeoutMinutes, warningMinutes, handleWarning, handleTimeout, clearTimers]);

  const extendSession = useCallback(() => {
    console.log('⏰ Session extended by user action');
    resetTimer();
  }, [resetTimer]);

  const endSession = useCallback(async () => {
    console.log('🚪 Session ended by user');
    clearTimers();
    
    try {
      await logout();
      
      toast({
        title: "Session Ended",
        description: "You have been logged out successfully.",
        variant: "default",
      });
      
      // Redirect to login page with return URL
      const currentPath = window.location.pathname;
      const returnUrl = currentPath !== '/login' ? encodeURIComponent(currentPath) : '';
      const loginUrl = returnUrl ? `${redirectTo}?redirect=${returnUrl}` : redirectTo;
      router.push(loginUrl);
    } catch (error) {
        console.error('Error ending session:', error);
        // Redirect to login page with return URL even on error
        const currentPath = window.location.pathname;
        const returnUrl = currentPath !== '/login' ? encodeURIComponent(currentPath) : '';
        const loginUrl = returnUrl ? `${redirectTo}?redirect=${returnUrl}` : redirectTo;
        router.push(loginUrl);
      }
  }, [clearTimers, logout, toast, router, redirectTo]);

  // Activity event listeners
  useEffect(() => {
    if (!isLoggedIn) {
      clearTimers();
      return;
    }

    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      const now = Date.now();
      // Only reset timer if more than 1 minute has passed since last activity
      // This prevents excessive timer resets
      if (now - lastActivityRef.current > 60000) {
        resetTimer();
      }
    };

    // Add event listeners
    activityEvents.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    // Initial timer setup
    resetTimer();

    // Cleanup
    return () => {
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
      clearTimers();
    };
  }, [isLoggedIn, resetTimer, clearTimers]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearTimers();
    };
  }, [clearTimers]);

  return {
    extendSession,
    endSession,
    resetTimer,
    timeRemaining: timeoutMinutes * 60 * 1000 // in milliseconds
  };
}