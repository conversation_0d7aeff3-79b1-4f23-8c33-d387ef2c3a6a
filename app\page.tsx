import { Metadata } from 'next';
import { generateCanonicalUrl, generateMetaWithCanonical } from '@/lib/canonical-utils';
import { HomePageClient } from '@/components/pages/home-page-client';

// Generate metadata for the homepage
export const metadata: Metadata = generateMetaWithCanonical(
  generateCanonicalUrl(''),
  'Code Medical Apps - Medical Courses, Books & Resources',
  'Discover professional medical courses, ebooks, printed books and medical accounts. Your trusted source for medical education and healthcare resources worldwide.',
  {
    keywords: 'medical courses, medical books, healthcare education, medical training, medical resources, medical ebooks, printed medical books, medical accounts',
    openGraph: {
      images: [
        {
          url: '/og-home.jpg',
          width: 1200,
          height: 630,
          alt: 'Code Medical Apps - Medical Education Platform',
        },
      ],
    },
  }
);

export default function Home() {
  return <HomePageClient />;
}
