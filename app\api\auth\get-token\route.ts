import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    
    // Try to get token from HttpOnly cookie first
    const tokenCookie = cookieStore.get('auth_token');
    
    if (tokenCookie?.value) {
      return NextResponse.json({ 
        token: tokenCookie.value,
        source: 'httponly_cookie'
      });
    }

    // Fallback to client-accessible token cookie
    const clientTokenCookie = cookieStore.get('auth_token_client');
    
    if (clientTokenCookie?.value) {
      return NextResponse.json({ 
        token: clientTokenCookie.value,
        source: 'client_cookie'
      });
    }

    // If no token found, return null
    return NextResponse.json({ 
      token: null,
      source: 'none'
    });
    
  } catch (error) {
    console.error('Error retrieving token:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve token' },
      { status: 500 }
    );
  }
}