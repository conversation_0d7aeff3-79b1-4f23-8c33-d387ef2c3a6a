"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Link from "next/link";
import { useSettings } from "@/contexts/settings-context";
import { useColorThemeContext } from "@/contexts/color-theme-context";
import { useUser } from "@/contexts/user-context";
import { usePoints } from "@/contexts/point-context";
import { useSessionTimeout } from "@/hooks/use-session-timeout";

import { MakeApiCallAsync, Config } from "@/lib/api-helper";
import { useToast } from "@/hooks/use-toast";
import {
  User,
  Package,
  CreditCard,
  Heart,
  LogOut,
  Save,
  Eye,
  EyeOff,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Shield,
  Star,
  Globe,
  UserCheck,
  Settings,
  RefreshCw,
  Search,
  CheckCircle,
} from "lucide-react";

export default function AccountPage() {
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const primaryTextColor = currentTheme.primaryForeground;
  
  // Add dynamic styles for primary color
  useEffect(() => {
    const style = document.createElement('style');
    if (!document.querySelector('#account-primary-dynamic-style')) {
      style.id = 'account-primary-dynamic-style';
      style.textContent = `
        .bg-primary-dynamic-10 { background-color: ${primaryColor}1a !important; }
        .text-primary-dynamic { color: ${primaryColor} !important; }
        .hover\:text-primary-dynamic-80:hover { color: ${primaryColor}cc !important; }
        .hover\:bg-primary-dynamic-10:hover { background-color: ${primaryColor}1a !important; }
        .bg-primary-dynamic { background-color: ${primaryColor} !important; }
        .bg-primary-dynamic:hover { background-color: ${primaryColor}dd !important; }
      `;
      document.head.appendChild(style);
    }
    return () => {
      const existingStyle = document.querySelector('#account-primary-dynamic-style');
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, [primaryColor]);
  const { user, isLoggedIn, isLoading: authLoading, logout, updateProfile } = useUser();
  const pointsContext = usePoints();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('profile');
  const router = useRouter();
  
  // Handle authentication redirect directly
  useEffect(() => {
    if (!authLoading && !isLoggedIn) {
      const redirectUrl = `/login?redirect=${encodeURIComponent('/account')}`;
      router.push(redirectUrl);
    }
  }, [authLoading, isLoggedIn, router]);
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [categorySearch, setCategorySearch] = useState("");
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [filteredCategories, setFilteredCategories] = useState<any[]>([]);
  const [profileData, setProfileData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    gender: "",
    category: "",
    newPassword: "",
    confirmPassword: "",
    currentPassword: "",
  });
  const [error, setError] = useState("");

  // Session timeout management
  const { endSession } = useSessionTimeout({
    timeoutMinutes: 30, // 30 minutes of inactivity
    warningMinutes: 5,  // Warning 5 minutes before timeout
    redirectTo: '/login'
  });

  // Debug logging for user object
  useEffect(() => {
    console.log("🔍 Account Page Debug - Full user object:", user);
    console.log("🔍 Account Page Debug - Gender fields:", {
      Gender: user?.Gender,
      gender: user?.gender,
      type: typeof user?.Gender
    });
    console.log("🔍 Account Page Debug - Category fields:", {
      CategoryID: user?.CategoryID,
      CategoryId: user?.CategoryId,
      SpecialistId: user?.SpecialistId,
      CatID: user?.CatID,
      categoryId: user?.categoryId
    });
    console.log("🔍 Account Page Debug - IsActive:", user?.IsActive, "(type:", typeof user?.IsActive, ")");
    console.log("🔍 Account Page Debug - CreatedOn:", user?.CreatedOn, "(type:", typeof user?.CreatedOn, ")");
    if (user) {
      console.log("🔍 Account Page Debug - All user keys:", Object.keys(user));
    }
  }, [user]);

  // All useEffect hooks must be at the top before any conditional returns
  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const param = {
          PageNumber: 1,
          PageSize: 100,
          SortColumn: "Name",
          SortOrder: "ASC",
        };
        const headers = {
          "Content-Type": "application/json",
          Accept: "application/json",
          // Authorization header will be automatically added by MakeApiCallAsync
        };
        const categoriesResponse = await MakeApiCallAsync(
          Config.END_POINT_NAMES.GET_CATEGORIES_LIST,
          null,
          param,
          headers,
          "POST",
          true
        );

        if (categoriesResponse?.data?.data) {
          try {
            const parsedData = JSON.parse(categoriesResponse.data.data);
            setCategories(parsedData);
            setFilteredCategories(parsedData);
          } catch (parseError) {
            console.error("Error parsing categories data:", parseError);
            setCategories([]);
            setFilteredCategories([]);
          }
        } else {
          setCategories([]);
          setFilteredCategories([]);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
        setCategories([]);
        setFilteredCategories([]);
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // Update profile data when user data changes
  useEffect(() => {
    if (user) {
      console.log("🔍 Account page: User data received:", user);
      console.log("🔍 Account page: Gender field:", user.Gender || user.gender);
      console.log("🔍 Account page: Category fields:", {
        CategoryID: user.CategoryID,
        CategoryId: user.CategoryId,
        SpecialistId: user.SpecialistId,
        CatID: user.CatID
      });

      // Map all possible field variations from user data
      const userData = {
        firstName: user.FirstName || user.firstname || user.first_name || "",
        lastName: user.LastName || user.lastname || user.last_name || "",
        email:
          user.Email ||
          user.EmailAddress ||
          user.email ||
          user.email_address ||
          "",
        phone:
          user.PhoneNumber ||
          user.PhoneNo ||
          user.MobileNo ||
          user.phone ||
          user.mobile ||
          "",
        gender: user.Gender || user.gender || "",
        category: user.CategoryID || user.CategoryId || user.category_id || user.categoryId || user.SpecialistId || user.specialist_id || user.CatID || "",
      };

      console.log("🔍 Account page: Mapped profile data:", userData);

      // Set timeout to ensure DOM is ready before updating state
      setTimeout(() => {
        setProfileData((prev) => ({
          ...prev,
          ...userData,
        }));

        console.log("🔍 Account page: Profile data after update:", {
          gender: userData.gender,
          category: userData.category,
          profileDataGender: profileData.gender,
          profileDataCategory: profileData.category
        });
        
        // Directly update the gender select element
        const genderSelect = document.getElementById('gender') as HTMLSelectElement;
        if (genderSelect && userData.gender) {
          console.log("🔍 Account page: Directly setting gender select to:", userData.gender);
          genderSelect.value = userData.gender;
        }
      }, 100);
    }
  }, [user]);

  // Update category search when categories load and user has a category
  useEffect(() => {
    if (categories.length > 0 && profileData.category) {
      console.log("🔍 Account page: Setting up category search");
      console.log("🔍 Account page: Profile category:", profileData.category);
      console.log("🔍 Account page: Available categories:", categories.length);

      const categoryId = parseInt(profileData.category.toString());
      const userCategory = categories.find(
        (cat) => cat.CategoryID === categoryId || cat.CategoryId === categoryId
      );

      console.log("🔍 Account page: Found user category:", userCategory);

      if (userCategory) {
        const categoryName = userCategory.CategoryName || userCategory.Name || "";
        console.log("🔍 Account page: Setting category search to:", categoryName);
        setCategorySearch(categoryName);
        setFilteredCategories(categories);
      }
    }
  }, [categories, profileData.category]);

  // Watch for profile data changes and update UI elements
  useEffect(() => {
    // Update gender select value
    const genderSelect = document.getElementById('gender') as HTMLSelectElement;
    if (genderSelect && profileData.gender) {
      console.log("🔍 Account page: Setting gender select to:", profileData.gender);
      genderSelect.value = profileData.gender;
    }

    // Update category search field when category changes
    if (categories.length > 0 && profileData.category) {
      const categoryId = parseInt(profileData.category.toString());
      const selectedCategory = categories.find(
        (cat) => cat.CategoryID === categoryId || cat.CategoryId === categoryId
      );
      if (selectedCategory) {
        const categoryName = selectedCategory.CategoryName || selectedCategory.Name || "";
        console.log("🔍 Account page: Setting category search to:", categoryName);
        setCategorySearch(categoryName);
      }
    }
  }, [profileData.gender, profileData.category, categories]);

  // Handle active tab styling with theme colors
  useEffect(() => {
    const updateTabStyles = () => {
      const tabTriggers = document.querySelectorAll('[data-active-bg]');
      tabTriggers.forEach((trigger) => {
        const element = trigger as HTMLElement;
        const isActive = element.getAttribute('data-state') === 'active';
        
        if (isActive) {
          element.style.setProperty('--state-active', primaryColor);
          element.style.setProperty('--state-active-text', primaryTextColor);
          element.style.setProperty('--state-active-scale', 'scale(1.05)');
          element.style.setProperty('--state-active-shadow', '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)');
          element.style.setProperty('--state-active-border', primaryColor);
          element.style.backgroundColor = primaryColor;
          element.style.color = primaryTextColor;
          element.style.transform = 'scale(1.05)';
          element.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
          element.style.borderColor = primaryColor;
        } else {
          element.style.setProperty('--state-active', 'rgb(209 213 219)');
          element.style.setProperty('--state-active-text', 'rgb(55 65 81)');
          element.style.setProperty('--state-active-scale', 'scale(1)');
          element.style.setProperty('--state-active-shadow', 'none');
          element.style.setProperty('--state-active-border', 'transparent');
          element.style.backgroundColor = 'rgb(209 213 219)';
          element.style.color = 'rgb(55 65 81)';
          element.style.transform = 'scale(1)';
          element.style.boxShadow = 'none';
          element.style.borderColor = 'transparent';
        }
      });
    };

    // Initial update
    updateTabStyles();

    // Set up observer for tab changes
    const observer = new MutationObserver(updateTabStyles);
    const tabsList = document.querySelector('[role="tablist"]');
    if (tabsList) {
      observer.observe(tabsList, {
        attributes: true,
        subtree: true,
        attributeFilter: ['data-state']
      });
    }

    return () => observer.disconnect();
  }, [primaryColor, primaryTextColor]);



  // Fetch points when account page loads and user is logged in
  useEffect(() => {
    if (isLoggedIn && user && !pointsContext.isLoading) {
      console.log('🔄 Account page: Fetching fresh points on page load');
      pointsContext.fetchPoints();
    }
  }, [isLoggedIn, user]); // Only depend on login state, not the fetch function to avoid loops

  // Watch for user changes and update profileData
  useEffect(() => {
    if (user && isLoggedIn) {
      console.log("🔄 Account page: User data changed, updating profileData");

      const userData = {
        firstName: user.FirstName || user.firstname || user.first_name || "",
        lastName: user.LastName || user.lastname || user.last_name || "",
        email: user.Email || user.EmailAddress || user.email || user.email_address || "",
        phone: user.PhoneNumber || user.PhoneNo || user.MobileNo || user.phone || user.mobile || "",
        gender: user.Gender || user.gender || "",
        category: user.CategoryID || user.CategoryId || user.category_id || user.categoryId || user.SpecialistId || user.specialist_id || user.CatID || "",
      };

      console.log("🔍 Account page: Updating profileData with:", userData);

      setProfileData(prev => ({
        ...prev,
        ...userData,
      }));
    }
  }, [user, isLoggedIn]);





  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setError("");
    
    // Real-time update for select options to show current values
    if (name === 'gender') {
      console.log("🔍 Account page: Gender changed to:", value);
      // Update gender select immediately
      setTimeout(() => {
        const genderSelect = document.getElementById('gender') as HTMLSelectElement;
        if (genderSelect) {
          genderSelect.value = value;
          console.log("🔍 Account page: Gender select updated to:", value);
        }
      }, 10);
    }
  };

  // Show loading state while checking authentication
  if (authLoading) {
    console.log('🔄 Account page: Showing loading state');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Check if this is a password update
    const isPasswordUpdate = profileData.newPassword || profileData.confirmPassword || profileData.currentPassword;

    if (isPasswordUpdate) {
      if (profileData.newPassword !== profileData.confirmPassword) {
        setError("New password and confirm password do not match");
        setLoading(false);
        return;
      }

      if (profileData.newPassword && profileData.newPassword.length < 8) {
        setError("Password must be at least 8 characters long");
        setLoading(false);
        return;
      }

      // TODO: Implement password update API call
      setError("Password update functionality is not yet implemented");
      setLoading(false);
      return;
    }

    // Profile update
    try {
      const headers = {
        Accept: "application/json",
        "Content-Type": "application/json",
      };

      const param = {
        requestParameters: {
          FirstName: profileData.firstName,
          LastName: profileData.lastName,
          Gender: profileData.gender || "Male",
          CategoryId: profileData.category || "1024"
        }
      };

      console.log("Sending update profile request:", param);

      const response = await MakeApiCallAsync(
        Config.END_POINT_NAMES.UPDATE_PROFILE,
        null,
        param,
        headers,
        "POST",
        true
      );

      console.log("Profile update response:", response);

      if (response?.data && !response.data.errorMessage) {
        // Parse the response data
        let responseData;
        if (typeof response.data.data === 'string') {
          responseData = JSON.parse(response.data.data);
        } else {
          responseData = response.data.data;
        }

        if (Array.isArray(responseData) && responseData.length > 0 &&
            responseData[0].ResponseMsg === "Saved Successfully") {

          setSuccess(true);

          // Update user context with new profile data
          const updatedUserData = {
            FirstName: profileData.firstName,
            LastName: profileData.lastName,
            UserName: `${profileData.firstName} ${profileData.lastName}`.trim(),
            Gender: profileData.gender,
            CategoryID: profileData.category,
            CategoryId: profileData.category,
            SpecialistId: profileData.category
          };
          
          await updateProfile(updatedUserData);

          // Update cookies with new user data including gender and specialist
          try {
            const updateResponse = await fetch('/api/auth/update-user-cookies', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include',
              body: JSON.stringify({
                FirstName: profileData.firstName,
                LastName: profileData.lastName,
                UserName: `${profileData.firstName} ${profileData.lastName}`.trim(),
                Gender: profileData.gender,
                CategoryID: profileData.category,
                CategoryId: profileData.category,
                SpecialistId: profileData.category
              })
            });

            if (updateResponse.ok) {
              console.log('User cookies updated successfully with all profile data');
              
              // Update select option values in the UI to reflect changes
              setTimeout(() => {
                // Update gender select if it exists
                const genderSelect = document.getElementById('gender') as HTMLSelectElement;
                if (genderSelect) {
                  genderSelect.value = profileData.gender;
                }
                
                // Update category/specialist search field
                if (profileData.category && categories.length > 0) {
                  const selectedCategory = categories.find(
                    (cat) => cat.CategoryID === parseInt(profileData.category.toString())
                  );
                  if (selectedCategory) {
                    setCategorySearch(selectedCategory.Name || selectedCategory.CategoryName || '');
                  }
                }
              }, 100);
            }
          } catch (cookieError) {
            console.warn('Failed to update user cookies:', cookieError);
          }

          toast({
            title: "Success!",
            description: "Profile updated successfully!",
          });

          // Reset success message after 3 seconds
          setTimeout(() => setSuccess(false), 3000);
        } else {
          throw new Error(responseData?.[0]?.ResponseMsg || 'Failed to update profile');
        }
      } else {
        throw new Error(response?.data?.errorMessage || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile. Please try again.';
      setError(errorMessage);

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">{t("home")}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t("myAccount")}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Page Content */}
      <div className="max-w-full md:max-w-7xl lg:max-w-7xl mx-auto">
        <h1 className="text-2xl md:text-3xl font-bold mb-4 md:mb-6">{t("myAccount")}</h1>

        <div className="grid grid-cols-1 md:grid-cols-[200px_1fr] lg:grid-cols-[250px_1fr] gap-4 md:gap-6 xl:gap-8">
          {/* Sidebar */}
          <div className="space-y-4">
            <Card>
              <div className="p-4 md:p-6">
                <div className="flex flex-col items-center text-center mb-4 md:mb-6">
                  <div className="w-16 h-16 md:w-20 md:h-20 rounded-full bg-primary-dynamic-10 flex items-center justify-center mb-3 md:mb-4 relative">
                    <User className="h-8 w-8 md:h-10 md:w-10 text-primary-dynamic" />
                    {user?.IsVerified && (
                      <div className="absolute -bottom-1 -right-1 w-5 h-5 md:w-6 md:h-6 bg-success rounded-full flex items-center justify-center">
                        <UserCheck className="h-2.5 w-2.5 md:h-3 md:w-3 text-success-foreground" />
                      </div>
                    )}
                  </div>
                  <h3 className="font-medium text-sm md:text-base">
                    {user?.FirstName} {user?.LastName}
                  </h3>
                  <p className="text-xs md:text-sm text-muted-foreground">
                    {user?.Email || user?.EmailAddress}
                  </p>
                  <div className="flex items-center gap-1 mt-2 px-2 py-1 bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-full">
                    <Star className="h-3 w-3 text-amber-700" />
                    <span className="text-xs font-medium text-amber-700">
                      {pointsContext.isLoading ? 'Loading...' : (pointsContext.points || '0.00')} Credit
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1 hover:bg-warning/20"
                      onClick={() => pointsContext.fetchPoints()}
                      disabled={pointsContext.isLoading}
                    >
                      <RefreshCw className={`h-3 w-3 text-amber-700 ${pointsContext.isLoading ? 'animate-spin' : ''}`} />
                    </Button>
                  </div>
                </div>

                <div className="space-y-1">
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-xs md:text-sm py-2 md:py-2.5"
                    asChild
                  >
                    <Link href="/account">
                      <User className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                      Profile
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-xs md:text-sm py-2 md:py-2.5"
                    asChild
                  >
                    <Link href="/orders">
                      <Package className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                      Orders
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-xs md:text-sm py-2 md:py-2.5"
                    asChild
                  >
                    <Link href="/addresses">
                      <MapPin className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                      Addresses
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-xs md:text-sm py-2 md:py-2.5"
                    asChild
                  >
                    <Link href="/payment-methods">
                      <CreditCard className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                      Payment Methods
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-xs md:text-sm py-2 md:py-2.5"
                    asChild
                  >
                    <Link href="/wishlist">
                      <Heart className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                      Wishlist
                    </Link>
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-primary-dynamic hover:text-primary-dynamic-80 hover:bg-primary-dynamic-10 text-xs md:text-sm py-2 md:py-2.5"
                    onClick={() => {
                      endSession();
                    }}
                  >
                    <CheckCircle className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                    Complete Session
                  </Button>
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-destructive hover:text-destructive/80 hover:bg-destructive/10 text-xs md:text-sm py-2 md:py-2.5"
                    onClick={() => {
                      logout();
                      toast({
                        title: "Logged Out",
                        description: "You have been successfully logged out.",
                      });
                      router.push("/");
                    }}
                  >
                    <LogOut className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                    Logout
                  </Button>
                </div>
              </div>
            </Card>
          </div>

          {/* Main Content */}
          <div className="w-full min-w-0">
            <Tabs defaultValue="profile" onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-1 md:grid-cols-3 mb-4 md:mb-6 gap-1 md:gap-2 bg-transparent p-0 h-auto">
                <TabsTrigger 
                  value="profile"
                  className="rounded-lg px-3 py-2 md:px-4 md:py-2.5 text-xs md:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102"
                  style={{
                    backgroundColor: activeTab === 'profile' ? primaryColor : 'rgb(209 213 219)',
                    color: activeTab === 'profile' ? primaryTextColor : 'rgb(55 65 81)',
                    transform: activeTab === 'profile' ? 'scale(1.05)' : 'scale(1)',
                    boxShadow: activeTab === 'profile' ? '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' : 'none',
                    borderColor: activeTab === 'profile' ? primaryColor : 'transparent'
                  }}
                >
                  Personal Information
                </TabsTrigger>
                <TabsTrigger 
                  value="password"
                  className="rounded-lg px-3 py-2 md:px-4 md:py-2.5 text-xs md:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102"
                  style={{
                    backgroundColor: activeTab === 'password' ? primaryColor : 'rgb(209 213 219)',
                    color: activeTab === 'password' ? primaryTextColor : 'rgb(55 65 81)',
                    transform: activeTab === 'password' ? 'scale(1.05)' : 'scale(1)',
                    boxShadow: activeTab === 'password' ? '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' : 'none',
                    borderColor: activeTab === 'password' ? primaryColor : 'transparent'
                  }}
                >
                  Security
                </TabsTrigger>
                <TabsTrigger 
                  value="overview"
                  className="rounded-lg px-3 py-2 md:px-4 md:py-2.5 text-xs md:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102"
                  style={{
                    backgroundColor: activeTab === 'overview' ? primaryColor : 'rgb(209 213 219)',
                    color: activeTab === 'overview' ? primaryTextColor : 'rgb(55 65 81)',
                    transform: activeTab === 'overview' ? 'scale(1.05)' : 'scale(1)',
                    boxShadow: activeTab === 'overview' ? '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)' : 'none',
                    borderColor: activeTab === 'overview' ? primaryColor : 'transparent'
                  }}
                >
                  Account Details
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-4 bg-white rounded-lg shadow-sm">
                <div className="p-4 md:p-6 lg:p-8">
                  <h3 className="text-xl font-semibold mb-6">Account Details</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  {/* Account Information */}
                  <Card>
                    <div className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <User className="h-5 w-5 text-primary" />
                        Account Information
                      </h3>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-sm text-muted-foreground">
                            Phone Number
                          </span>
                          <span className="font-medium">
                            {user?.PhoneNo || user?.MobileNo || user?.PhoneNumber || "Not specified"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-sm text-muted-foreground">
                            Gender
                          </span>
                          <span className="font-medium">
                            {profileData.gender || user?.Gender || user?.gender || "Not specified"}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-sm text-muted-foreground">
                            Specialist
                          </span>
                          <span className="font-medium">
                            {(() => {
                              // First try to find by profileData.category
                              if (profileData.category) {
                                const categoryByProfile = categories.find(
                                  (cat) => cat.CategoryID === parseInt(profileData.category?.toString() || "0")
                                );
                                if (categoryByProfile) {
                                  return categoryByProfile.Name || categoryByProfile.CategoryName;
                                }
                              }

                              // Then try to find by user data
                              const categoryByUser = categories.find(
                                (cat) =>
                                  cat.CategoryID === (user?.CategoryID || user?.CategoryId || user?.categoryId || user?.SpecialistId)
                              );

                              return categoryByUser?.Name || categoryByUser?.CategoryName || "Not specified";
                            })()}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-sm text-muted-foreground">
                            Account Status
                          </span>
                          <div className="flex items-center gap-2">
                            {(() => {
                              // Handle various possible IsActive formats from API
                              const isActive = user?.IsActive === true || 
                                              user?.IsActive === 1 || 
                                              user?.IsActive === "true" || 
                                              user?.IsActive === "1" ||
                                              (typeof user?.IsActive === 'string' && user?.IsActive.toLowerCase() === 'true');
                              
                              return isActive ? (
                                <>
                                  <div className="w-2 h-2 bg-success rounded-full"></div>
                                  <span className="text-success font-medium">
                                    Active
                                  </span>
                                </>
                              ) : (
                                <>
                                  <div className="w-2 h-2 bg-destructive rounded-full"></div>
                                  <span className="text-destructive font-medium">
                                    Inactive (Debug: IsActive={JSON.stringify(user?.IsActive)}, type={typeof user?.IsActive})
                                  </span>
                                </>
                              );
                            })()}
                          </div>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-sm text-muted-foreground">
                            Member Since
                          </span>
                          <span className="font-medium">
                            {user?.CreatedOn
                              ? new Date(user.CreatedOn).toLocaleDateString()
                              : `N/A (Debug: CreatedOn=${JSON.stringify(user?.CreatedOn)}, type=${typeof user?.CreatedOn})`}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2">
                          <span className="text-sm text-muted-foreground">
                            Credit Balance
                          </span>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span className="font-medium">
                              {user?.Pointno || 0}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Contact Information */}
                  <Card>
                    <div className="p-6">
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <Phone className="h-5 w-5 text-primary" />
                        Contact Information
                      </h3>
                      <div className="space-y-4">
                        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <Mail className="h-5 w-5 text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-500">
                              Email Address
                            </p>
                            <p className="font-medium">
                              {user?.EmailAddress || user?.Email}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <Phone className="h-5 w-5 text-gray-500" />
                          <div>
                            <p className="text-sm text-gray-500">
                              Phone Number
                            </p>
                            <p className="font-medium">
                              {user?.PhoneNo || user?.MobileNo || user?.PhoneNumber || "Not provided (Debug: PhoneNo=" + JSON.stringify(user?.PhoneNo) + ", MobileNo=" + JSON.stringify(user?.MobileNo) + ", PhoneNumber=" + JSON.stringify(user?.PhoneNumber) + ")"}
                            </p>
                          </div>
                        </div>
                        {user?.MobileNo &&
                          user?.PhoneNo &&
                          user?.MobileNo !== user?.PhoneNo && (
                            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                              <Phone className="h-5 w-5 text-gray-500" />
                              <div>
                                <p className="text-sm text-gray-500">
                                  Mobile Number
                                </p>
                                <p className="font-medium">{user?.MobileNo}</p>
                              </div>
                            </div>
                          )}
                      </div>
                    </div>
                  </Card>


                  </div>
                </div>
              </TabsContent>

              <TabsContent value="profile" className="mt-4 bg-white rounded-lg shadow-sm w-full">
                <div className="p-4 md:p-6 lg:p-8 w-full">
                  <h3 className="text-lg md:text-xl font-semibold mb-4 md:mb-6">Personal Information</h3>
                  <form onSubmit={handleSubmit} className="w-full">
                    {success && (
                      <div className="bg-success/10 border border-success text-success px-4 py-3 rounded mb-4 md:mb-6">
                        Profile updated successfully!
                      </div>
                    )}

                    <div className="bg-white border border-gray-200 rounded-lg p-4 md:p-6 lg:p-8 mb-4 md:mb-6 w-full max-w-none">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6 mb-4 md:mb-6">
                      <div>
                        <Label htmlFor="firstName">First Name</Label>
                        <Input
                          id="firstName"
                          name="firstName"
                          value={profileData.firstName}
                          onChange={handleChange}
                          placeholder="Enter your first name"
                        />
                      </div>

                      <div>
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input
                          id="lastName"
                          name="lastName"
                          value={profileData.lastName}
                          onChange={handleChange}
                          placeholder="Enter your last name"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6 mb-4 md:mb-6">
                      <div>
                        <Label htmlFor="gender">Gender</Label>
                        <select
                          id="gender"
                          name="gender"
                          value={profileData.gender || ''}
                          onChange={handleChange}
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-black ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          key={`gender-select-${profileData.gender}`} // Force re-render when gender changes
                        >
                          <option value="">Select Gender</option>
                          <option value="Male">Male</option>
                          <option value="Female">Female</option>
                        </select>
                      </div>

                      <div className="relative">
                        <Label htmlFor="category">Specialist</Label>
                        <div className="relative">
                          <Input
                            id="category"
                            name="category"
                            value={categorySearch}
                            onChange={(e) => {
                              setCategorySearch(e.target.value);
                              setShowCategoryDropdown(true);
                            }}
                            onFocus={() => setShowCategoryDropdown(true)}
                            placeholder="Search and select specialist..."
                            disabled={loadingCategories}
                            className="pr-10"
                            key={`category-input-${profileData.category}`} // Force re-render when category changes
                          />
                          {categorySearch && (
                            <button
                              type="button"
                              onClick={() => {
                                setCategorySearch("");
                                setProfileData((prev) => ({
                                  ...prev,
                                  category: "",
                                }));
                                setShowCategoryDropdown(false);
                              }}
                              className="absolute right-8 top-3 h-4 w-4 text-muted-foreground hover:text-red-500"
                            >
                              ×
                            </button>
                          )}
                          <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />

                          {showCategoryDropdown && !loadingCategories && (
                            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                              {categories
                                .filter((category) =>
                                  category.Name.toLowerCase().includes(
                                    categorySearch.toLowerCase()
                                  )
                                )
                                .map((category) => (
                                  <div
                                    key={category.CategoryID}
                                    className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                                    onClick={() => {
                                      setProfileData((prev) => ({
                                        ...prev,
                                        category: category.CategoryID,
                                      }));
                                      setCategorySearch(category.Name);
                                      setShowCategoryDropdown(false);
                                    }}
                                  >
                                    <div className="font-medium">
                                      {category.Name}
                                    </div>
                                    {category.ParentCategoryID && (
                                      <div className="text-xs text-muted-foreground">
                                        Subcategory of:{" "}
                                        {
                                          categories.find(
                                            (cat) =>
                                              cat.CategoryID ===
                                              category.ParentCategoryID
                                          )?.Name
                                        }
                                      </div>
                                    )}
                                  </div>
                                ))}
                              {categories.filter((category) =>
                                category.Name.toLowerCase().includes(
                                  categorySearch.toLowerCase()
                                )
                              ).length === 0 && (
                                <div className="px-3 py-2 text-sm text-muted-foreground">
                                  No specialists found
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                        {loadingCategories && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Loading specialists...
                          </p>
                        )}

                        {/* Click outside to close dropdown */}
                        {showCategoryDropdown && (
                          <div
                            className="fixed inset-0 z-5"
                            onClick={() => setShowCategoryDropdown(false)}
                          />
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6 mb-4 md:mb-6">
                      <div>
                        <Label htmlFor="email">Email Address</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={profileData.email}
                          onChange={handleChange}
                          disabled
                          className="bg-gray-50"
                          placeholder="Email address"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Email address cannot be changed
                        </p>

                      </div>

                      <div>
                        <Label htmlFor="phone">Phone Number</Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={profileData.phone}
                          onChange={handleChange}
                          disabled
                          className="bg-gray-50"
                          placeholder="Phone number"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Phone number cannot be changed
                        </p>

                      </div>
                    </div>
                    </div>

                    <Button type="submit" disabled={loading} className="bg-primary-dynamic text-white">
                      {loading ? (
                        <span className="flex items-center gap-2">
                          <svg
                            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Updating...
                        </span>
                      ) : (
                        <span className="flex items-center gap-2">
                          <Save className="h-4 w-4" />
                          Save Changes
                        </span>
                      )}
                    </Button>
                  </form>
                </div>
              </TabsContent>

              <TabsContent value="password" className="mt-4 bg-white rounded-lg shadow-sm">
                <div className="p-4 md:p-6 lg:p-8">
                  <h3 className="text-lg md:text-xl font-semibold mb-4 md:mb-6">Security Settings</h3>
                  <Card>
                  <form onSubmit={handleSubmit} className="p-4 md:p-6">
                    {success && (
                      <div className="bg-success/10 border border-success text-success px-4 py-3 rounded mb-4 md:mb-6">
                        Password updated successfully!
                      </div>
                    )}

                    <div className="space-y-4 mb-4 md:mb-6">
                      <div>
                        <Label htmlFor="currentPassword">
                          Current Password
                        </Label>
                        <div className="relative">
                          <Input
                            id="currentPassword"
                            name="currentPassword"
                            type={showPassword ? "text" : "password"}
                            value={profileData.currentPassword}
                            onChange={handleChange}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="newPassword">New Password</Label>
                        <div className="relative">
                          <Input
                            id="newPassword"
                            name="newPassword"
                            type={showPassword ? "text" : "password"}
                            value={profileData.newPassword}
                            onChange={handleChange}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="confirmPassword">
                          Confirm New Password
                        </Label>
                        <div className="relative">
                          <Input
                            id="confirmPassword"
                            name="confirmPassword"
                            type={showPassword ? "text" : "password"}
                            value={profileData.confirmPassword}
                            onChange={handleChange}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>

                    <Button
                      type="submit"
                      disabled={
                        loading ||
                        !profileData.currentPassword ||
                        !profileData.newPassword ||
                        !profileData.confirmPassword ||
                        profileData.newPassword !== profileData.confirmPassword
                      }
                    >
                      {loading ? (
                        <span className="flex items-center gap-2">
                          <svg
                            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Updating...
                        </span>
                      ) : (
                        <span className="flex items-center gap-2">
                          <Save className="h-4 w-4" />
                          Update Password
                        </span>
                      )}
                    </Button>
                    {error && (
                      <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded mb-6">
                        {error}
                      </div>
                    )}
                  </form>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
