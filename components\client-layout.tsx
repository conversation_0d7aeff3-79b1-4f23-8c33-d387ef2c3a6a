'use client';

import React from 'react';
import { Header } from '@/components/ui/header';
import { Footer } from '@/components/ui/footer';
import { WhatsAppButton } from '@/components/ui/whatsapp-button';
import { MobileBottomNav } from '@/components/ui/mobile-bottom-nav';
import { TabletNav } from '@/components/ui/tablet-nav';
import { Toaster } from '@/components/ui/toaster';
import { Providers } from '@/components/providers';
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';
import { Toaster as SonnerToaster } from 'sonner';
import { generateOrganizationStructuredData, generateWebsiteStructuredData } from '@/lib/structured-data';
import { StructuredData } from '@/components/seo/structured-data';
import Script from 'next/script';

interface ClientLayoutProps {
  children: React.ReactNode;
}

export function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <>
      {/* Google Tag Manager */}
      <Script
        id="gtm-script"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','GTM-KGBQQVTH');`
        }}
      />
      
      {/* Google Tag Manager (noscript) */}
      <noscript>
        <iframe
          src="https://www.googletagmanager.com/ns.html?id=GTM-KGBQQVTH"
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        />
      </noscript>
      
      {/* Structured Data */}
      <StructuredData data={generateOrganizationStructuredData()} />
      <StructuredData data={generateWebsiteStructuredData()} />
      
      <GoogleReCaptchaProvider
        reCaptchaKey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY!}
        scriptProps={{
          async: true,
          defer: true,
          appendTo: 'body',
          nonce: undefined,
        }}
        container={{
          parameters: {
            badge: 'inline',
            theme: 'light'
          }
        }}
      >
        <Providers>
          <Header />
          <main className="min-h-screen pb-16 md:pb-20 lg:pb-0">
            {children}
          </main>
          <Footer />
          <WhatsAppButton />
          {/* Mobile Navigation (phones only) */}
          <MobileBottomNav />
          {/* Tablet Navigation (tablet portrait only) */}
          <TabletNav />
          <Toaster />
          <SonnerToaster position="top-right" />
        </Providers>
      </GoogleReCaptchaProvider>
    </>
  );
}