'use client';

import { ProductBox } from '@/components/ui/product-box';
import { useSettings } from '@/contexts/settings-context';

interface ProductImage {
  id: string;
  url: string;
  alt: string;
}

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  discountedPrice?: number;
  images: ProductImage[];
  rating: number;
  categoryName: string;
  categorySlug: string;
  isNew?: boolean;
  inStock?: boolean;
}

interface ProductGridProps {
  products: Product[];
  gridType: string;
  isLoading?: boolean;
}

export function ProductGrid({ products, gridType, isLoading = false }: ProductGridProps) {
  const { t } = useSettings();

  // Define grid classes based on gridType
  const getGridClasses = () => {
    switch (gridType) {
      case 'grid-2':
        return 'grid grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-3 sm:gap-4';
      case 'grid-3':
        return 'grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-3 gap-3 sm:gap-4';
      case 'grid-4':
        return 'grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4';
      case 'list':
        return 'flex flex-col space-y-4';
      default:
        return 'grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4';
    }
  };

  // If loading, show skeleton
  if (isLoading) {
    return (
      <div className={getGridClasses()}>
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="bg-gray-100 animate-pulse rounded-md h-80"></div>
        ))}
      </div>
    );
  }

  // If no products, show message
  if (products.length === 0) {
    return (
      <div className="flex justify-center items-center py-12">
        <p className="text-lg text-muted-foreground">No products found</p>
      </div>
    );
  }

  // Render products in grid or list view
  return (
    <div className={getGridClasses()}>
      {products.map((product) => (
        <div key={product.id} className={gridType === 'list' ? 'w-full' : ''}>
          <ProductBox
            product={product}
            effect="opacity-0 group-hover:opacity-100"
            layout={gridType === 'list' ? 'list' : 'grid'}
          />
        </div>
      ))}
    </div>
  );
}
