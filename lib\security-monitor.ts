/**
 * Security monitoring and alerting system
 */

interface SecurityEvent {
  type: 'bot_detected' | 'rate_limit_exceeded' | 'suspicious_phone' | 'invalid_origin' | 'multiple_failures';
  phoneNumber?: string;
  ip: string;
  userAgent?: string;
  timestamp: number;
  details: string;
}

class SecurityMonitor {
  private events: SecurityEvent[] = [];
  private readonly maxEvents = 1000; // Keep last 1000 events

  /**
   * Log a security event
   */
  logEvent(event: Omit<SecurityEvent, 'timestamp'>) {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: Date.now()
    };

    this.events.push(securityEvent);

    // Keep only the last maxEvents
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Log to console (in production, send to monitoring service)
    console.warn('Security Event:', {
      type: event.type,
      ip: event.ip,
      details: event.details,
      timestamp: new Date(securityEvent.timestamp).toISOString()
    });

    // Check for patterns that require immediate attention
    this.checkForPatterns();
  }

  /**
   * Check for suspicious patterns
   */
  private checkForPatterns() {
    const recentEvents = this.getRecentEvents(5 * 60 * 1000); // Last 5 minutes

    // Check for multiple bot attempts from same IP
    const ipCounts = new Map<string, number>();
    recentEvents.forEach(event => {
      if (event.type === 'bot_detected') {
        ipCounts.set(event.ip, (ipCounts.get(event.ip) || 0) + 1);
      }
    });

    ipCounts.forEach((count, ip) => {
      if (count >= 5) {
        console.error(`ALERT: Multiple bot attempts from IP ${ip} (${count} attempts in 5 minutes)`);
        // In production, you might want to temporarily block this IP
      }
    });

    // Check for rate limit abuse
    const rateLimitEvents = recentEvents.filter(e => e.type === 'rate_limit_exceeded');
    if (rateLimitEvents.length >= 10) {
      console.error(`ALERT: High rate limit violations (${rateLimitEvents.length} in 5 minutes)`);
    }
  }

  /**
   * Get recent security events
   */
  getRecentEvents(timeWindowMs: number): SecurityEvent[] {
    const cutoff = Date.now() - timeWindowMs;
    return this.events.filter(event => event.timestamp > cutoff);
  }

  /**
   * Get security statistics
   */
  getStats(timeWindowMs: number = 60 * 60 * 1000): {
    totalEvents: number;
    eventsByType: Record<string, number>;
    topIPs: Array<{ ip: string; count: number }>;
  } {
    const recentEvents = this.getRecentEvents(timeWindowMs);
    
    const eventsByType: Record<string, number> = {};
    const ipCounts = new Map<string, number>();

    recentEvents.forEach(event => {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
      ipCounts.set(event.ip, (ipCounts.get(event.ip) || 0) + 1);
    });

    const topIPs = Array.from(ipCounts.entries())
      .map(([ip, count]) => ({ ip, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalEvents: recentEvents.length,
      eventsByType,
      topIPs
    };
  }

  /**
   * Check if an IP should be temporarily blocked
   */
  shouldBlockIP(ip: string): boolean {
    const recentEvents = this.getRecentEvents(15 * 60 * 1000); // Last 15 minutes
    const ipEvents = recentEvents.filter(event => event.ip === ip);
    
    // Block if more than 10 security events from same IP in 15 minutes
    return ipEvents.length >= 10;
  }

  /**
   * Clean up old events (call periodically)
   */
  cleanup() {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // Keep 24 hours
    this.events = this.events.filter(event => event.timestamp > cutoff);
  }
}

// Global security monitor instance
export const securityMonitor = new SecurityMonitor();

// Cleanup old events every hour
setInterval(() => {
  securityMonitor.cleanup();
}, 60 * 60 * 1000);