'use client';

import {
  Phone,
  Mail,
  Clock,
  MessageCircle,
  User,
  UserPlus,
  Heart,
  ShoppingCart,
  Menu,
  Search,
  ChevronDown,
  Sun,
  Moon,
  Globe,
  ChevronRight,
  X,
  Package,
  LogOut,
  MapPin,
  Home
} from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { toast } from 'sonner';
import { MakeApiCallAsync, Config, GetTokenForHeader } from "@/lib/api-helper";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Button } from './button';
import { useSettings } from '@/contexts/settings-context';
import { useUser } from '@/contexts/user-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { ColorPicker } from './color-picker';
import { TabletLandscapeDropdownNav, MobileDropdownNav } from './responsive-dropdown-nav';

type Category = {
  id: number;
  name: string;
  subcategories: string[];
};

export function Header() {
  const router = useRouter();
  const pathname = usePathname();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showMobileCategories, setShowMobileCategories] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [cartCount, setCartCount] = useState(0);
  const [wishlistCount, setWishlistCount] = useState(0);
  const [isTabletLandscape, setIsTabletLandscape] = useState(false);
  const cart = useCart();
  const wishlist = useWishlist();
  const { user, isLoggedIn, logout } = useUser();
  const { theme, language, toggleTheme, setLanguage, t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  
  // Use the current theme colors
  const primaryColor = currentTheme.primary;
  const primaryTextColor = currentTheme.primaryForeground;
  const destructiveColor = '#dc2626'; // Red color for error states
  const destructiveTextColor = '#ffffff'; // White text for error states

  const handleSearch = () => {
    // Check if we're already on the products page
    const isOnProductsPage = pathname === '/products' || pathname?.startsWith('/products?');
    
    const params = new URLSearchParams();

    if (searchTerm) {
      params.append('search', searchTerm);
    }

    if (selectedCategoryId) {
      params.append('category', selectedCategoryId.toString());
    }

    if (isOnProductsPage) {
      // If on products page, update URL without navigation (stay on same page)
      const newUrl = `/products?${params.toString()}`;
      window.history.pushState({}, '', newUrl);
      // Trigger a page refresh to update the search results
      window.location.reload();
    } else {
      // If not on products page, navigate to products page
      router.push(`/products?${params.toString()}`);
    }
  };

  const handleProductsNavigation = (e: React.MouseEvent) => {
    e.preventDefault();
    // Check if we're already on the products page
    const isOnProductsPage = pathname === '/products' || pathname?.startsWith('/products?');
    
    if (isOnProductsPage) {
      // If on products page, force reload
      window.location.href = '/products';
    } else {
      // If not on products page, navigate normally
      router.push('/products');
    }
  };

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const param = {
          "PageNumber": 1,
          "PageSize": 100,
          "SortColumn": "Name",
          "SortOrder": "ASC"
        };
        const token = await GetTokenForHeader();
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': token ? 'Bearer ' + token : ''
        };
        const categoriesResponse = await MakeApiCallAsync(Config.END_POINT_NAMES.GET_CATEGORIES_LIST, null, param, headers, "POST", true);

        if (categoriesResponse?.data?.data) {
          try {
            const parsedData = JSON.parse(categoriesResponse.data.data);
            if (Array.isArray(parsedData)) {
              // Create a map of parent categories
              const parentCategories = parsedData.filter(cat => !cat.ParentCategoryID);
              const childCategories = parsedData.filter(cat => cat.ParentCategoryID);

              // Format parent categories with their children
              const formattedCategories = parentCategories.map(parent => ({
                id: parent.CategoryID,
                name: parent.Name,
                subcategories: childCategories
                  .filter(child => child.ParentCategoryID === parent.CategoryID)
                  .map(child => child.Name)
              }));

              setCategories(formattedCategories);
            } else {
              console.error('Categories data is not an array:', parsedData);
              setCategories([]);
            }
          } catch (parseError) {
            console.error('Error parsing categories data:', parseError);
            setCategories([]);
          }
        } else if (categoriesResponse?.data?.errorMessage) {
          console.error('API Error:', categoriesResponse.data.errorMessage);
          setCategories([]);
        } else {
          console.error('Invalid or empty response from API');
          setCategories([]);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        setCategories([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Update cart count when cart items change
  useEffect(() => {
    if (cart && cart.items) {
      setCartCount(cart.totalItems);
    }
  }, [cart, cart?.items]);

  // Update wishlist count when wishlist items change
  useEffect(() => {
    if (wishlist) {
      setWishlistCount(wishlist.totalItems);
    }
  }, [wishlist, wishlist?.wishlistItems]);

  // Handle tablet landscape orientation detection
  useEffect(() => {
    const checkOrientation = () => {
      const isTabletSize = window.innerWidth >= 768 && window.innerWidth <= 1330;
      const isLandscape = window.innerHeight < window.innerWidth;
      const isTabletLandscapeMode = isTabletSize && isLandscape;
      setIsTabletLandscape(isTabletLandscapeMode);
      
      // Debug logging
      console.log('Screen dimensions:', window.innerWidth, 'x', window.innerHeight);
      console.log('Is tablet size:', isTabletSize);
      console.log('Is landscape:', isLandscape);
      console.log('Is tablet landscape mode:', isTabletLandscapeMode);
    };

    // Check on mount
    checkOrientation();

    // Add event listeners
    window.addEventListener('resize', checkOrientation);
    window.addEventListener('orientationchange', () => {
      setTimeout(checkOrientation, 100); // Small delay for orientation change
    });

    return () => {
      window.removeEventListener('resize', checkOrientation);
      window.removeEventListener('orientationchange', checkOrientation);
    };
  }, []);

  return (
    <header className="w-full">
      <Button
        variant="ghost"
        size="sm"
        className="fixed bottom-36 right-4 md:bottom-24 md:right-6 md:left-auto z-50 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 shadow-2xl rounded-2xl border-2 border-white/50 flex hover:scale-110 hover:-rotate-6 transition-all duration-300 hover:shadow-purple-500/50 group items-center justify-center w-14 h-14 md:w-16 md:h-16 hover:rounded-full"
        onClick={() => setShowColorPicker(true)}
      >
        <div className="relative">
          <div className="h-8 w-8 md:h-10 md:w-10 rounded-xl group-hover:rounded-full ring-2 ring-white/80 group-hover:ring-4 transition-all duration-300 shadow-inner" style={{ backgroundColor: primaryColor }} />
          <div className="absolute -top-2 -right-2 w-5 h-5 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full shadow-lg animate-spin group-hover:animate-pulse" />
          <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full animate-bounce" />
        </div>
      </Button>

      {/* Top Bar - Desktop Only */}
      <div className="hidden md:block py-2.5" style={{ backgroundColor: primaryColor, color: primaryTextColor }}>
        <div className="container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4">
          <div className="flex md:flex-row items-start justify-start gap-4 md:gap-8">
            <Link href="tel:009647836071686" className="flex items-center gap-2 hover:text-white/80">
              <Phone className="h-4 w-4" />
              <span className="text-xs md:text-sm">{t('phone')}</span>
            </Link>
            <Link href="mailto:<EMAIL>" className="flex items-center gap-2 hover:text-white/80">
              <Mail className="h-4 w-4" />
              <span className="text-xs md:text-sm">{t('email')}</span>
            </Link>
          </div>
          <div className="flex items-center gap-2 md:gap-4">
            <Button variant="ghost" size="sm" className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2" onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}>
              <Globe className="h-4 w-4" />
              <span className="text-sm">{language === 'en' ? 'العربية' : 'English'}</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:text-white/80 flex items-center gap-2"
              onClick={() => window.open(`https://wa.me/9647836071686?text=${encodeURIComponent('Hello! I would like to chat with you regarding your services.')}`, '_blank')}
            >
              <MessageCircle className="h-4 w-4" />
              <span className="text-sm">{t('liveChat')}</span>
            </Button>
            {isLoggedIn ? (
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span className="text-sm">Welcome, {user?.FirstName || user?.UserName}</span>
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2 bg-white border border-gray-200 shadow-lg" align="end">
                  <div className="space-y-1">
                    <Link href="/account">
                      <Button variant="ghost" size="sm" className="w-full justify-start hover:bg-muted/50">
                        <User className="h-4 w-4 mr-2" />
                        My Account
                      </Button>
                    </Link>
                    <Link href="/orders">
                      <Button variant="ghost" size="sm" className="w-full justify-start hover:bg-muted/50">
                        <Package className="h-4 w-4 mr-2" />
                        My Orders
                      </Button>
                    </Link>
                    <Link href="/addresses">
                      <Button variant="ghost" size="sm" className="w-full justify-start hover:bg-muted/50">
                        <MapPin className="h-4 w-4 mr-2" />
                        My Addresses
                      </Button>
                    </Link>
                    <Link href="/wishlist">
                      <Button variant="ghost" size="sm" className="w-full justify-start hover:bg-muted/50">
                        <Heart className="h-4 w-4 mr-2" />
                        Wishlist
                      </Button>
                    </Link>
                    <div className="border-t border-gray-100 my-1"></div>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="w-full justify-start"
                      style={{
                        color: destructiveColor,
                        '--hover-color': destructiveTextColor,
                        '--hover-bg': `${destructiveColor}10`
                      } as React.CSSProperties}
                      onClick={() => {
                        logout();
                        router.push('/');
                        toast.success('Logged out successfully');
                      }}
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      Logout
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            ) : (
              <>
                <Link href="/login">
                  <Button variant="ghost" size="sm" className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span className="text-sm">{t('login')}</span>
                  </Button>
                </Link>
                <Link href="/signup">
                  <Button variant="ghost" size="sm" className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2">
                    <UserPlus className="h-4 w-4" />
                    <span className="text-sm">{t('signUp')}</span>
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto py-4 px-4">
        {/* Mobile Layout - Show only on small screens */}
        {!isTabletLandscape && (
        <div className="lg:hidden">
          {/* Logo Row with Language Selector */}
          <div className="flex items-center justify-between mb-4">
            {/* Left spacer for perfect centering */}
            <div className="w-20"></div>

            {/* Centered Logo */}
            <Link href="/" className="flex items-center gap-2">
              <div className="text-[#1B3764] flex items-center gap-2">
                <img src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`} alt="Logo" className="h-12 md:h-14 w-auto" />
              </div>
            </Link>

            {/* Language Selector with Flag */}
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 px-3 py-2 rounded-full border border-gray-200 hover:bg-gray-50"
              onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}
            >
              <span className="text-lg">
                {language === 'en' ? '🇺🇸' : '🇮🇶'}
              </span>
              <span className="text-sm font-medium">
                {language === 'en' ? 'EN' : 'AR'}
              </span>
            </Button>
          </div>

          {/* Search Row - Improved for tablets */}
          <div className="w-full">
            <div className="flex items-center gap-2 border rounded-full px-4 py-3 md:py-4 bg-background shadow-sm">
              <input
                type="text"
                placeholder={t('products') || 'البحث عن المنتجات...'}
                className="bg-transparent border-none focus:outline-none text-sm md:text-base flex-1 placeholder:text-gray-400"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button
                variant="ghost"
                className="h-8 w-8 md:h-10 md:w-10 p-0 hover:bg-accent/80 transition-colors"
                style={{ color: primaryColor }}
                onClick={handleSearch}
              >
                <Search className="h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </div>
          </div>
        </div>
        )}
        
        {/* Desktop Layout - Show on large screens */}
        {!isTabletLandscape && (
          <div 
            className={`desktop-layout ${isTabletLandscape ? 'hidden' : 'hidden lg:flex'} items-center justify-between gap-2 min-w-0 overflow-hidden`}
            style={{ display: isTabletLandscape ? 'none' : '' }}
          >
          {/* Logo and Search */}
          <div className="flex items-center gap-2 lg:gap-4 flex-1 min-w-0 overflow-hidden">
            {/* Logo */}
            <Link href="/" className="flex items-center gap-2">
              <div className="text-[#1B3764] flex items-center gap-2">
                <img 
                  src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`} 
                  alt="Logo" 
                  className="h-16 xl:h-16 header-logo w-auto" 
                />
              </div>
            </Link>

            {/* Search and Category - Improved for tablets */}
            <div className="flex items-center gap-2 border rounded-full px-2 lg:px-3 py-1.5 flex-1 max-w-sm lg:max-w-md xl:max-w-lg header-search ml-2 lg:ml-4 overflow-hidden">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" className="h-8 flex items-center gap-1 px-2 flex-shrink-0 min-w-0 overflow-hidden">
                    <span className="text-muted-foreground text-sm truncate max-w-20">{selectedCategory || selectedSubcategory || t('category')}</span>
                    <ChevronDown className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-64 p-0 bg-white border border-gray-200 shadow-lg" align="start">
                  <div className="max-h-[300px] overflow-auto">
                    {isLoading ? (
                      <div className="p-4 text-center text-muted-foreground">{t('loadingCategories')}</div>
                    ) : (
                      <div className="grid">
                        {categories.map((category) => (
                          <div key={category.id} className="group">
                            <button className="w-full px-4 py-2 text-left hover:bg-muted/50" onClick={() => {
                              setSelectedCategory(category.name);
                              setSelectedCategoryId(category.id);
                              setSelectedSubcategory(null);
                            }}>
                              {category.name}
                            </button>
                            <div className="hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border border-gray-200">
                              {category.subcategories.map((sub, index) => (
                                <button
                                  key={index}
                                  className="w-full px-4 py-2 text-left hover:bg-muted/50"
                                  onClick={() => {
                                    setSelectedSubcategory(sub);
                                    setSelectedCategory(null);
                                    // Keep the parent category ID for search purposes
                                    setSelectedCategoryId(category.id);
                                  }}
                                >
                                  {sub}
                                </button>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </PopoverContent>
              </Popover>
              <div className="h-5 w-px bg-border mx-2 flex-shrink-0" />
              <input
                type="text"
                placeholder={t('products')}
                className="bg-transparent border-none focus:outline-none text-sm flex-1 min-w-0"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button variant="ghost" className="h-8 w-8 p-0 flex-shrink-0" onClick={handleSearch}>
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Navigation - Improved spacing for tablets, hidden on small tablets */}
          <div className="hidden lg:flex items-center gap-2 lg:gap-3 xl:gap-6 header-nav flex-shrink-0 overflow-hidden">
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('home')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/hot-deals" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('hotDeals')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <a href="/products" onClick={handleProductsNavigation} className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('products') || 'Products'}
                    </a>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/payment-methods" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('paymentMethods')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/follow-us" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('followUs')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/about" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('aboutUs')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/contact" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('contactUs')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Cart, Wishlist Icons and Mobile Menu Button */}
          <div className="flex items-center gap-2 lg:gap-4 flex-shrink-0">
            {/* Dropdown Navigation for Medium Tablets */}
            <div className="hidden md:block lg:hidden">
              <MobileDropdownNav className="tablet-dropdown-nav" />
            </div>
            
            {/* Cart and Wishlist - Visible on tablet and desktop */}
            <div className="hidden sm:flex items-center gap-2 lg:gap-4 flex-shrink-0">
              <Link href="/wishlist">
                <Button variant="ghost" size="icon" className="relative">
                  <Heart className="h-5 w-5" style={{ color: primaryColor }} />
                  <span className="absolute -top-1 -right-0.5 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center" style={{ backgroundColor: primaryColor }}>
                    {wishlistCount}
                  </span>
                </Button>
              </Link>
              <Link href="/cart">
                <Button variant="ghost" size="icon" className="relative">
                  <ShoppingCart className="h-5 w-5" style={{ color: primaryColor }} />
                  <span className="absolute -top-1 -right-0.5 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center" style={{ backgroundColor: primaryColor }}>
                    {cartCount}
                  </span>
                </Button>
              </Link>
            </div>
            
            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="sm:hidden"
              onClick={() => setShowMobileMenu(!showMobileMenu)}
              aria-label="Toggle menu"
            >
              {showMobileMenu ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>
        )}

        {/* Tablet Landscape Layout - Show only on tablet landscape */}
        {isTabletLandscape && (
          <div className="tablet-landscape-layout w-full py-2 px-4 border-b">
            {/* First Row: Logo, Navigation, and Actions */}
            <div className="flex items-center justify-between gap-2 min-w-0 w-full mb-2">
              {/* Logo */}
              <Link href="/" className="flex-shrink-0">
                <img 
                  src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`} 
                  alt="Logo" 
                  className="h-10 w-auto" 
                />
              </Link>

              {/* Navigation Dropdown Menu */}
              <div className="flex items-center gap-1 flex-1 justify-center max-w-md overflow-hidden">
                <TabletLandscapeDropdownNav 
                  t={t as (key: string) => string}
                  handleProductsNavigation={handleProductsNavigation}
                  className="responsive-dropdown-nav" 
                />
              </div>

              {/* Actions (Cart and Wishlist) */}
               <div className="flex items-center gap-2 flex-shrink-0">
                 {/* Cart and Wishlist */}
              <Link href="/wishlist">
                <Button variant="ghost" size="icon" className="relative h-8 w-8">
                  <Heart className="h-4 w-4" style={{ color: primaryColor }} />
                  {wishlistCount > 0 && (
                    <span className="absolute -top-0.5 -right-0.5 text-white text-xs rounded-full h-3 w-3 flex items-center justify-center text-[8px]" style={{ backgroundColor: primaryColor }}>
                      {wishlistCount > 9 ? '9+' : wishlistCount}
                    </span>
                  )}
                </Button>
              </Link>
              <Link href="/cart">
                <Button variant="ghost" size="icon" className="relative h-8 w-8">
                  <ShoppingCart className="h-4 w-4" style={{ color: primaryColor }} />
                  {cartCount > 0 && (
                    <span className="absolute -top-0.5 -right-0.5 text-white text-xs rounded-full h-3 w-3 flex items-center justify-center text-[8px]" style={{ backgroundColor: primaryColor }}>
                      {cartCount > 9 ? '9+' : cartCount}
                    </span>
                  )}
                </Button>
              </Link>
                </div>
            </div>

            {/* Second Row: Search Bar */}
            <div className="flex justify-center w-full">
              <div className="flex items-center gap-2 border rounded-full px-4 py-2 w-full max-w-md">
                <Search className="h-4 w-4 text-gray-400 flex-shrink-0" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="bg-transparent border-none focus:outline-none text-sm flex-1 min-w-0"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch();
                      e.currentTarget.blur();
                    }
                  }}
                />
                {searchTerm && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-5 w-5 p-0 text-gray-400 hover:text-gray-600"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedCategory(null);
                      setSelectedCategoryId(null);
                      setSelectedSubcategory(null);
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
      {/* Mobile Navigation Menu */}
      {showMobileMenu && (
        <div className="md:hidden bg-white border-t border-gray-200 shadow-lg">
          <nav className="px-4 py-4 space-y-2">
            <Link
              href="/"
              className="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
              onClick={() => setShowMobileMenu(false)}
            >
              {t('home')}
            </Link>
            <Link
              href="/hot-deals"
              className="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
              onClick={() => setShowMobileMenu(false)}
            >
              {t('hotDeals')}
            </Link>
            <a
              href="/products"
              className="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
              onClick={(e) => {
                setShowMobileMenu(false);
                handleProductsNavigation(e);
              }}
            >
              {t('products') || 'Products'}
            </a>
            <Link
              href="/payment-methods"
              className="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
              onClick={() => setShowMobileMenu(false)}
            >
              {t('paymentMethods')}
            </Link>
            <Link
              href="/follow-us"
              className="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
              onClick={() => setShowMobileMenu(false)}
            >
              {t('followUs')}
            </Link>
            <Link
              href="/about"
              className="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
              onClick={() => setShowMobileMenu(false)}
            >
              {t('aboutUs')}
            </Link>
            <Link
              href="/contact"
              className="block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
              onClick={() => setShowMobileMenu(false)}
            >
              {t('contactUs')}
            </Link>
            
            {/* Mobile Cart and Wishlist Links */}
            <div className="border-t border-gray-200 pt-4 mt-4">
              <Link
                href="/wishlist"
                className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
                onClick={() => setShowMobileMenu(false)}
              >
                <Heart className="h-5 w-5" />
                <span>Wishlist</span>
                <span className="ml-auto text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" style={{ backgroundColor: primaryColor }}>
                  {wishlistCount}
                </span>
              </Link>
              <Link
                href="/cart"
                className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
                onClick={() => setShowMobileMenu(false)}
              >
                <ShoppingCart className="h-5 w-5" />
                <span>Cart</span>
                <span className="ml-auto text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" style={{ backgroundColor: primaryColor }}>
                  {cartCount}
                </span>
              </Link>
            </div>
            
            {/* Mobile User Menu */}
            <div className="border-t border-gray-200 pt-4 mt-4">
              {isLoggedIn ? (
                <>
                  <div className="px-4 py-2 text-sm text-gray-500">
                    Welcome, {user?.FirstName || user?.UserName}
                  </div>
                  <Link
                    href="/account"
                    className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
                    onClick={() => setShowMobileMenu(false)}
                  >
                    <User className="h-5 w-5" />
                    <span>My Account</span>
                  </Link>
                  <Link
                    href="/orders"
                    className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
                    onClick={() => setShowMobileMenu(false)}
                  >
                    <Package className="h-5 w-5" />
                    <span>My Orders</span>
                  </Link>
                  <Link
                    href="/addresses"
                    className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
                    onClick={() => setShowMobileMenu(false)}
                  >
                    <MapPin className="h-5 w-5" />
                    <span>My Addresses</span>
                  </Link>
                  <button
                    className="flex items-center gap-3 w-full px-4 py-3 rounded-lg transition-colors"
                    style={{
                      color: destructiveColor,
                      backgroundColor: 'transparent'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = `${destructiveColor}10`;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                    onClick={() => {
                      logout();
                      setShowMobileMenu(false);
                      router.push('/');
                      toast.success('Logged out successfully');
                    }}
                  >
                    <LogOut className="h-5 w-5" />
                    <span>Logout</span>
                  </button>
                </>
              ) : (
                <>
                  <Link
                    href="/login"
                    className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
                    onClick={() => setShowMobileMenu(false)}
                  >
                    <User className="h-5 w-5" />
                    <span>{t('login')}</span>
                  </Link>
                  <Link
                    href="/signup"
                    className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors"
                    onClick={() => setShowMobileMenu(false)}
                  >
                    <UserPlus className="h-5 w-5" />
                    <span>{t('signUp')}</span>
                  </Link>
                </>
              )}
            </div>
          </nav>
        </div>
      )}
      
      {/* Tablet Side Menu - Removed since we now use dropdown navigation */}
      
      {showColorPicker && (
        <ColorPicker
          onColorSelect={() => {
            setShowColorPicker(false);
          }}
          onClose={() => setShowColorPicker(false)}
        />
      )}
    </header>
  );
}