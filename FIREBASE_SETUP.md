# Firebase Authentication Setup Guide

## Error: `auth/captcha-check-failed` - Hostname match not found

This error occurs when your current domain is not authorized in Firebase Console.

## Quick Fix Steps:

### 1. **Firebase Console Configuration**

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to **Authentication** → **Settings** → **Authorized domains**
4. Add your domains:

#### For AWS Amplify:
```
your-app-name.amplifyapp.com
```

#### For Development:
```
localhost
127.0.0.1
```

#### For Production (if custom domain):
```
codemedicalapps.com
www.codemedicalapps.com
```

### 2. **Environment Variables**

Make sure these are set in your deployment environment:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

### 3. **AWS Amplify Environment Variables**

In AWS Amplify Console:
1. Go to your app
2. Click **Environment variables** in the left sidebar
3. Add the Firebase environment variables listed above

## Common Domains to Add:

### Development:
- `localhost`
- `127.0.0.1`

### AWS Amplify:
- `*.amplifyapp.com` (or your specific subdomain)
- `main.your-app-id.amplifyapp.com`

### Production:
- Your custom domain (e.g., `codemedicalapps.com`)
- `www.codemedicalapps.com`

## Testing the Fix:

1. **Check Console Logs**: Look for domain authorization warnings
2. **Test Authentication**: Try phone number verification
3. **Verify reCAPTCHA**: Ensure invisible reCAPTCHA loads properly

## Troubleshooting:

### If you still get errors:

1. **Clear Browser Cache**: Hard refresh (Ctrl+Shift+R)
2. **Check Network Tab**: Look for failed reCAPTCHA requests
3. **Verify Firebase Config**: Ensure all environment variables are correct
4. **Test in Incognito**: Rule out browser extension issues

### Error Messages and Solutions:

| Error | Solution |
|-------|----------|
| `auth/captcha-check-failed` | Add domain to Firebase authorized domains |
| `auth/invalid-app-credential` | Check Firebase API key and project ID |
| `auth/network-request-failed` | Check internet connection and Firebase status |

## Security Notes:

- Only add domains you control to authorized domains
- Use environment variables for sensitive Firebase config
- Test thoroughly after adding new domains

## Support:

If issues persist:
1. Check Firebase Console → Authentication → Usage for error details
2. Verify your Firebase project is active and billing is set up (if required)
3. Contact Firebase support through the console