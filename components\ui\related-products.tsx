'use client';

import { useEffect, useState } from 'react';
import { ProductBox } from '@/components/ui/product-box';
import { useSettings } from '@/contexts/settings-context';
import { Card } from '@/components/ui/card';
import { Config } from '@/lib/config';
import { Skeleton } from '@/components/ui/skeleton';

interface ProductImage {
  id: string;
  url: string;
  alt: string;
}

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  discountedPrice?: number;
  images: ProductImage[];
  rating: number;
  categoryName: string;
  categorySlug: string;
  isNew?: boolean;
  inStock?: boolean;
}

interface RelatedProductsProps {
  productId: string;
  categoryId: string;
  effect?: string;
}

export function RelatedProducts({ productId, categoryId, effect }: RelatedProductsProps) {
  const { t } = useSettings();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRelatedProducts = async () => {
      setLoading(true);
      try {
        // TODO: Replace with actual API call
        const response = await fetch(
          `${Config.ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-related-products-list?productId=${productId}&categoryId=${categoryId}`
        );
        let data = [];
        const text = await response.text();
        if (text) {
          try {
            data = JSON.parse(text);
          } catch (jsonError) {
            console.error('Invalid JSON in related products response:', jsonError);
            data = [];
          }
        } else {
          data = [];
        }
        setProducts(data);
      } catch (error) {
        console.error('Error fetching related products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRelatedProducts();
  }, [productId, categoryId]);

  if (loading) {
    return (
      <div className="space-y-4 px-4 sm:px-6">
        <h2 className="text-xl sm:text-2xl font-bold">Related Products</h2>
        <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="p-3 sm:p-4">
              <Skeleton className="h-32 sm:h-40 w-full mb-3 sm:mb-4" />
              <Skeleton className="h-3 sm:h-4 w-2/3 mb-2" />
              <Skeleton className="h-3 sm:h-4 w-1/2" />
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (products.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4 px-4 sm:px-6">
      <h2 className="text-xl sm:text-2xl font-bold">Related Products</h2>
      <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4">
        {products.map((product) => (
          <ProductBox
            key={product.id}
            product={product}
            effect={effect}
          />
        ))}
      </div>
    </div>
  );
}