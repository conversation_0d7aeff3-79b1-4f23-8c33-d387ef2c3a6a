'use client';

import { toast } from 'sonner';
import { <PERSON><PERSON>ircle, ShoppingCart, X } from 'lucide-react';
import { Button } from './button';
import { useColorThemeContext } from '@/contexts/color-theme-context';

// Helper function to construct proper image URL
const constructImageUrl = (imageUrl: string): string => {
  if (!imageUrl) {
    return '/placeholder.svg';
  }
  
  // If it's already a full URL, return as is
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }
  
  // If it's a placeholder or public asset, return as is
  if (imageUrl.startsWith('/placeholder') || imageUrl.startsWith('/images/') || imageUrl.startsWith('/assets/')) {
    return imageUrl;
  }
  
  // For API images, construct the full URL
  const baseUrl = process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'https://admin.codemedicalapps.com';
  const normalizedBaseUrl = baseUrl.replace(/\/$/, '');
  // Normalize path (ensure it starts with exactly one slash)
  let normalizedPath = imageUrl.startsWith('/') ? imageUrl : `/${imageUrl}`;
  // Remove any double slashes in the path
  normalizedPath = normalizedPath.replace(/\/+/g, '/');
  const finalUrl = `${normalizedBaseUrl}${normalizedPath}`;
  
  return finalUrl;
};

interface ModernToastProps {
  productName: string;
  quantity: number;
  productImage?: string;
  onViewCart?: () => void;
  onClose?: () => void;
}

export const showModernAddToCartToast = ({
  productName,
  quantity,
  productImage,
  onViewCart,
  primaryColor = '#3b82f6', // Default fallback color
}: Omit<ModernToastProps, 'onClose'> & { primaryColor?: string }) => {
  const toastId = toast.custom(
    (t) => (
      <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-md w-full">
        <div className="flex items-start gap-3">
          {/* Success Icon */}
          <div className="flex-shrink-0">
            <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: `${primaryColor}20` }}>
              <CheckCircle className="w-5 h-5" style={{ color: primaryColor }} />
            </div>
          </div>
          
          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 mb-1">
                  Added to cart
                </p>
                <p className="text-sm text-gray-600 line-clamp-2">
                  {quantity} × {productName}
                </p>
              </div>
              
              {/* Product Image */}
              {productImage && (
                <div className="flex-shrink-0 ml-3">
                  <img
                    src={constructImageUrl(productImage)}
                    alt={productName}
                    className="w-12 h-12 rounded-md object-cover border border-gray-200"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.onerror = null; // Prevent infinite loop
                      target.src = '/placeholder.svg';
                    }}
                  />
                </div>
              )}
            </div>
            
            {/* Action Buttons */}
            <div className="flex items-center gap-2 mt-3">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  toast.dismiss(t);
                  onViewCart?.();
                }}
                className="h-8 text-xs"
              >
                <ShoppingCart className="w-3 h-3 mr-1" />
                View Cart
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => toast.dismiss(t)}
                className="h-8 text-xs text-gray-500 hover:text-gray-700"
              >
                Continue Shopping
              </Button>
            </div>
          </div>
          
          {/* Close Button */}
          <button
            onClick={() => toast.dismiss(t)}
            className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    ),
    {
      duration: 5000, // Show for 5 seconds instead of 2
      position: 'top-right',
    }
  );

  return toastId;
};

// Alternative shorter duration version
export const showQuickAddToCartToast = ({
  productName,
  quantity,
}: {
  productName: string;
  quantity: number;
}) => {
  return toast.success(
    <div className="flex items-center gap-2">
      <CheckCircle className="w-4 h-4 text-green-600" />
      <span>{quantity} × {productName} added to cart</span>
    </div>,
    {
      duration: 3000, // 3 seconds instead of 2
    }
  );
};