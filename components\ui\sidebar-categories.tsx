'use client';

import { useEffect, useState } from 'react';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';
import { ChevronDown, ChevronRight, Package, Menu, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from './button';

type Category = {
  id: number;
  name: string;
  subcategories: { id: number; name: string }[];
};

export function SidebarCategories() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [expandedCategories, setExpandedCategories] = useState<number[]>([]);
  const [isOpen, setIsOpen] = useState(true);
  const [selectedParent, setSelectedParent] = useState<Category | null>(null);
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const param = {
          "PageNumber": 1,
          "PageSize": 100,
          "SortColumn": "Name",
          "SortOrder": "ASC"
        };
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer ' + localStorage.getItem('token')
        };
        const categoriesResponse = await MakeApiCallAsync(Config.END_POINT_NAMES.GET_CATEGORIES_LIST, null, param, headers, "POST", true);

        if (categoriesResponse?.data?.data) {
          try {
            const parsedData = JSON.parse(categoriesResponse.data.data);
            if (Array.isArray(parsedData)) {
              const parentCategories = parsedData.filter(cat => !cat.ParentCategoryID);
              const childCategories = parsedData.filter(cat => cat.ParentCategoryID);

              const formattedCategories = parentCategories.map(parent => ({
                id: parent.CategoryID,
                name: parent.Name,
                subcategories: childCategories
                  .filter(child => child.ParentCategoryID === parent.CategoryID)
                  .map(child => ({ id: child.CategoryID, name: child.Name }))
              }));

              setCategories(formattedCategories);
            } else {
              console.error('Categories data is not an array:', parsedData);
              setCategories([]);
            }
          } catch (parseError) {
            console.error('Error parsing categories data:', parseError);
            setCategories([]);
          }
        } else if (categoriesResponse?.data?.errorMessage) {
          console.error('API Error:', categoriesResponse.data.errorMessage);
          setCategories([]);
        } else {
          console.error('Invalid or empty response from API');
          setCategories([]);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        setCategories([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const toggleCategory = (category: Category) => {
    setSelectedParent(category);
  };

  const handleBack = () => {
    setSelectedParent(null);
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-4 bg-gradient-to-b from-background to-accent/5 min-h-screen border-r">
        {[...Array(5)].map((_, index) => (
          <div key={index} className="space-y-2">
            <div className="h-8 bg-accent/10 animate-pulse rounded-lg w-3/4" />
            <div className="h-6 bg-accent/5 animate-pulse rounded-lg w-1/2 ml-4" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="hidden md:block">
      <Button
        variant="ghost"
        size="icon"
        className="absolute left-4 top-4 z-50 md:hidden"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Menu className="h-6 w-6" />
      </Button>
      <motion.div
        className="bg-background md:relative w-full md:w-48 lg:w-64 shadow-sm"
        style={{ background: `linear-gradient(135deg, ${primaryColor}30, ${primaryColor}20, ${primaryColor}10)` }}
        initial={{ x: 0 }}
        animate={{ x: isOpen ? 0 : '-100%' }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
      >
        <div className="p-6 space-y-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Package className="h-6 w-6" style={{ color: primaryColor }} />
              <h2 className="text-xl font-semibold" style={{ color: primaryColor }}>{t('categories')}</h2>
            </div>
            {selectedParent && (
              <Button
                variant="ghost"
                size="icon"
                onClick={handleBack}
                className="hover:bg-accent/80"
              >
                <X className="h-5 w-5" />
              </Button>
            )}
          </div>

          <AnimatePresence mode="wait">
            {!selectedParent ? (
              <motion.div
                key="parent-list"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.2 }}
                className="space-y-2"
              >
                {[...categories].reverse().map((category) => (
                  <motion.div
                    key={category.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    {category.subcategories.length > 0 ? (
                      <button
                        onClick={() => toggleCategory(category)}
                        className={cn(
                          "w-full flex items-center justify-between p-4 transition-all duration-200 rounded-lg",
                          "bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] relative overflow-hidden"
                        )}
                      >
                        <span className="text-sm font-medium">{category.name}</span>
                        <ChevronRight className="h-4 w-4" />
                      </button>
                    ) : (
                      <Link
                        href={`/products?category=${category.id}`}
                        className={cn(
                          "w-full flex items-center justify-between p-4 transition-all duration-200 rounded-lg",
                          "bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] relative overflow-hidden"
                        )}
                      >
                        <span className="text-sm font-medium">{category.name}</span>
                      </Link>
                    )}
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              <motion.div
                key="subcategory-list"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-2 max-h-[432px] overflow-y-auto overflow-x-hidden"
                style={{ maxHeight: 'calc(9 * 48px)' }}
              >
                {selectedParent.subcategories.sort((a, b) => a.name.localeCompare(b.name)).map((subcategory, index) => (
                  <motion.div
                    key={subcategory.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <Link
                      href={`/products?category=${subcategory.id}`}
                      className={cn(
                        "block px-4 py-3 text-sm transition-all duration-200",
                        "bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] rounded-lg",
                        "relative overflow-hidden group"
                      )}
                    >
                      <span className="relative z-10 flex items-center gap-2">
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                        {subcategory.name}
                      </span>
                    </Link>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </div>
  );
}