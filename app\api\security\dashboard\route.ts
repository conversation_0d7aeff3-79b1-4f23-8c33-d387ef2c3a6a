import { NextRequest, NextResponse } from 'next/server';
import { securityMonitor } from '@/lib/security-monitor';
import { smsRateLimiter } from '@/lib/rate-limiter';

export async function GET(request: NextRequest) {
  try {
    // Simple authentication check (in production, use proper auth)
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.SECURITY_DASHBOARD_TOKEN}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get security statistics for different time windows
    const last15Min = securityMonitor.getStats(15 * 60 * 1000);
    const lastHour = securityMonitor.getStats(60 * 60 * 1000);
    const last24Hours = securityMonitor.getStats(24 * 60 * 60 * 1000);

    // Get recent security events
    const recentEvents = securityMonitor.getRecentEvents(60 * 60 * 1000); // Last hour

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      statistics: {
        last15Minutes: last15Min,
        lastHour: lastHour,
        last24Hours: last24Hours
      },
      recentEvents: recentEvents.slice(-50), // Last 50 events
      rateLimiter: {
        // Note: This is a simplified view, in production you'd want more detailed stats
        status: 'active'
      }
    });

  } catch (error) {
    console.error('Security dashboard error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}