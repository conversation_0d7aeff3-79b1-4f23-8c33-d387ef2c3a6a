'use client';

import { useState, useEffect } from 'react';
import { useMediaQuery } from '@/hooks/use-media-query';

export function ViewportInfo() {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  
  // Media queries for testing
  const isDesktop = useMediaQuery('(min-width: 1330px)');
  const isTabletLandscape = useMediaQuery('(min-width: 768px) and (max-width: 1329px)');
  const isTablet = useMediaQuery('(min-width: 768px) and (max-width: 1023px)');
  const isMobile = useMediaQuery('(max-width: 767px)');

  useEffect(() => {
    const updateDimensions = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  const getDeviceType = () => {
    if (isDesktop) return 'Desktop';
    if (isTabletLandscape) return 'Tablet Landscape';
    if (isTablet) return 'Tablet';
    if (isMobile) return 'Mobile';
    return 'Unknown';
  };

  return (
    <div className="fixed top-4 right-4 bg-black/80 text-white p-3 rounded-lg text-sm z-50 font-mono">
      <div>Viewport: {dimensions.width} × {dimensions.height}</div>
      <div>Device: {getDeviceType()}</div>
      <div className="text-xs mt-1 opacity-75">
        Target: 768px-1329px for tablet landscape
      </div>
    </div>
  );
}
