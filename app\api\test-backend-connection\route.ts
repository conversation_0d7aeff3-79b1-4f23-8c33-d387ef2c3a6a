import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing backend connection...');
    
    // Test environment variables
    const adminBaseUrl = process.env.ADMIN_BASE_URL;
    const nextPublicAdminBaseUrl = process.env.NEXT_PUBLIC_ADMIN_BASE_URL;
    const fallbackUrl = 'https://admin.codemedicalapps.com';
    
    const rawBase = adminBaseUrl || nextPublicAdminBaseUrl || fallbackUrl;
    const base = rawBase.endsWith('/') ? rawBase.slice(0, -1) : rawBase;
    
    console.log('🔧 Environment check:', {
      ADMIN_BASE_URL: adminBaseUrl ? 'SET' : 'NOT SET',
      NEXT_PUBLIC_ADMIN_BASE_URL: nextPublicAdminBaseUrl ? 'SET' : 'NOT SET',
      rawBase,
      normalizedBase: base
    });
    
    // Test basic connectivity to backend
    const testUrl = `${base}/api/v1/verification/store`;
    console.log('📤 Testing connection to:', testUrl);
    
    const testPayload = {
      PhoneNumber: '+905382885821',
      VerificationCode: '123456',
      ExpirationMinutes: 10,
      IPAddress: 'test',
      UserAgent: 'test-connection'
    };
    
    console.log('📤 Test payload:', JSON.stringify(testPayload, null, 2));

    const payloadString = JSON.stringify(testPayload);
    const response = await fetch(testUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': payloadString.length.toString(),
        'User-Agent': 'NextJS-Frontend/1.0',
        'Accept': 'application/json',
      },
      body: payloadString
    });
    
    console.log('📥 Backend response status:', response.status);
    console.log('📥 Backend response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('📥 Backend response body:', responseText);
    
    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = responseText;
    }
    
    return NextResponse.json({
      success: true,
      test: 'backend-connection',
      environment: {
        ADMIN_BASE_URL: adminBaseUrl ? 'SET' : 'NOT SET',
        NEXT_PUBLIC_ADMIN_BASE_URL: nextPublicAdminBaseUrl ? 'SET' : 'NOT SET',
        normalizedBase: base
      },
      backend: {
        url: testUrl,
        status: response.status,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries()),
        body: responseData
      }
    });
    
  } catch (error) {
    console.error('❌ Backend connection test failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json({
      success: false,
      test: 'backend-connection',
      error: errorMessage,
      environment: {
        ADMIN_BASE_URL: process.env.ADMIN_BASE_URL ? 'SET' : 'NOT SET',
        NEXT_PUBLIC_ADMIN_BASE_URL: process.env.NEXT_PUBLIC_ADMIN_BASE_URL ? 'SET' : 'NOT SET'
      }
    }, { status: 500 });
  }
}
