'use client';

import { X, Search, ChevronRight } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';

interface MobileCategoriesProps {
  onClose: () => void;
}

interface Category {
  CategoryID: string | number;
  ParentCategoryID: string | number | null;
  Name: string;
  Icon?: string;
  AttachmentURL?: string;
}

const categoryIcons = [
  '👕', '📱', '💻', '🏠', '❤️', '⭐', '🎁', '🎵', '📷', '🎮',
  '📚', '☕', '🛍️', '⌚', '🎧', '🚚', '🏆', '⚡', '☀️', '🌙'
];

export function MobileCategoriesNew({ onClose }: MobileCategoriesProps) {
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // Fetch categories on component mount
  useEffect(() => {
    if (categories.length === 0) {
      fetchCategories();
    }
  }, []);

  // Close modal when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        handleClose();
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Prevent body scroll when modal is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  const fetchCategories = async () => {
    setIsLoading(true);
    try {
      const response = await MakeApiCallAsync(
        Config.END_POINT_NAMES.GET_CATEGORIES_LIST,
        Config.COMMON_CONTROLLER_SUB_URL,
        {},
        {},
        'GET'
      );

      if (response && response.data) {
        // Ensure we have an array of categories
        const categoriesData = Array.isArray(response.data) ? response.data : [];
        const categoriesWithIcons = categoriesData.map((cat: Category, index: number) => ({
          ...cat,
          Icon: categoryIcons[index % categoryIcons.length] || '📦' // Default icon if none available
        }));
        
        setCategories(categoriesWithIcons);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getChildCategories = (parentId: string | number) => {
    return categories.filter(cat => cat.ParentCategoryID === parentId);
  };

  const handleCategorySelect = (category: Category) => {
    const hasChildren = categories.some(cat => cat.ParentCategoryID === category.CategoryID);
    if (hasChildren) {
      setSelectedCategory(category);
    } else {
      // Check if we're already on the products page
      const pathname = window.location.pathname;
      const isOnProductsPage = pathname === '/products' || pathname.startsWith('/products');
      
      if (isOnProductsPage) {
        // If on products page, force reload with new category
        window.location.href = `/products/?category=${category.CategoryID}`;
      } else {
        // If not on products page, navigate normally
        router.push(`/products/?category=${category.CategoryID}`);
      }
      onClose();
    }
  };

  const handleBack = () => {
    setSelectedCategory(null);
  };

  const handleClose = () => {
    setSelectedCategory(null);
    setSearchQuery('');
    onClose();
  };

  const filteredCategories = categories.filter(cat => {
    const matchesSearch = cat.Name.toLowerCase().includes(searchQuery.toLowerCase());
    const isChild = selectedCategory 
      ? cat.ParentCategoryID === selectedCategory.CategoryID 
      : !cat.ParentCategoryID;
    return matchesSearch && isChild;
  });

  return (
    <div className="fixed inset-0 z-50">
      <div className="absolute inset-0 bg-black/50" onClick={handleClose} />
      <motion.div 
        ref={modalRef}
        initial={{ y: '100%' }}
        animate={{ y: 0 }}
        exit={{ y: '100%' }}
        transition={{ type: 'spring', damping: 30, stiffness: 300 }}
        className="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl h-[90vh] flex flex-col"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <button 
            onClick={selectedCategory ? handleBack : handleClose}
            className="text-gray-600 p-2 -ml-2"
          >
            {selectedCategory ? (
              <ChevronRight className="w-6 h-6" />
            ) : (
              <X className="w-6 h-6" />
            )}
          </button>
          <h2 className="text-lg font-semibold">
            {selectedCategory ? selectedCategory.Name : 'التصنيفات'}
          </h2>
          <div className="w-10" /> {/* Spacer for balance */}
        </div>

        {/* Search */}
        <div className="p-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="ابحث عن تصنيف..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pr-10 pl-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Categories List */}
        <div className="flex-1 overflow-y-auto p-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : filteredCategories.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              لا توجد تصنيفات متاحة
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-3">
              {filteredCategories.map((category) => (
                <button
                  key={category.CategoryID}
                  onClick={() => handleCategorySelect(category)}
                  className="flex flex-col items-center justify-center p-4 rounded-xl border hover:bg-gray-50 transition-colors"
                >
                  <span className="text-2xl mb-2">{category.Icon}</span>
                  <span className="text-sm font-medium text-center">{category.Name}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
}
