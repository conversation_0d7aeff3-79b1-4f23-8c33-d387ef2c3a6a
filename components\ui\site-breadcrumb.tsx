'use client';

import { useSettings } from '@/contexts/settings-context';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from './breadcrumb';
import Link from 'next/link';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface SiteBreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function SiteBreadcrumb({ items, className = '' }: SiteBreadcrumbProps) {
  const { t } = useSettings();
  
  if (!items || items.length === 0) return null;
  
  return (
    <Breadcrumb className={`mb-4 sm:mb-6 ${className}`}>
      <BreadcrumbList>
        {/* Home is always the first item */}
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link href="/">{t('home')}</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        
        <BreadcrumbSeparator />
        
        {/* Map through the provided items */}
        {items.map((item, index) => {
          const isLast = index === items.length - 1;
          
          return (
            <BreadcrumbItem key={index}>
              {isLast ? (
                <BreadcrumbPage>{item.label}</BreadcrumbPage>
              ) : (
                <>
                  <BreadcrumbLink asChild>
                    <Link href={item.href || '#'}>{item.label}</Link>
                  </BreadcrumbLink>
                  <BreadcrumbSeparator />
                </>
              )}
            </BreadcrumbItem>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}