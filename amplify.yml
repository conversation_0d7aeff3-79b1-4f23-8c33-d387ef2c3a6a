version: 1
frontend:
  phases:
    preBuild:
      commands:
        - echo "Using Node.js 20 LTS"
        - node --version
        - npm --version
        - echo "Checking Firebase environment variables..."
        - echo "API_KEY:" $NEXT_PUBLIC_FIREBASE_API_KEY | cut -c1-20
        - echo "AUTH_DOMAIN:" $NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN
        - echo "PROJECT_ID:" $NEXT_PUBLIC_FIREBASE_PROJECT_ID
        - echo "Installing dependencies with legacy peer deps..."
        - npm install --legacy-peer-deps
    build:
      commands:
        - echo "Building the application..."
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - "**/*"
  cache:
    paths:
      - node_modules/**/*
# Enable serverless functions for API routes
backend:
  phases:
    build:
      commands:
        - echo "Setting up serverless functions for API routes"
# Custom headers for video streaming and CORS
customHeaders:
  - pattern: '/api/video-proxy'
    headers:
      - key: 'Access-Control-Allow-Origin'
        value: '*'
      - key: 'Access-Control-Allow-Methods'
        value: 'GET, HEAD, OPTIONS'
      - key: 'Access-Control-Allow-Headers'
        value: 'Range, Content-Type, Authorization, X-Requested-With, Cache-Control, Pragma'
      - key: 'Access-Control-Expose-Headers'
        value: 'Content-Length, Content-Range, Accept-Ranges, Content-Type, Cache-Control'
      - key: 'Accept-Ranges'
        value: 'bytes'
      - key: 'Cross-Origin-Resource-Policy'
        value: 'cross-origin'
      - key: 'Cross-Origin-Embedder-Policy'
        value: 'credentialless'
  - pattern: '/api/**'
    headers:
      - key: 'Access-Control-Allow-Origin'
        value: '*'
      - key: 'Access-Control-Allow-Methods'
        value: 'GET, POST, PUT, DELETE, OPTIONS'
      - key: 'Access-Control-Allow-Headers'
        value: 'Content-Type, Authorization, Range'
