import axios, { AxiosError, AxiosResponse } from 'axios';
import { Config as AppConfig } from './config';

// Configure axios defaults for HTTPS connections
axios.defaults.timeout = 30000; // 30 seconds timeout

// <PERSON>le self-signed certificates for local development
if (typeof window !== 'undefined' && window.location.protocol === 'https:' &&
    AppConfig.ADMIN_BASE_URL.includes('localhost')) {
  axios.defaults.httpsAgent = { rejectUnauthorized: false };
}

// Define interfaces for API responses
interface ApiErrorResponse {
    errorMessage?: string;
    status?: number | string;
}

interface ApiResponse {
    data?: any;
    errorMessage?: string;
    status?: number | string;
}

const Config = {
    ADMIN_BASE_URL: AppConfig.ADMIN_BASE_URL,
    API_VERSION: 'v1',
    DYNAMIC_METHOD_SUB_URL: 'api/v1/dynamic/dataoperation/',
    END_POINT_NAMES: {
        ...AppConfig.END_POINT_NAMES,
        GET_CATEGORIES_LIST: 'get-categories-list',
        SIGNUP_USER: 'signup-user',
        GET_USER_LOGIN: 'get-user-login',
        GET_USER_BY_PHONE: 'get-user-by-phone',
        RESET_PASSWORD_BY_PHONE: 'reset-password-by-phone',
        RESET_PASSWORD_FIREBASE: 'reset-password-firebase',
        GET_HOME_SCREEN_BANNER: 'get-home-screen-banner',
        GET_RECENT_PRODUCTS: 'get-recents-products-list',
        GET_POPULAR_PRODUCTS: 'get-popular-products-list',
        GET_HOT_DEAL_PRODUCTS: 'get-hot-deal-products',
        GET_CAMPAIGNS_LIST: 'get-web-campaign-list',
        GET_PRODUCTS_LIST: 'get-products-list',
        GET_ALL_PRODUCTS: 'api/v1/products/get-all-products',
        GET_MANUFACTURERS_LIST: 'get-manufacturers-list',
        GET_TAGS_LIST: 'get-tags-list',
        GET_CURRENCY_RATE: 'get-exchange-rate',
        GET_WEBSITE_LOGO: 'get-logo',
        GET_COUPON_CODE_DISCOUNT: 'get-coupon-code-data',
        GET_POINT: 'get-point',
        UPDATE_PROFILE: 'update-profile',
        INSERT_PRODUCT_REVIEW: 'Insert-Product-Review',
        GET_PRODUCT_REVIEWS: 'get-product-reviews',
    },
    COMMON_CONTROLLER_SUB_URL: 'api/v1/common/'
};

const GetTokenForHeader = async (): Promise<string | null> => {
    try {
        // First try to get from secure HttpOnly cookies via API
        if (typeof window !== 'undefined') {
            try {
                const response = await fetch('/api/auth/get-token', {
                    method: 'GET',
                    credentials: 'include', // Include HttpOnly cookies
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.token) {
                        console.log('🔐 Retrieved JWT token from secure HttpOnly cookie');
                        return data.token;
                    }
                }
            } catch (apiError) {
                console.log('API token retrieval failed, trying client-side cookies:', apiError);
            }

            // Fallback to client-side cookies
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'auth_token') {
                    console.log('🔐 Retrieved JWT token from client-side cookie');
                    return decodeURIComponent(value);
                }
            }

            // Final fallback to localStorage for backward compatibility (remove localStorage usage)
            const localStorageToken = localStorage.getItem('token') || localStorage.getItem('authToken');
            if (localStorageToken) {
                console.log('⚠️ Using token from localStorage (migrating to secure cookies)');
                // Clear localStorage after getting token
                localStorage.removeItem('token');
                localStorage.removeItem('authToken');
                return localStorageToken;
            }
        }
        return null;
    } catch (error) {
        console.error('Error getting token for header:', error);
        return null;
    }
};

const GetUserIdForHeader = async (): Promise<string | null> => {
    try {
        // Get user ID from secure cookies (client-accessible auth_user cookie)
        if (typeof window !== 'undefined') {
            // Try to get from client-side cookies first
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'auth_user') {
                    try {
                        const userData = JSON.parse(decodeURIComponent(value));
                        const userId = userData.UserId?.toString() || userData.UserID?.toString();
                        if (userId) {
                            console.log('🔐 Retrieved User ID from secure cookie');
                            return userId;
                        }
                    } catch (e) {
                        console.warn('Failed to parse user data from cookie:', e);
                    }
                }
            }

            // Fallback to localStorage for backward compatibility (remove localStorage usage)
            const localStorageUserId = localStorage.getItem('userId') || localStorage.getItem('userID');
            if (localStorageUserId) {
                console.log('⚠️ Using User ID from localStorage (migrating to secure cookies)');
                // Clear localStorage after getting user ID
                localStorage.removeItem('userId');
                localStorage.removeItem('userID');
                return localStorageUserId;
            }
        }
        return null;
    } catch (error) {
        console.error('Error getting user ID for header:', error);
        return null;
    }
};

// Utility function to remove UserId from request body
const removeUserIdFromRequestBody = (param: any): any => {
    if (!param) return param;

    // Create a deep copy to avoid modifying the original
    const cleanedParam = JSON.parse(JSON.stringify(param));

    // Remove UserId/UserID from top level
    if (cleanedParam.hasOwnProperty('UserId')) {
        console.log('🔧 Removing UserId from request body (will use JWT token instead)');
        delete cleanedParam.UserId;
    }
    if (cleanedParam.hasOwnProperty('UserID')) {
        console.log('🔧 Removing UserID from request body (will use JWT token instead)');
        delete cleanedParam.UserID;
    }
    if (cleanedParam.hasOwnProperty('user_id')) {
        console.log('🔧 Removing user_id from request body (will use JWT token instead)');
        delete cleanedParam.user_id;
    }

    // Remove UserId/UserID from requestParameters if it exists
    if (cleanedParam.requestParameters) {
        if (cleanedParam.requestParameters.hasOwnProperty('UserId')) {
            console.log('🔧 Removing UserId from requestParameters (will use JWT token instead)');
            delete cleanedParam.requestParameters.UserId;
        }
        if (cleanedParam.requestParameters.hasOwnProperty('UserID')) {
            console.log('🔧 Removing UserID from requestParameters (will use JWT token instead)');
            delete cleanedParam.requestParameters.UserID;
        }
        if (cleanedParam.requestParameters.hasOwnProperty('user_id')) {
            console.log('🔧 Removing user_id from requestParameters (will use JWT token instead)');
            delete cleanedParam.requestParameters.user_id;
        }
    }

    return cleanedParam;
};

export const MakeApiCallAsync = async (endPointName: string, methodSubURL: string | null, param: any, headers: any, methodType: string, loading = true): Promise<AxiosResponse | { data: ApiErrorResponse }> => {
    try {
        // Remove UserId from request body (JWT token will be used instead)
        const cleanedParam = removeUserIdFromRequestBody(param);

        // Create a copy of headers to avoid modifying the original object
        const updatedHeaders = { ...headers };

        // Check if Authorization header already exists
        if (!updatedHeaders.hasOwnProperty('Authorization')) {
            // If not, try to add it using the token from GetTokenForHeader
            const token = await GetTokenForHeader();
            if (token) {
                updatedHeaders['Authorization'] = 'Bearer ' + token;
                console.log('🔐 Added JWT token to Authorization header');
            }
        }

        // For backward compatibility, also add Token header if it doesn't exist
        if (!updatedHeaders.hasOwnProperty('Token')) {
            const token = await GetTokenForHeader();
            updatedHeaders['Token'] = token ?? "";
            if (token) {
                console.log('🔐 Added JWT token to Token header (backward compatibility)');
            }
        }

        // Add user id in header (for legacy endpoints that still need it)
        if (!updatedHeaders.hasOwnProperty('UserID')) {
            const UserID = await GetUserIdForHeader();
            updatedHeaders['UserID'] = UserID ?? "";
        }

        // Always ensure proper content type headers are set
        if (!updatedHeaders.hasOwnProperty('Accept')) {
            updatedHeaders['Accept'] = 'application/json';
        }

        if (!updatedHeaders.hasOwnProperty('Content-Type')) {
            updatedHeaders['Content-Type'] = 'application/json';
        }

        const URL = Config['ADMIN_BASE_URL'] + (methodSubURL === null || methodSubURL == undefined ? Config['DYNAMIC_METHOD_SUB_URL'] : methodSubURL) + endPointName;
        methodType = methodType ?? "POST";

        const axiosConfig: import('axios').AxiosRequestConfig = {
            headers: updatedHeaders,
            responseType: 'json' as const, // Explicitly set response type to JSON with proper type assertion
            timeout: 30000, // 30 seconds timeout
            withCredentials: false // Disable sending cookies with cross-origin requests
        };

        if (methodType === 'POST') {
            const response = await axios.post(URL, cleanedParam, axiosConfig);
            return response;
        } else if (methodType == 'GET') {
            axiosConfig.params = cleanedParam; // For GET requests, params should be used
            const response = await axios.get(URL, axiosConfig);
            return response;
        } else {
            // Return a default error response for unsupported method types
            return {
                data: {
                    errorMessage: `Unsupported method type: ${methodType}`,
                    status: 'method_not_supported'
                }
            };
        }
    } catch (error: unknown) {
        console.error('API call failed:', error);
        // Return a structured error response instead of throwing
        // This allows components to handle errors more gracefully

        // Create a response object with the ApiResponse interface
        const errorResponse: { data: ApiErrorResponse } = {
            data: {
                errorMessage: 'An unexpected error occurred',
                status: 'unknown_error'
            }
        };

        // Type guard for axios error with response
        if (error && typeof error === 'object' && 'response' in error && error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            const axiosError = error as AxiosError;
            const responseData = axiosError.response?.data as ApiErrorResponse | undefined;

            errorResponse.data = {
                errorMessage: responseData?.errorMessage || 'An error occurred while processing your request.',
                status: axiosError.response?.status
            };
        // Type guard for axios error with request but no response
        } else if (error && typeof error === 'object' && 'request' in error) {
            // The request was made but no response was received
            // This is likely a network error, CORS issue, or server not running
            const axiosError = error as AxiosError;
            let networkErrorMessage = 'Network error: No response received from server.';

            // Check if it's a CORS issue
            if (axiosError.message && axiosError.message.includes('Network Error')) {
                networkErrorMessage = 'Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:';
                networkErrorMessage += '\n1. The server is running and accessible';
                networkErrorMessage += '\n2. The URL is correct: ' + Config.ADMIN_BASE_URL;
                networkErrorMessage += '\n3. CORS is properly configured on the server';
                networkErrorMessage += '\n4. If using HTTPS, the SSL certificate is valid';
            }

            errorResponse.data = {
                errorMessage: networkErrorMessage,
                status: 'network_error'
            };
        } else {
            // Something happened in setting up the request that triggered an Error
            // Type guard for standard Error object
            const errorMessage = error && typeof error === 'object' && 'message' in error
                ? (error as Error).message
                : 'An unexpected error occurred';

            errorResponse.data = {
                errorMessage,
                status: 'request_error'
            };
        }

        return errorResponse;
    }
};

// Currency rate service
export const fetchCurrencyRate = async (): Promise<number> => {
    try {
        console.log('💱 Fetching exchange rate from API...');
        
        const response = await MakeApiCallAsync(
            'get-exchange-rate',
            null,
            {
                requestParameters: {}
            },
            {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            'POST',
            false
        );

        console.log('💱 Exchange rate API response:', {
            statusCode: response?.data?.statusCode,
            dataType: typeof response?.data?.data,
            rawData: response?.data?.data
        });

        if (response && response.data && response.data.statusCode === 200) {
            let rate;
            
            // Handle different possible response formats
            if (typeof response.data.data === 'string') {
                try {
                    // Try parsing as JSON first (nested structure)
                    const parsedData = JSON.parse(response.data.data);
                    console.log('💱 Parsed exchange rate data:', parsedData);
                    
                    // Handle array response
                    if (Array.isArray(parsedData) && parsedData.length > 0) {
                        const rateData = parsedData[0];
                        // Look for common rate field names
                        rate = rateData.Rate || rateData.ExchangeRate || rateData.rate || rateData.exchangeRate || rateData.Value || rateData.value;
                    } else if (typeof parsedData === 'object') {
                        // Handle object response
                        rate = parsedData.Rate || parsedData.ExchangeRate || parsedData.rate || parsedData.exchangeRate || parsedData.Value || parsedData.value;
                    } else {
                        // Direct numeric value
                        rate = parseFloat(parsedData);
                    }
                } catch (parseError) {
                    console.log('💱 Not JSON, trying direct parse:', response.data.data);
                    // If not JSON, try direct parse
                    rate = parseFloat(response.data.data);
                }
            } else if (typeof response.data.data === 'number') {
                rate = response.data.data;
            } else if (typeof response.data.data === 'object') {
                // Direct object response
                const rateData = response.data.data;
                rate = rateData.Rate || rateData.ExchangeRate || rateData.rate || rateData.exchangeRate || rateData.Value || rateData.value;
            }
            
            console.log('💱 Extracted rate value:', rate, '(type:', typeof rate, ')');
            
            if (rate !== undefined && !isNaN(parseFloat(rate)) && parseFloat(rate) > 0) {
                const finalRate = parseFloat(rate);
                console.log('✅ Exchange rate fetched successfully:', finalRate);
                return finalRate;
            }
        }
        
        console.log('⚠️ Using default exchange rate (1500) - API response invalid or empty');
        return 1500; // Default fallback rate (consistent with CurrencyContext)
    } catch (error) {
        console.error('❌ Error fetching currency rate:', error);
        return 1500; // Default fallback rate (consistent with CurrencyContext)
    }
};

// Logo fetching service
export const fetchWebsiteLogo = async (): Promise<string> => {
    try {
        const response = await MakeApiCallAsync(
            'get-logo',
            null,
            {
                requestParameters: {}
            },
            {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            'POST',
            false
        );

        if (response && response.data && response.data.statusCode === 200) {
            const logoPath = response.data.data;
            if (logoPath && typeof logoPath === 'string') {
                // Construct full URL: NEXT_PUBLIC_API_BASE_URL + logoPath
                const fullLogoUrl = `${process.env.NEXT_PUBLIC_API_BASE_URL || Config.ADMIN_BASE_URL}${logoPath}`;
                console.log('Logo URL fetched successfully:', fullLogoUrl);
                return fullLogoUrl;
            }
        }
        
        console.log('Using default logo - API response invalid');
        return '/default-logo.png'; // Default fallback logo
    } catch (error) {
        console.error('Error fetching website logo:', error);
        return '/default-logo.png'; // Default fallback logo
    }
};

export const convertUSDToIQD = (usdPrice: number, rate: number): number => {
    // Validate inputs
    if (!usdPrice || isNaN(usdPrice) || usdPrice < 0) {
        console.warn('⚠️ Invalid USD price for conversion:', usdPrice);
        return 0;
    }
    
    if (!rate || isNaN(rate) || rate <= 0) {
        console.warn('⚠️ Invalid exchange rate for conversion:', rate, '- using default 1500');
        rate = 1500; // Fallback rate
    }
    
    const result = Math.round(usdPrice * rate);
    console.log(`💱 Currency conversion: $${usdPrice} × ${rate} = ${result} IQD`);
    return result;
};

export const formatPrice = (price: number | undefined | null, currency: 'USD' | 'IQD' = 'USD'): string => {
    // Handle undefined, null, or invalid price values
    if (price === undefined || price === null || isNaN(price)) {
        if (currency === 'IQD') {
            return '0 IQD';
        }
        return '$0.00';
    }

    // Ensure price is a valid number
    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;
    if (isNaN(numericPrice)) {
        if (currency === 'IQD') {
            return '0 IQD';
        }
        return '$0.00';
    }

    if (currency === 'IQD') {
        // For IQD, round to nearest whole number and format with commas
        const roundedPrice = Math.round(numericPrice);
        return `${roundedPrice.toLocaleString()} IQD`;
    }
    
    // For USD, ensure 2 decimal places
    return `$${numericPrice.toFixed(2)}`;
};

export { Config, GetTokenForHeader };