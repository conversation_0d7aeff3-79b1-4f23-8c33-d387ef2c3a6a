/**
 * Utility functions for phone number handling
 */

/**
 * Validates if a phone number is in the correct format
 * @param phoneNumber - The phone number to validate
 * @returns boolean - True if valid, false otherwise
 */
export function isValidPhoneNumber(phoneNumber: string): boolean {
  // Basic validation: should start with + and have 10-15 digits
  const phoneRegex = /^\+[1-9]\d{9,14}$/;
  return phoneRegex.test(phoneNumber);
}

/**
 * Formats a phone number to E.164 format
 * @param phoneNumber - The phone number to format
 * @returns string - Formatted phone number or original if already formatted
 */
export function formatPhoneNumber(phoneNumber: string): string {
  // Remove all non-digit characters except +
  const cleaned = phoneNumber.replace(/[^\d+]/g, '');
  
  // If it doesn't start with +, assume it's a US number and add +1
  if (!cleaned.startsWith('+')) {
    return `+1${cleaned}`;
  }
  
  return cleaned;
}

/**
 * Sanitizes phone number input
 * @param phoneNumber - Raw phone number input
 * @returns string - Sanitized phone number
 */
export function sanitizePhoneNumber(phoneNumber: string): string {
  return phoneNumber.trim().replace(/\s+/g, '');
}