'use client';

import React, { createContext, useContext } from 'react';

type ContactInfo = {
  whatsappNumber: string;
  phoneNumber: string;
  whatsappLink: string;
};

const contactInfo: ContactInfo = {
  whatsappNumber: '+**********',  // Replace with actual WhatsApp number
  phoneNumber: '+**********',     // Replace with actual phone number
  whatsappLink: 'https://wa.me/**********'  // Replace with actual WhatsApp link
};

const ContactContext = createContext<ContactInfo>(contactInfo);

export function ContactProvider({ children }: { children: React.ReactNode }) {
  return (
    <ContactContext.Provider value={contactInfo}>
      {children}
    </ContactContext.Provider>
  );
}

export function useContactInfo() {
  return useContext(ContactContext);
}