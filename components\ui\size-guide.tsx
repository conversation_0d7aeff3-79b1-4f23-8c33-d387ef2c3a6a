'use client';

import { useState } from 'react';
import { But<PERSON> } from './button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './table';
import { useSettings } from '@/contexts/settings-context';

interface SizeGuideProps {
  className?: string;
  buttonVariant?: 'default' | 'outline' | 'ghost';
}

export function SizeGuide({ className = '', buttonVariant = 'outline' }: SizeGuideProps) {
  const { t } = useSettings();
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={buttonVariant} className={className}>
          Size Guide
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Size Guide</DialogTitle>
        </DialogHeader>

        <div className="mt-4 overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Size</TableHead>
                <TableHead className="text-center">Chest (in)</TableHead>
                <TableHead className="text-center">Waist (in)</TableHead>
                <TableHead className="text-center">Hips (in)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell className="font-medium">XS</TableCell>
                <TableCell className="text-center">34-36</TableCell>
                <TableCell className="text-center">27-29</TableCell>
                <TableCell className="text-center">34-36</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">S</TableCell>
                <TableCell className="text-center">36-38</TableCell>
                <TableCell className="text-center">29-31</TableCell>
                <TableCell className="text-center">36-38</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">M</TableCell>
                <TableCell className="text-center">38-40</TableCell>
                <TableCell className="text-center">31-33</TableCell>
                <TableCell className="text-center">38-40</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">L</TableCell>
                <TableCell className="text-center">40-42</TableCell>
                <TableCell className="text-center">33-36</TableCell>
                <TableCell className="text-center">40-43</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">XL</TableCell>
                <TableCell className="text-center">42-45</TableCell>
                <TableCell className="text-center">36-40</TableCell>
                <TableCell className="text-center">43-46</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">XXL</TableCell>
                <TableCell className="text-center">45-48</TableCell>
                <TableCell className="text-center">40-44</TableCell>
                <TableCell className="text-center">46-50</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <div className="mt-4 text-sm text-muted-foreground">
          <p>Note: This size guide is general and may vary by brand or style. Always check specific product measurements when available.</p>
        </div>
      </DialogContent>
    </Dialog>
  );
}