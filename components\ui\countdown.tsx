"use client";

import { useEffect, useState } from "react";
import { useColorThemeContext } from '@/contexts/color-theme-context';

interface CountdownProps {
  endDate: string | Date;
  className?: string;
}

interface CountdownState {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  completed: boolean;
}

export function Countdown({ endDate, className }: CountdownProps) {
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  
  const [countdown, setCountdown] = useState<CountdownState>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    completed: false,
  });

  useEffect(() => {
    const targetDate = new Date(endDate).getTime();

    const interval = setInterval(() => {
      const now = new Date().getTime();
      const difference = targetDate - now;

      if (difference < 0) {
        clearInterval(interval);
        setCountdown((prev) => ({ ...prev, completed: true }));
        return;
      }

      setCountdown({
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor(
          (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
        ),
        minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((difference % (1000 * 60)) / 1000),
        completed: false,
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [endDate]);

  if (countdown.completed) {
    return <span className={className}>Offer expired</span>;
  }

  return (
    <div
      className={`inline-flex items-center gap-0.5 sm:gap-2 bg-white rounded-lg p-1.5 sm:p-3 shadow-sm border ${className}`}
    >
      <div className="flex flex-col items-center">
        <div className="w-6 h-6 sm:w-12 sm:h-12 bg-white border sm:border-2 rounded sm:rounded-lg flex items-center justify-center border-red-600">
          <span className="text-[10px] sm:text-xl font-bold text-red-600">
            {countdown.days.toString().padStart(2, "0")}
          </span>
        </div>
        <span className="text-[8px] sm:text-xs font-medium mt-0.5 sm:mt-1 text-red-600">Days</span>
      </div>
      <div className="flex flex-col items-center">
        <div className="w-6 h-6 sm:w-12 sm:h-12 bg-white border sm:border-2 rounded sm:rounded-lg flex items-center justify-center border-red-600">
          <span className="text-[10px] sm:text-xl font-bold text-red-600">
            {countdown.hours.toString().padStart(2, "0")}
          </span>
        </div>
        <span className="text-[8px] sm:text-xs font-medium mt-0.5 sm:mt-1 text-red-600">Hrs</span>
      </div>
      <div className="flex flex-col items-center">
        <div className="w-6 h-6 sm:w-12 sm:h-12 bg-white border sm:border-2 rounded sm:rounded-lg flex items-center justify-center border-red-600">
          <span className="text-[10px] sm:text-xl font-bold text-red-600">
            {countdown.minutes.toString().padStart(2, "0")}
          </span>
        </div>
        <span className="text-[8px] sm:text-xs font-medium mt-0.5 sm:mt-1 text-red-600">Min</span>
      </div>
      <div className="flex flex-col items-center">
        <div className="w-6 h-6 sm:w-12 sm:h-12 bg-white border sm:border-2 rounded sm:rounded-lg flex items-center justify-center border-red-600">
          <span className="text-[10px] sm:text-xl font-bold text-red-600">
            {countdown.seconds.toString().padStart(2, "0")}
          </span>
        </div>
        <span className="text-[8px] sm:text-xs font-medium mt-0.5 sm:mt-1 text-red-600">Sec</span>
      </div>
    </div>
  );
}
