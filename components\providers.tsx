'use client';

import { SettingsProvider } from '@/contexts/settings-context';
import { CartProvider } from '@/contexts/cart-context';
import { ContactProvider } from '@/contexts/contact-info';
import { CouponProvider } from '@/contexts/coupon-context';
import { WishlistProvider } from '@/contexts/wishlist-context';
import { CurrencyProvider } from '@/contexts/currency-context';
import { UserProvider } from '@/contexts/user-context';
import { ColorThemeProvider } from '@/contexts/color-theme-context';
import { PointProvider } from '@/contexts/point-context';
import { LogoProvider } from '@/contexts/logo-context';

interface ProvidersProps {
  children: React.ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  return (
    <SettingsProvider>
      <ColorThemeProvider>
        <UserProvider>
          <PointProvider>
            <CurrencyProvider>
              <LogoProvider>
                <CartProvider>
                  <ContactProvider>
                    <CouponProvider>
                      <WishlistProvider>
                        {children}
                      </WishlistProvider>
                    </CouponProvider>
                  </ContactProvider>
                </CartProvider>
              </LogoProvider>
            </CurrencyProvider>
          </PointProvider>
        </UserProvider>
      </ColorThemeProvider>
    </SettingsProvider>
  );
}