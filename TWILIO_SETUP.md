# Twilio SMS Setup Guide

This guide explains how to set up Twilio SMS API for phone verification in your application.

## Prerequisites

1. A Twilio account (sign up at https://www.twilio.com/try-twilio)
2. Twilio credentials (Account SID, Auth Token, and Phone Number)

## Setup Steps

### 1. Install Dependencies

```bash
npm install twilio
```

### 2. Environment Variables

Add the following to your `.env.local` and `.env` files:

```env
# Twilio SMS Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here
```

### 3. Get Twilio Credentials

1. Sign up for a Twilio account at https://www.twilio.com/try-twilio
2. Go to your Twilio Console Dashboard
3. Find your Account SID and Auth Token in the "Account Info" section
4. Purchase a phone number or use your trial number
5. Replace the placeholder values in your environment files

## Testing

1. Start your development server: `npm run dev`
2. Visit `/test-twilio` to test SMS functionality
3. Enter a phone number with country code (e.g., +**********)
4. Check if you receive the SMS verification code

## API Endpoints

### Send Verification Code

**POST** `/api/sms/send-verification`

```json
{
  "phoneNumber": "+**********"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Verification code sent successfully",
  "messageSid": "SM**********abcdef",
  "verificationCode": "123456" // Only in development mode
}
```

### Verify Code

**POST** `/api/sms/verify-code`

```json
{
  "phoneNumber": "+**********",
  "code": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Phone number verified successfully"
}
```

## Features

- **SMS Verification**: Send 6-digit verification codes via SMS
- **Code Expiration**: Codes expire after 10 minutes
- **Rate Limiting**: Built-in protection against spam
- **Development Mode**: Shows verification codes in development for testing
- **Error Handling**: Comprehensive error handling and logging

## File Structure

```
lib/
├── twilio.ts              # Twilio client initialization
app/
├── api/
│   └── sms/
│       ├── send-verification/
│       │   └── route.ts   # Send SMS verification code
│       └── verify-code/
│           └── route.ts   # Verify SMS code
├── test-twilio/
│   └── page.tsx          # Test page for SMS functionality
├── signup-twilio/
│   └── page.tsx          # Signup with SMS verification
├── signup/
│   └── page.tsx          # Main signup page (uses SMS)
└── forgot-password/
    └── page.tsx          # Password reset with SMS
```

## Configuration Options

### Phone Number Format
- Always include country code (e.g., +1 for US, +44 for UK)
- Remove any spaces, dashes, or parentheses
- Example: +**********

### Message Customization
You can customize the SMS message in `/api/sms/send-verification/route.ts`:

```typescript
const message = await twilio.messages.create({
  body: `Your CodeMedical verification code is: ${verificationCode}. This code will expire in 10 minutes.`,
  from: process.env.TWILIO_PHONE_NUMBER,
  to: phoneNumber,
});
```

## Security Considerations

### Built-in Security Features

This implementation includes comprehensive security measures to protect against fraud and bot attacks:

1. **Rate Limiting**: 
   - 3 SMS attempts per phone number per 15 minutes
   - 1 hour block after exceeding limits
   - 5 verification attempts per phone number per 15 minutes

2. **Bot Detection**:
   - User agent analysis
   - Referer header validation
   - Request pattern analysis

3. **Phone Number Validation**:
   - Format validation (E.164)
   - Suspicious pattern detection (repeated digits, sequential numbers)
   - Country code validation

4. **Origin Validation**:
   - CORS protection
   - Allowed origins whitelist
   - Referer header checks

5. **IP Address Monitoring**:
   - Suspicious IP detection
   - Automatic temporary blocking
   - Private IP filtering in production

6. **Security Monitoring**:
   - Real-time event logging
   - Pattern detection
   - Automatic alerting

### Security Headers

The middleware automatically adds security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`

### Environment Variables

```env
# Twilio SMS Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here

# Security Configuration
SECURITY_DASHBOARD_TOKEN=your_secure_random_token_here
```

### Security Dashboard

Monitor security events at `/api/security/dashboard` with proper authentication:

```bash
curl -H "Authorization: Bearer your_secure_random_token_here" \
     https://yourdomain.com/api/security/dashboard
```

### Additional Recommendations

1. **Environment Variables**: Never expose credentials in client-side code
2. **HTTPS**: Always use HTTPS in production
3. **Database Storage**: Use Redis or database for verification codes in production
4. **Monitoring**: Set up alerts for security events
5. **Regular Updates**: Keep dependencies updated
6. **Backup Plans**: Have alternative verification methods

## Production Deployment

### Environment Variables
Set the following environment variables in your production environment:

```env
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=+**********
```

### Recommended Improvements

1. **Database Storage**: Replace in-memory code storage with Redis or database
2. **Rate Limiting**: Implement proper rate limiting per phone number
3. **Logging**: Add comprehensive logging for monitoring
4. **Webhooks**: Set up Twilio webhooks for delivery status
5. **International Support**: Handle international phone number formatting

### 3. Configure Twilio Settings

- Set up proper sender ID/from number
- Configure delivery receipts if needed
- Set up webhooks for message status updates

## Troubleshooting

Common issues and solutions:

1. **SMS not received**: Check phone number format (must include country code)
2. **Invalid credentials**: Verify your Twilio Account SID and Auth Token
3. **Rate limiting**: Twilio has rate limits, implement proper retry logic
4. **Phone number not verified**: In trial mode, you can only send to verified numbers
5. **Country restrictions**: Some countries may have restrictions on SMS delivery

### Testing

Use the test page at `/test-twilio` to verify your setup is working correctly.

## Migration from Vonage

The following files have been updated to remove Vonage dependencies:

- `lib/vonage.ts` → Replaced with `lib/twilio.ts`
- `app/signup-vonage/page.tsx` → Moved to `app/signup-twilio/page.tsx`
- `app/test-vonage/page.tsx` → Moved to `app/test-twilio/page.tsx`
- `package.json` → Removed `@vonage/server-sdk` dependency, added `twilio`
- Environment variables → Replaced Vonage config with Twilio config

## Cost Considerations

Twilio charges per SMS sent. Check their pricing at https://www.twilio.com/sms/pricing to understand costs for your usage volume.

## Support

For Twilio-specific issues, refer to:
- [Twilio Documentation](https://www.twilio.com/docs)
- [Twilio SMS API Reference](https://www.twilio.com/docs/sms/api)
- [Twilio Console](https://console.twilio.com/)