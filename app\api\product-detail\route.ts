import { type NextRequest, NextResponse } from "next/server"
import { Config } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    console.log("Product Detail API Route - Received request body:", body)

    const response = await fetch(`${Config.ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-product_detail`, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    })

    console.log("Product Detail API Route - External API response status:", response.status)

    if (!response.ok) {
      console.error("Product Detail API Route - External API error:", response.status, response.statusText)
      return NextResponse.json(
        { error: `External API error: ${response.status} ${response.statusText}` },
        { status: response.status },
      )
    }

    const data = await response.json();
    console.log("Product Detail API Route - External API response data:", data);

    // Check if data needs parsing
    let parsedData;
    if (typeof data.data === 'string') {
        try {
            parsedData = JSON.parse(data.data);
        } catch (e) {
            console.error("Failed to parse data.data:", e);
            return NextResponse.json({ error: "Failed to parse product details" }, { status: 500 });
        }
    } else {
        parsedData = data.data;
    }

    // Ensure parsedData is an array
    const products = Array.isArray(parsedData) ? parsedData : [parsedData];

    // Modify the image URL
    const modifiedProducts = products.map(product => {
        if (product.ImagePath && (product.ImagePath.startsWith('http://') || product.ImagePath.startsWith('https://'))) {
            product.ProductImage = product.ImagePath;
        } else if (product.ImagePath) {
            product.ProductImage = `${Config.ADMIN_BASE_URL}${product.ImagePath.startsWith('/') ? '' : '/'}${product.ImagePath}`;
        }
        return product;
    });

    // Return the modified data
    if (typeof data.data === 'string') {
        return NextResponse.json({ ...data, data: JSON.stringify(modifiedProducts) });
    } else {
        return NextResponse.json({ ...data, data: modifiedProducts });
    }
  } catch (error) {
    console.error("Product Detail API Route - Error:", error)
    return NextResponse.json({ error: "Failed to fetch product details" }, { status: 500 })
  }
}
