'use client';

import { Star } from 'lucide-react';

interface ProductRatingStarsProps {
  rating: number;
  maxRating?: number;
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  className?: string;
}

export function ProductRatingStars({
  rating,
  maxRating = 5,
  size = 'md',
  showValue = false,
  className = '',
}: ProductRatingStarsProps) {
  // Ensure rating is between 0 and maxRating
  const normalizedRating = Math.max(0, Math.min(rating, maxRating));
  
  // Calculate the filled width as a percentage
  const filledPercentage = (normalizedRating / maxRating) * 100;
  
  // Determine star size based on the size prop
  const starSizeMap = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  };
  
  const starSize = starSizeMap[size];
  
  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <div className="relative inline-flex">
        {/* Background stars (gray) */}
        <div className="flex">
          {Array.from({ length: maxRating }).map((_, i) => (
            <Star key={`bg-${i}`} className={`${starSize} text-gray-200`} />
          ))}
        </div>
        
        {/* Foreground stars (filled based on rating) */}
        <div 
          className="absolute top-0 left-0 overflow-hidden flex" 
          style={{ width: `${filledPercentage}%` }}
        >
          {Array.from({ length: maxRating }).map((_, i) => (
            <Star key={`fg-${i}`} className={`${starSize} text-yellow-400`} fill="currentColor" />
          ))}
        </div>
      </div>
      
      {showValue && (
        <span className="text-sm font-medium ml-1">
          {normalizedRating.toFixed(1)}
        </span>
      )}
    </div>
  );
}