'use client';

import React, { createContext, useContext, useState } from 'react';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';
import { useCart } from './cart-context';

type Coupon = {
  code: string;
  discount: number;
  type: 'percentage' | 'fixed';
  discountTypeId: number;
  title?: string;
  discountId?: number;
  minAmount?: number;
  expiryDate?: Date;
  maxQuantity?: number;
  productId?: number;
  categoryId?: number;
  orderCount?: number;
  orderItemsCount?: number;
  productIds?: string;
};

interface CouponContextType {
  appliedCoupon: Coupon | null;
  validateCoupon: (code: string, amount: number, cartItems?: any[]) => Promise<{ valid: boolean; message: string; discount: number }>;
  clearCoupon: () => void;
  isLoading: boolean;
}

const CouponContext = createContext<CouponContextType | undefined>(undefined);

export function CouponProvider({ children }: { children: React.ReactNode }) {
  const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const validateCoupon = async (code: string, amount: number, cartItems?: any[]): Promise<{ valid: boolean; message: string; discount: number }> => {
    if (!code.trim()) {
      return { valid: false, message: 'Please enter a coupon code', discount: 0 };
    }

    setIsLoading(true);

    try {
      // Prepare cart data from cart items
      const cartData = cartItems?.map(item => ({
        ProductId: item.id,
        ProductName: item.name,
        Price: item.adjustedPrice || item.price,
        Quantity: item.quantity,
        IsDiscountAllowed: true
      })) || [];

      const cartJsonData = JSON.stringify(cartData);

      const param = {
        requestParameters: {
          CouponCode: code.toUpperCase(),
          cartJsonData: cartJsonData
        }
      };

      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      const response = await MakeApiCallAsync(
        Config.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,
        Config.DYNAMIC_METHOD_SUB_URL,
        param,
        headers,
        'POST'
      );

      if (response && response.data && !response.data.errorMessage) {
        let couponData;

        // Parse the response data
        if (typeof response.data.data === 'string') {
          couponData = JSON.parse(response.data.data);
        } else {
          couponData = response.data.data;
        }

        // Check if couponData is valid and has discount amount
        if (couponData && couponData.DiscountValueAfterCouponAppliedWithQuantity > 0) {
          const discountAmount = couponData.DiscountValueAfterCouponAppliedWithQuantity;
          const discountTypeId = couponData.DiscountTypeId || 1;
          const maxQuantity = couponData.MaxQuantity || 0;
          const orderCount = couponData.OrderCount || 0;
          const orderItemsCount = couponData.OrderItemsCount || 0;
          const productIds = couponData.ProductIds || "";

          // Validate based on DiscountTypeId
          if (discountTypeId === 1 && maxQuantity > 0 && orderCount > maxQuantity) {
            return {
              valid: false,
              message: `This coupon has a maximum usage limit of ${maxQuantity} orders. You have already used it ${orderCount} times.`,
              discount: 0
            };
          }

          if (discountTypeId === 3) {
            // Check OrderItemsCount vs MaxQuantity
            if (maxQuantity > 0 && orderItemsCount > maxQuantity) {
              return {
                valid: false,
                message: `This coupon has a maximum usage limit of ${maxQuantity} items. You have already used it for ${orderItemsCount} items.`,
                discount: 0
              };
            }

            // Check if cart contains required ProductIds
            if (productIds && productIds.trim() !== "") {
              const requiredProductIds = productIds.split(',').map((id: string) => parseInt(id.trim())).filter((id: number) => !isNaN(id));
              const cartProductIds = cartItems?.map(item => item.id) || [];
              const hasRequiredProducts = requiredProductIds.some((reqId: number) => cartProductIds.includes(reqId));
              
              if (!hasRequiredProducts) {
                return {
                  valid: false,
                  message: 'This coupon is only valid for specific products that are not in your cart.',
                  discount: 0
                };
              }
            }
          }

          // Determine discount type based on the coupon data
          // CORRECT mapping
          const discountType = couponData.DiscountValueType === 2 ? 'percentage' : 'fixed';

          // Create coupon object for state
          const coupon: Coupon = {
            code: code.toUpperCase(),
            discount: discountAmount,
            type: discountType,
            discountTypeId: discountTypeId
          };

          setAppliedCoupon(coupon);

          return {
            valid: true,
            message: 'Coupon applied successfully!',
            discount: discountAmount
          };
        } else {
          // If no discount amount, check if we got basic coupon data and calculate manually
          if (couponData && Array.isArray(couponData) && couponData.length > 0) {
            const couponInfo = couponData[0];
            if (couponInfo && couponInfo.DiscountValue > 0 && couponInfo.IsActive) {
              const discountTypeId = couponInfo.DiscountTypeId || 1;
              const maxQuantity = couponInfo.MaxQuantity || 0;
              const orderCount = couponInfo.OrderCount || 0;
              const orderItemsCount = couponInfo.OrderItemsCount || 0;
              const productIds = couponInfo.ProductIds || "";

              // Validate based on DiscountTypeId
              if (discountTypeId === 1 && maxQuantity > 0 && orderCount > maxQuantity) {
                return {
                  valid: false,
                  message: `This coupon has a maximum usage limit of ${maxQuantity} orders. You have already used it ${orderCount} times.`,
                  discount: 0
                };
              }

              if (discountTypeId === 3) {
                // Check OrderItemsCount vs MaxQuantity
                if (maxQuantity > 0 && orderItemsCount > maxQuantity) {
                  return {
                    valid: false,
                    message: `This coupon has a maximum usage limit of ${maxQuantity} items. You have already used it for ${orderItemsCount} items.`,
                    discount: 0
                  };
                }

                // Check if cart contains required ProductIds
                if (productIds && productIds.trim() !== "") {
                  const requiredProductIds = productIds.split(',').map((id: string) => parseInt(id.trim())).filter((id: number) => !isNaN(id));
                  const cartProductIds = cartItems?.map(item => item.id) || [];
                  const hasRequiredProducts = requiredProductIds.some((reqId: number) => cartProductIds.includes(reqId));
                  
                  if (!hasRequiredProducts) {
                    return {
                      valid: false,
                      message: 'This coupon is only valid for specific products that are not in your cart.',
                      discount: 0
                    };
                  }
                }
              }

              // Calculate discount based on discount type and value type
              let discountAmount = 0;
              
              // Apply discount based on DiscountTypeId
              switch (discountTypeId) {
                case 1: // Applied on order total
                case 2: // Applied on order subtotal
                  if (couponInfo.DiscountValueType === 2) {
                    // Percentage
                    discountAmount = (couponInfo.DiscountValue * amount) / 100;
                  } else if (couponInfo.DiscountValueType === 1) {
                    // Fixed value
                    discountAmount = couponInfo.DiscountValue;
                  }
                  break;
                case 3: // Applied on products
                case 4: // Applied on categories
                case 5: // Applied on manufacturers
                case 6: // Applied on cities
                case 7: // Applied on shipping
                  // For specific applications, use the discount value as provided
                  if (couponInfo.DiscountValueType === 2) {
                    discountAmount = (couponInfo.DiscountValue * amount) / 100;
                  } else {
                    discountAmount = couponInfo.DiscountValue;
                  }
                  break;
                default:
                  discountAmount = 0;
              }

              if (discountAmount > 0) {
                const couponObj: Coupon = {
                  code: code.toUpperCase(),
                  discount: discountAmount,
                  type: couponInfo.DiscountValueType === 2 ? 'percentage' : 'fixed',
                  discountTypeId: discountTypeId,
                  title: couponInfo.Title,
                  discountId: couponInfo.DiscountId,
                  maxQuantity: couponInfo.MaxQuantity,
                  productId: couponInfo.ProductId,
                  categoryId: couponInfo.CategoryID
                };

                setAppliedCoupon(couponObj);

                const discountTypeNames: { [key: number]: string } = {
                  1: 'order total',
                  2: 'order subtotal',
                  3: 'products',
                  4: 'categories',
                  5: 'manufacturers',
                  6: 'cities',
                  7: 'shipping'
                };

                return {
                  valid: true,
                  message: `Coupon "${couponInfo.Title}" applied successfully! Discount applied on ${discountTypeNames[discountTypeId] || 'order'}.`,
                  discount: discountAmount
                };
              }
            }
          }

          return {
            valid: false,
            message: 'Invalid coupon code or coupon not applicable to your cart',
            discount: 0
          };
        }
      } else {
        return {
          valid: false,
          message: response.data?.errorMessage || 'Failed to validate coupon',
          discount: 0
        };
      }
    } catch (error) {
      console.error('Coupon validation error:', error);
      return {
        valid: false,
        message: 'Error validating coupon. Please try again.',
        discount: 0
      };
    } finally {
      setIsLoading(false);
    }
  };

  const clearCoupon = () => {
    setAppliedCoupon(null);
  };

  return (
    <CouponContext.Provider
      value={{
        appliedCoupon,
        validateCoupon,
        clearCoupon,
        isLoading
      }}
    >
      {children}
    </CouponContext.Provider>
  );
}

export function useCoupon() {
  const context = useContext(CouponContext);
  if (context === undefined) {
    throw new Error('useCoupon must be used within a CouponProvider');
  }
  return context;
}