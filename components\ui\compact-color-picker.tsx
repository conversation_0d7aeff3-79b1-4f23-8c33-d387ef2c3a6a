'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Check } from 'lucide-react';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { ColorTheme } from '@/hooks/use-color-theme';
import { cn } from '@/lib/utils';

interface CompactColorPickerProps {
  className?: string;
  onColorChange?: (color: string) => void;
}

export function CompactColorPicker({ className, onColorChange }: CompactColorPickerProps) {
  const { currentTheme, availableThemes, changeTheme, createCustomTheme, isLoading } = useColorThemeContext();
  const [customColor, setCustomColor] = useState('#0074b2');

  const handlePresetThemeSelect = (theme: ColorTheme) => {
    changeTheme(theme);
    onColorChange?.(theme.primary);
  };

  const handleCustomColorChange = (color: string) => {
    setCustomColor(color);
  };

  const handleCustomColorApply = () => {
    createCustomTheme(customColor, 'Custom Color');
    onColorChange?.(customColor);
  };

  const isValidHexColor = (color: string) => {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
  };

  if (isLoading) {
    return (
      <div className={cn("flex items-center justify-center p-4", className)}>
        <div className="w-6 h-6 rounded-full bg-gray-200 animate-pulse" />
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Preset Colors - Compact Grid */}
      <div className="grid grid-cols-4 gap-2">
        {availableThemes.map((theme) => (
          <button
            key={theme.name}
            onClick={() => handlePresetThemeSelect(theme)}
            className={cn(
              "relative w-8 h-8 rounded-full border-2 transition-all hover:scale-110",
              currentTheme.primary === theme.primary
                ? "border-gray-900 shadow-md scale-110"
                : "border-gray-200 hover:border-gray-300"
            )}
            style={{ backgroundColor: theme.primary }}
            title={theme.name}
          >
            {currentTheme.primary === theme.primary && (
              <Check className="w-3 h-3 text-white absolute inset-0 m-auto" />
            )}
          </button>
        ))}
      </div>

      {/* Custom Color Input - Compact */}
      <div className="space-y-2 pt-2 border-t border-gray-100">
        <div className="flex gap-2 items-center">
          <input
            type="color"
            value={customColor}
            onChange={(e) => handleCustomColorChange(e.target.value)}
            className="w-8 h-8 rounded border cursor-pointer"
            title="Pick custom color"
          />
          <Input
            type="text"
            value={customColor}
            onChange={(e) => handleCustomColorChange(e.target.value)}
            placeholder="#0074b2"
            className="text-xs h-8 flex-1"
          />
          <Button
            onClick={handleCustomColorApply}
            disabled={!isValidHexColor(customColor)}
            size="sm"
            className="h-8 px-3 text-xs"
          >
            Apply
          </Button>
        </div>
        {!isValidHexColor(customColor) && (
          <p className="text-xs" style={{ color: '#ef4444' }}>
            Enter valid hex color
          </p>
        )}
      </div>

      {/* Current Theme Info */}
      <div className="text-xs text-gray-500 text-center pt-1 border-t border-gray-100">
        Current: {currentTheme.name}
      </div>
    </div>
  );
}