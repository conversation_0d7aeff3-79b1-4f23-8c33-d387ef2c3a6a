import { NextRequest, NextResponse } from 'next/server';

// Optimized timeout for Amplify serverless environment
export const maxDuration = 60; // Reduced to 60 seconds for better Amplify compatibility
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store'; // Disable caching for video streaming

// Helper function to check if URL is from interserver or similar hosting
function isInterserverOrSimilar(url: string): boolean {
  const hostname = new URL(url).hostname.toLowerCase();
  return hostname.includes('interserver') || 
         hostname.includes('hostgator') || 
         hostname.includes('godaddy') || 
         hostname.includes('bluehost') ||
         hostname.includes('siteground');
}

export async function GET(request: NextRequest) {
  try {
    const url = request.nextUrl.searchParams.get('url');
    const quality = request.nextUrl.searchParams.get('quality');
    const bandwidth = request.nextUrl.searchParams.get('bandwidth');
    
    if (!url) {
      console.error('Video proxy: Missing URL parameter');
      return new NextResponse('Missing URL parameter', { status: 400 });
    }

    // Validate URL to prevent SSRF attacks
    try {
      const parsedUrl = new URL(url);
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        throw new Error('Invalid protocol');
      }
    } catch (urlError) {
      console.error('Video proxy: Invalid URL:', url);
      return new NextResponse('Invalid URL', { status: 400 });
    }

    console.log('Video proxy: Fetching video from:', url);
    if (quality) {
      console.log('Video proxy: Requested quality:', quality, 'bandwidth:', bandwidth);
    }

    // Enhanced fetch with timeout optimized for Amplify and interserver hosting
    const controller = new AbortController();
    // Use different timeouts based on hosting provider
    const isInterserver = isInterserverOrSimilar(url);
    const timeoutDuration = isInterserver ? 15000 : 8000; // Longer timeout for interserver
    const timeoutId = setTimeout(() => {
      console.log('Video proxy: Request timeout after', timeoutDuration, 'ms for', isInterserver ? 'interserver' : 'standard', 'hosting');
      controller.abort();
    }, timeoutDuration);

    try {
      // Prepare adaptive headers optimized for interserver and Amplify
    const fetchHeaders: Record<string, string> = {
      'Range': request.headers.get('range') || 'bytes=0-',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': 'video/mp4,video/webm,video/*,*/*;q=0.9',
      'Accept-Encoding': 'identity', // Prevent compression issues
      'Connection': 'keep-alive',
      'Referer': isInterserver ? url : 'https://main.dtxuefu2qzcuy.amplifyapp.com/',
      'Origin': 'https://main.dtxuefu2qzcuy.amplifyapp.com'
    };
    
    // Add specific headers for interserver hosting
    if (isInterserver) {
      fetchHeaders['X-Forwarded-For'] = request.headers.get('x-forwarded-for') || '127.0.0.1';
      fetchHeaders['X-Real-IP'] = request.headers.get('x-real-ip') || '127.0.0.1';
    }
    
    // Check if optimization is requested for slow connections
    const optimize = request.nextUrl.searchParams.get('optimize') === 'true';

      // Add quality-specific headers for adaptive streaming
      if (quality && quality !== 'auto') {
        fetchHeaders['X-Video-Quality'] = quality;
        if (bandwidth) {
          fetchHeaders['X-Bandwidth-Limit'] = bandwidth;
        }
      } else if (optimize) {
        // For slow connections with auto quality, default to lower quality
        fetchHeaders['X-Video-Quality'] = '360p';
        fetchHeaders['X-Bandwidth-Limit'] = '800';
      }

      // Optimize chunk sizes for Amplify and interserver hosting
      const range = request.headers.get('range');
      if (!range) {
        // Start with larger initial chunk for interserver, smaller for others
        const initialChunk = isInterserver ? 524288 : 262144; // 512KB for interserver, 256KB for others
        fetchHeaders['Range'] = `bytes=0-${initialChunk}`;
      } else if (optimize || (bandwidth && parseInt(bandwidth) < 800)) {
        // Adjust chunk sizes based on hosting and optimization
        const rangeMatch = range.match(/bytes=(\d+)-/);
        if (rangeMatch) {
          const start = parseInt(rangeMatch[1]);
          let chunkSize;
          if (isInterserver) {
            chunkSize = optimize ? 524288 : 1048576; // 512KB/1MB for interserver
          } else {
            chunkSize = optimize ? 262144 : 524288; // 256KB/512KB for others
          }
          const end = start + chunkSize;
          fetchHeaders['Range'] = `bytes=${start}-${end}`;
        }
      }

      // Add retry logic for interserver hosting
      let videoResponse;
      let retryCount = 0;
      const maxRetries = isInterserver ? 2 : 1;
      
      while (retryCount <= maxRetries) {
        try {
          videoResponse = await fetch(url, {
            signal: controller.signal,
            headers: fetchHeaders,
            redirect: 'follow', // Allow redirects for interserver
            cache: 'no-store'
          });
          
          if (videoResponse.ok || videoResponse.status === 206) {
            break; // Success, exit retry loop
          }
          
          if (retryCount < maxRetries && isInterserver) {
            console.log(`Video proxy: Retry ${retryCount + 1} for interserver URL:`, url);
            retryCount++;
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
          } else {
            break; // No more retries or not interserver
          }
        } catch (fetchError) {
          if (retryCount < maxRetries && isInterserver) {
            console.log(`Video proxy: Fetch error, retry ${retryCount + 1}:`, fetchError);
            retryCount++;
            await new Promise(resolve => setTimeout(resolve, 1000));
          } else {
            throw fetchError;
          }
        }
      }

      clearTimeout(timeoutId);

      // Handle redirects manually
      if (videoResponse && videoResponse.status >= 300 && videoResponse.status < 400) {
        const location = videoResponse.headers.get('location');
        if (location) {
          console.log('Video proxy: Following redirect to:', location);
          return NextResponse.redirect(location);
        }
      }

      if (!videoResponse || !videoResponse.ok) {
        console.error('Video proxy: Failed to fetch video, status:', videoResponse?.status || 'unknown');
        return new NextResponse(`Failed to fetch video: ${videoResponse?.status || 'unknown'}`, {
          status: videoResponse?.status || 500 
        });
      }

      const headers = new Headers();
      
      // Copy essential headers
      const contentType = videoResponse.headers.get('Content-Type');
      const contentLength = videoResponse.headers.get('Content-Length');
      const contentRange = videoResponse.headers.get('Content-Range');
      const acceptRanges = videoResponse.headers.get('Accept-Ranges');
      
      if (contentType) headers.set('Content-Type', contentType);
      if (contentLength) headers.set('Content-Length', contentLength);
      if (contentRange) headers.set('Content-Range', contentRange);
      if (acceptRanges) headers.set('Accept-Ranges', acceptRanges);
      
      // Enhanced CORS headers for video streaming - specifically for Amplify
      headers.set('Access-Control-Allow-Origin', '*');
      headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
      headers.set('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization, X-Requested-With, Cache-Control, Pragma');
      headers.set('Access-Control-Expose-Headers', 'Content-Length, Content-Range, Accept-Ranges, Content-Type, Cache-Control');
      headers.set('Access-Control-Allow-Credentials', 'false');
      headers.set('Cross-Origin-Resource-Policy', 'cross-origin');
      headers.set('Cross-Origin-Embedder-Policy', 'credentialless');
      
      // Force content-type for video if not set
      if (!headers.get('Content-Type')) {
        headers.set('Content-Type', 'video/mp4');
      }
      
      // Adaptive cache headers based on quality, bandwidth and optimization
      if (optimize) {
        // Shorter cache for optimized videos to allow quality changes
        headers.set('Cache-Control', 'public, max-age=1800, stale-while-revalidate=900');
      } else if (quality === '240p' || (bandwidth && parseInt(bandwidth) < 800)) {
        // Longer cache for low quality videos (better for slow connections)
        headers.set('Cache-Control', 'public, max-age=7200, stale-while-revalidate=3600');
      } else if (quality === '720p' || (bandwidth && parseInt(bandwidth) > 2000)) {
        // Shorter cache for high quality videos
        headers.set('Cache-Control', 'public, max-age=1800, stale-while-revalidate=900');
      } else {
        // Default cache
        headers.set('Cache-Control', 'public, max-age=3600, stale-while-revalidate=1800');
      }
      
      headers.set('Vary', 'Range, X-Video-Quality, X-Bandwidth-Limit');
      
      // Add quality information to response headers
      if (quality) {
        headers.set('X-Served-Quality', quality);
      }
      if (bandwidth) {
        headers.set('X-Served-Bandwidth', bandwidth);
      }
      // Add optimization info to response headers
      if (optimize) {
        headers.set('X-Video-Optimized', 'true');
      }

      console.log('Video proxy: Successfully proxying video, status:', videoResponse.status);

      return new NextResponse(videoResponse.body, {
        status: videoResponse.status,
        headers
      });
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError;
    }
  } catch (error) {
    console.error('Video proxy error:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return new NextResponse('Video request timeout', { status: 504 });
      }
      if (error.message.includes('fetch')) {
        return new NextResponse('Failed to fetch video source', { status: 502 });
      }
    }
    
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

export async function OPTIONS() {
  const headers = new Headers();
  
  // Enhanced CORS headers for video streaming
  headers.set('Access-Control-Allow-Origin', '*');
  headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization');
  headers.set('Access-Control-Expose-Headers', 'Content-Length, Content-Range, Accept-Ranges');
  headers.set('Access-Control-Max-Age', '86400'); // 24 hours
  
  return new NextResponse(null, {
    status: 204,
    headers
  });
}