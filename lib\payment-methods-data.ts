// Payment methods data extracted from payment-methods page
export interface PaymentMethodInfo {
  id: string;
  name: string;
  category: 'inside_iraq' | 'outside_iraq' | 'cash_on_delivery';
  details: {
    number?: string;
    email?: string;
    link?: string;
    instructions?: string;
    accountNumber?: string;
    bankName?: string;
  };
  icon?: string;
}

export const PAYMENT_METHODS_DATA: PaymentMethodInfo[] = [
  // Inside Iraq
  {
    id: 'zain_cash',
    name: 'Zain cash (Iraq)',
    category: 'inside_iraq',
    details: {
      number: '***********',
    },
    icon: '/Zaincash iraq.png'
  },
  {
    id: 'rafidain_bank',
    name: 'Rafidain Bank',
    category: 'inside_iraq',
    details: {
      accountNumber: '**********',
      bankName: 'Rafidain Bank',
    },
    icon: '/Qicard iraq.png'
  },
  {
    id: 'asia_pay',
    name: 'Asia Pay',
    category: 'inside_iraq',
    details: {
      number: '***********',
    },
    icon: '/Asia pay.png'
  },
  // Outside Iraq
  {
    id: 'paypal',
    name: 'PayPal',
    category: 'outside_iraq',
    details: {
      link: 'https://paypal.me/FatimahNaser?country.x=JO&locale.x=en_US',
      instructions: 'You can pay through this link👇',
    },
    icon: '/Paypal.png'
  },
  {
    id: 'amazon_gift',
    name: 'Amazon Gift',
    category: 'outside_iraq',
    details: {
      link: 'https://www.amazon.com/Amazon-eGift-Card-Logo-Animated/dp/B07PCMWTSG/ref=mp_s_a_1_1?adgrpid=************&dib=eyJ2IjoiMSJ9.y343JC2nqCCCtAt_MaFdYdSEoDk1IL8C8OVn3MADfESEozTH6jWzFIJ4WqlXn7_W-n2IrnPR-rfE3Spk4QYVuOOL7cvbuK9Esy0CXQivH6v0c4KW6RfZeH8pYn15Gdj-s62p0V-fiHzAE12D4YOgeY2zQf3sUuAQF30eHiR7nSfSyvGj9P0M79Suz3VRAqqxS64beG-r2SJhB_Y_apq-6Q.gbfTjpxr2hWpO9dWg-U8dthgvZM21cwxR6PrsZBpG38&dib_tag=se&hvadid=************&hvdev=m&hvlocphy=9211521&hvnetw=g&hvqmt=e&hvrand=15003258399388157606&hvtargid=kwd-2389411675177&hydadcr=22339_13333066&keywords=amazon%27+gift+card&mcid=90ecf431d9b83733b420d0f87065fc78&qid=1748814128&sr=8-1',
      email: '<EMAIL>',
      instructions: 'Please choose the amount and then send it to this email👇',
    },
    icon: '/Amazon gift card.png'
  },
  // Cash on Delivery
  {
    id: 'cash_on_delivery',
    name: 'Cash on Delivery',
    category: 'cash_on_delivery',
    details: {
      instructions: 'Pay in cash upon delivery - we offer delivery to all provinces within Iraq. Additional fees may apply depending on your location.',
    },
    icon: '/Cash on delivery.png'
  }
];

// Helper function to get payment method info by name
export function getPaymentMethodInfo(methodName: string): PaymentMethodInfo | null {
  const normalizedName = methodName.toLowerCase();
  
  return PAYMENT_METHODS_DATA.find(method => {
    const normalizedMethodName = method.name.toLowerCase();
    return normalizedMethodName.includes(normalizedName) || 
           normalizedName.includes(normalizedMethodName) ||
           method.id.includes(normalizedName.replace(/\s+/g, '_'));
  }) || null;
}

// Helper function to get related payment methods (for two-column display)
export function getRelatedPaymentMethods(methodName: string): { primary: PaymentMethodInfo | null, secondary: PaymentMethodInfo | null } {
  const normalizedName = methodName.toLowerCase();
  
  // Define related payment method pairs - Amazon Gift only shows with Amazon/PayPal
  const relatedPairs: Record<string, string> = {
    'zain': '', // No secondary for Zain Cash
    'asia': 'rafidain_bank', 
    'paypal': 'amazon_gift',
    'amazon': 'amazon_gift', // Amazon Gift shows with Amazon
    'rafidain': 'asia_pay',
    'bank': 'asia_pay'
  };
  
  const primary = getPaymentMethodInfo(methodName);
  let secondaryId = '';
  
  // Find the related payment method
  for (const [key, value] of Object.entries(relatedPairs)) {
    if (normalizedName.includes(key)) {
      secondaryId = value;
      break;
    }
  }
  
  const secondary = secondaryId ? PAYMENT_METHODS_DATA.find(method => method.id === secondaryId) || null : null;
  
  return { primary, secondary };
}