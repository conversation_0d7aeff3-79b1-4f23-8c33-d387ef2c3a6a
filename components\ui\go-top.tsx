'use client';

import { useState, useEffect } from 'react';
import { ChevronUp } from 'lucide-react';
import { useColorThemeContext } from '@/contexts/color-theme-context';

export function GoTop() {
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      if (currentScrollY > 500) {
        setVisible(true);
      } else {
        setVisible(false);
      }
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      behavior: 'smooth',
      top: 0,
    });
  };

  return (
    <button
      className={`fixed bottom-6 right-6 z-50 flex h-10 w-10 items-center justify-center rounded-full text-white shadow-md transition-opacity duration-300 ${visible ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
      style={{ backgroundColor: primaryColor }}
      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = `${primaryColor}e6`}
      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = primaryColor}
      onClick={scrollToTop}
      aria-label="Scroll to top"
    >
      <ChevronUp className="h-5 w-5" />
    </button>
  );
}