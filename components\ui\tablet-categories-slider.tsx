'use client';

import { X, ChevronLeft, ChevronRight, Package } from 'lucide-react';
import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';

interface TabletCategoriesSliderProps {
  onClose: () => void;
}

interface Category {
  CategoryID: string | number;
  ParentCategoryID: string | number | null;
  Name: string;
  Icon?: string;
  AttachmentURL?: string;
}

const categoryIcons = [
  '👕', '📱', '💻', '🏠', '❤️', '⭐', '🎁', '🎵', '📷', '🎮',
  '📚', '☕', '🛍️', '⌚', '🎧', '🚚', '🏆', '⚡', '☀️', '🌙'
];

export function TabletCategoriesSlider({ onClose }: TabletCategoriesSliderProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const sliderRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;

  const itemsPerSlide = 8; // 4x2 grid for tablet

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    setIsLoading(true);
    try {
      const response = await MakeApiCallAsync(
        Config.END_POINT_NAMES.GET_CATEGORIES_LIST,
        Config.COMMON_CONTROLLER_SUB_URL,
        {},
        {},
        'GET'
      );

      if (response && response.data) {
        const categoriesData = Array.isArray(response.data) ? response.data : [];
        const parentCategories = categoriesData
          .filter((cat: Category) => !cat.ParentCategoryID)
          .map((cat: Category, index: number) => ({
            ...cat,
            Icon: categoryIcons[index % categoryIcons.length] || '📦'
          }));
        
        setCategories(parentCategories);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCategorySelect = (category: Category) => {
    // Check if we're already on the products page
    const pathname = window.location.pathname;
    const isOnProductsPage = pathname === '/products' || pathname.startsWith('/products');
    
    if (isOnProductsPage) {
      // If on products page, force reload with new category
      window.location.href = `/products/?category=${category.CategoryID}`;
    } else {
      // If not on products page, navigate normally
      router.push(`/products/?category=${category.CategoryID}`);
    }
    onClose();
  };

  const totalSlides = Math.ceil(categories.length / itemsPerSlide);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
  };

  const getCurrentSlideCategories = () => {
    const startIndex = currentSlide * itemsPerSlide;
    return categories.slice(startIndex, startIndex + itemsPerSlide);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="hidden md:block lg:hidden fixed inset-0 bg-black/50 z-50"
      onClick={onClose}
    >
      <motion.div
        initial={{ y: '100%' }}
        animate={{ y: 0 }}
        exit={{ y: '100%' }}
        transition={{ type: 'spring', damping: 30, stiffness: 300 }}
        className="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl h-[60vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <h2 className="text-2xl font-bold text-gray-800">
            {t?.('categories') || 'التصنيفات'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <X className="w-6 h-6 text-gray-600" />
          </button>
        </div>

        {/* Categories Slider */}
        <div className="flex-1 p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2" style={{ borderTopColor: primaryColor, borderBottomColor: primaryColor }}></div>
            </div>
          ) : categories.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              لا توجد تصنيفات متاحة
            </div>
          ) : (
            <div className="relative h-full">
              {/* Categories Grid */}
              <div 
                ref={sliderRef}
                className="grid grid-cols-4 grid-rows-2 gap-4 h-full transition-all duration-300"
              >
                {getCurrentSlideCategories().map((category) => (
                  <motion.button
                    key={category.CategoryID}
                    onClick={() => handleCategorySelect(category)}
                    className="flex flex-col items-center justify-center p-4 rounded-2xl border-2 border-gray-100 transition-all duration-300 hover:scale-105 hover:shadow-lg"
                    style={{
                      '--hover-border-color': '#93c5fd',
                      '--hover-bg-color': '#eff6ff'
                    } as React.CSSProperties}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#93c5fd';
                      e.currentTarget.style.backgroundColor = '#eff6ff';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#f3f4f6';
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <span className="text-4xl mb-3">{category.Icon}</span>
                    <span className="text-sm font-medium text-center text-gray-700 leading-tight">
                      {category.Name}
                    </span>
                  </motion.button>
                ))}
              </div>

              {/* Navigation Arrows */}
              {totalSlides > 1 && (
                <>
                  <button
                    onClick={prevSlide}
                    className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
                    disabled={currentSlide === 0}
                  >
                    <ChevronLeft className="w-6 h-6 text-gray-600" />
                  </button>
                  <button
                    onClick={nextSlide}
                    className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
                    disabled={currentSlide === totalSlides - 1}
                  >
                    <ChevronRight className="w-6 h-6 text-gray-600" />
                  </button>
                </>
              )}

              {/* Slide Indicators */}
              {totalSlides > 1 && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {Array.from({ length: totalSlides }).map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        index === currentSlide
                          ? 'bg-blue-500 scale-125'
                          : 'bg-gray-300 hover:bg-gray-400'
                      }`}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
}