'use client';

import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { useContactInfo } from '@/contexts/contact-info';
import { Button } from './button';
import Link from 'next/link';
import { Phone } from 'lucide-react';

interface ContactBannerProps {
  className?: string;
}

export function ContactBanner({ className = '' }: ContactBannerProps) {
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const primaryTextColor = currentTheme.primaryForeground;
  const { phoneNumber } = useContactInfo();

  return (
    <section
      className={`py-8 sm:py-12 md:py-16 ${className}`}
      style={{ backgroundColor: primaryColor, color: primaryTextColor }}
    >
      <div className="container mx-auto px-4 sm:px-6">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4 sm:gap-6">
          <div className="text-center md:text-left mb-4 md:mb-0">
            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 sm:mb-3">
              Need Help With Your Order?
            </h2>
            <p className="text-white/80 max-w-xl text-sm sm:text-base">
              Our customer support team is here to answer your questions. Contact us for assistance.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto">
            <Button asChild variant="outline" className="bg-white/10 border-white hover:bg-white/20 w-full sm:w-auto">
              <Link href="/contact" className="min-w-[120px] sm:min-w-[140px] justify-center text-sm sm:text-base py-2">
                Contact Us
              </Link>
            </Button>

            <Button asChild variant="secondary" className="bg-white text-primary hover:bg-white/90 w-full sm:w-auto">
              <Link href={`tel:${phoneNumber}`} className="min-w-[120px] sm:min-w-[140px] justify-center text-sm sm:text-base py-2">
                <Phone className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                Call Us
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}