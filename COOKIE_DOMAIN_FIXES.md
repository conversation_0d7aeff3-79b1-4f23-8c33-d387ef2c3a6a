# Cookie Domain Fixes for Production Deployment

## Problem Solved
Fixed login authentication issues that occurred when deploying to Vercel or AWS Amplify where cookies were not being set properly due to incorrect domain configuration.

## Changes Made

### 1. Updated Cookie Domain Configuration
- **Files Modified:**
  - `app/api/auth/set-cookies/route.ts`
  - `app/api/auth/clear-cookies/route.ts`
  - `app/api/auth/update-user-cookies/route.ts`

- **Key Changes:**
  - Dynamic domain detection using environment variables and request headers
  - Proper domain prefix with dot (.) for cross-subdomain support
  - Fallback chain: `NEXT_PUBLIC_DOMAIN` → `VERCEL_URL` → `request.headers.get('host')`

### 2. Updated SameSite Policy
- Changed from `sameSite: 'strict'` to `sameSite: 'lax'`
- Better support for cross-origin authentication flows

### 3. Enhanced Middleware
- **File Modified:** `middleware.ts`
- Extended CORS support to all API routes (not just SMS)
- Added support for VERCEL_URL in allowed origins
- Added credentials support for cookie handling

## Environment Variables Required

### For Vercel:
1. Go to Vercel Dashboard → Your Project → Settings → Environment Variables
2. Add the following:

```
NEXT_PUBLIC_DOMAIN=your-domain.vercel.app
ENCRYPTION_KEY=your-secure-encryption-key-here
NEXT_PUBLIC_ADMIN_BASE_URL=your-backend-api-url
```

### For AWS Amplify:
1. Go to Amplify Console → Your App → Environment Variables
2. Add the same variables:

```
NEXT_PUBLIC_DOMAIN=your-app.amplifyapp.com
ENCRYPTION_KEY=your-secure-encryption-key-here
NEXT_PUBLIC_ADMIN_BASE_URL=your-backend-api-url
```

## How It Works

### Before (Broken):
```typescript
const domain = process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_DOMAIN 
  ? process.env.NEXT_PUBLIC_DOMAIN 
  : undefined;
```

### After (Fixed):
```typescript
let domain = undefined;
if (process.env.NODE_ENV === 'production') {
  domain = process.env.NEXT_PUBLIC_DOMAIN || 
           process.env.VERCEL_URL ||
           request.headers.get('host')?.replace('www.', '');
  
  if (domain && !domain.startsWith('.')) {
    domain = `.${domain}`;
  }
}
```

## Testing

1. **Deploy to your platform** (Vercel/Amplify)
2. **Check browser dev tools** → Application → Cookies
3. **Verify cookies have correct domain** (should show `.your-domain.com`)
4. **Test login flow** - should persist across page refreshes
5. **Check console logs** for cookie setting confirmations

## Debugging

If issues persist:

1. **Check environment variables are set** in platform dashboard
2. **Inspect cookie domain in browser dev tools**
3. **Look for console errors** during login process
4. **Verify backend API connectivity**

## Domain Examples

- **Vercel:** `.your-app.vercel.app`
- **Amplify:** `.your-app.amplifyapp.com`
- **Custom Domain:** `.yourdomain.com`

The dot prefix ensures cookies work across all subdomains (www, api, etc.).