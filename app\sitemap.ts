import { MetadataRoute } from 'next'
import { generateCanonicalUrl, generateProductCanonicalUrl, generateCampaignCanonicalUrl, generateCategoryCanonicalUrl } from '@/lib/canonical-utils'
// Using Next.js API route for product fetching instead of direct external API calls

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://codemedicalapps.com'
  
  // Static routes with canonical URLs
  const staticRoutes: MetadataRoute.Sitemap = [
    {
      url: generateCanonicalUrl(''),
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 1,
    },
    {
      url: generateCanonicalUrl('about'),
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: generateCanonicalUrl('products'),
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: generateCanonicalUrl('hot-deals'),
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: generateCanonicalUrl('contact'),
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: generateCanonicalUrl('terms'),
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: generateCanonicalUrl('follow-us'),
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: generateCanonicalUrl('login'),
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: generateCanonicalUrl('signup'),
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
  ]

  // Dynamic routes for categories and products
  const dynamicRoutes: MetadataRoute.Sitemap = []

  try {
    // Fetch categories using Next.js API route
    const categoriesResponse = await fetch(`${baseUrl}/api/categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        requestParameters: {
          recordValueJson: "[]",
        },
      }),
    });

    if (categoriesResponse.ok) {
      const categoriesData = await categoriesResponse.json();
      
      if (categoriesData?.data) {
        let categories = [];
        
        if (typeof categoriesData.data === 'string') {
          categories = JSON.parse(categoriesData.data);
        } else if (Array.isArray(categoriesData.data)) {
          categories = categoriesData.data;
        }
        
        // Add category routes
        categories.forEach((category: any) => {
          dynamicRoutes.push({
            url: generateCategoryCanonicalUrl(category.CategoryID.toString(), category.CategoryName || 'category'),
            lastModified: new Date(),
            changeFrequency: 'weekly',
            priority: 0.8,
          });
        });
      }
    }

    // Try to fetch real products for sitemap using Next.js API route
    try {
      const requestData = {
        requestParameters: {
          SearchTerm: '',
          CategoryID: null,
          TagID: null,
          ManufacturerID: null,
          MinPrice: null,
          MaxPrice: null,
          Rating: null,
          OrderByColumnName: 0,
          PageNo: 1,
          PageSize: 100,
          recordValueJson: '[]',
          producttypeId: null,
        },
      };

      const response = await fetch(`${baseUrl}/api/products/get-products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        const data = await response.json();
        
        if (data && data.data) {
          let productsData: any[] = [];
          
          if (typeof data.data === 'string') {
            productsData = JSON.parse(data.data);
          } else if (Array.isArray(data.data)) {
            productsData = data.data;
          } else {
            productsData = [data.data];
          }
          
          if (Array.isArray(productsData) && productsData.length > 0) {
            const validProductIds = productsData
              .map((product: any) => product.ProductID || product.ProductId)
              .filter((id: any) => id && typeof id === 'number')
              .slice(0, 100);
            
            if (validProductIds.length > 0) {
              validProductIds.forEach((productId: number) => {
                const product = productsData.find((p: any) => (p.ProductID || p.ProductId) === productId);
                const productTitle = product?.ProductName || product?.Title || 'product';
                dynamicRoutes.push({
                  url: generateProductCanonicalUrl(productId.toString(), productTitle),
                  lastModified: new Date(),
                  changeFrequency: 'weekly' as const,
                  priority: 0.7,
                });
              });
              console.log(`Added ${validProductIds.length} dynamic product URLs to sitemap`);
            }
          }
        }
      } else {
        console.log('API route failed with status:', response.status);
      }
      
    } catch (error) {
      console.error('Error fetching products for sitemap:', error);
      
      // Fallback: Add representative product URLs for SEO
      // These are placeholder IDs that should be replaced with real product IDs
      // once the API is working properly
      const fallbackProductIds = [
        // Sample range 1: 100-109
        100, 101, 102, 103, 104, 105, 106, 107, 108, 109,
        // Sample range 2: 200-209  
        200, 201, 202, 203, 204, 205, 206, 207, 208, 209,
        // Sample range 3: 300-309
        300, 301, 302, 303, 304, 305, 306, 307, 308, 309
      ];
      
      fallbackProductIds.forEach(productId => {
        dynamicRoutes.push({
          url: generateProductCanonicalUrl(productId.toString(), 'product'),
          lastModified: new Date(),
          changeFrequency: 'weekly' as const,
          priority: 0.7,
        });
      });
    }
  } catch (error) {
    console.error('Error fetching dynamic routes for sitemap:', error)
    // Continue with static routes only if API calls fail
  }

  return [...staticRoutes, ...dynamicRoutes]
}