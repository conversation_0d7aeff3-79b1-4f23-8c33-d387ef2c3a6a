'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';

const categories = [
  { id: 1, name: 'DRESSES & SKIRTS', image: '/categories/dresses.jpg', href: '/category/dresses-skirts' },
  { id: 2, name: 'SANDALS AND SLIPPERS', image: '/categories/sandals.jpg', href: '/category/sandals-slippers' },
  { id: 3, name: 'PANTS & JEANS', image: '/categories/pants.jpg', href: '/category/pants-jeans' },
  { id: 4, name: 'SHIRTS & POLO', image: '/categories/shirts.jpg', href: '/category/shirts-polo' },
  { id: 5, name: 'MENS WATCHES', image: '/categories/watches.jpg', href: '/category/mens-watches' },
  { id: 6, name: 'SWEATERS', image: '/categories/sweaters.jpg', href: '/category/sweaters' },
];

export function Categories() {
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;

  return (
    <div className="py-8 px-4">
      <h2 className="text-2xl font-bold text-center mb-8">POPULAR CATEGORIES!</h2>
      <div className="flex overflow-x-auto gap-6 pb-4 no-scrollbar">
        {categories.map((category) => (
          <Link
            key={category.id}
            href={category.href}
            className="flex flex-col items-center space-y-4 min-w-[160px] group"
          >
            <div className="relative w-[160px] h-[160px] rounded-lg overflow-hidden bg-accent/10 transition-all duration-300 group-hover:scale-105 shadow-md">
              <Image
                src={category.image}
                alt={category.name}
                fill
                className="object-cover"
                sizes="160px"
              />
              <div className="absolute inset-0 bg-black/30 transition-opacity duration-300 group-hover:bg-black/40" />
              <div className="absolute inset-0 flex items-center justify-center p-4">
                <span className="text-white text-center font-semibold text-sm tracking-wide">
                  {category.name}
                </span>
              </div>
            </div>
            <button 
              className="px-4 py-2 text-white rounded-full text-sm font-medium transition-colors"
              style={{ backgroundColor: primaryColor }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = `${primaryColor}e6`}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = primaryColor}
            >
              Shop Now
            </button>
          </Link>
        ))}
      </div>
    </div>
  );
}