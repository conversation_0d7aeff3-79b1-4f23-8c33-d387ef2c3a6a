'use client';

import React from 'react';
import { useSettings } from '@/contexts/settings-context';
import { Card } from '@/components/ui/card';
import { Truck, RefreshCcw, HeadphonesIcon, CreditCard } from 'lucide-react';

interface ServiceItem {
  icon: React.ReactElement;
  title: string;
  description: string;
}

export function ProductService() {
  const { t } = useSettings();

  const services: ServiceItem[] = [
    {
      icon: <Truck className="h-8 w-8" />,
      title: 'Free Shipping',
      description: 'Free shipping on all orders over $50'
    },
    {
      icon: <RefreshCcw className="h-8 w-8" />,
      title: 'Money Return',
      description: '30-day money-back guarantee'
    },
    {
      icon: <HeadphonesIcon className="h-8 w-8" />,
      title: '24/7 Support',
      description: 'Customer support available 24/7'
    },
    {
      icon: <CreditCard className="h-8 w-8" />,
      title: 'Secure Payment',
      description: 'Multiple secure payment options'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {services.map((service, index) => (
        <Card key={index} className="p-6 text-center hover:shadow-lg transition-shadow">
          <div className="flex flex-col items-center space-y-4">
            <div className="text-primary">{service.icon}</div>
            <div>
              <h3 className="font-medium text-lg mb-2">{service.title}</h3>
              <p className="text-muted-foreground text-sm">{service.description}</p>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}