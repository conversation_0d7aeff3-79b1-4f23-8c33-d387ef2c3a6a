/**
 * Rate limiter for SMS verification to prevent abuse
 */

interface RateLimitEntry {
  count: number;
  resetTime: number;
  lastAttempt: number;
}

class RateLimiter {
  private attempts: Map<string, RateLimitEntry> = new Map();
  private readonly maxAttempts: number;
  private readonly windowMs: number;
  private readonly blockDurationMs: number;

  constructor(maxAttempts = 3, windowMs = 15 * 60 * 1000, blockDurationMs = 60 * 60 * 1000) {
    this.maxAttempts = maxAttempts; // 3 attempts
    this.windowMs = windowMs; // 15 minutes window
    this.blockDurationMs = blockDurationMs; // 1 hour block
  }

  /**
   * Check if a phone number is rate limited
   */
  isRateLimited(phoneNumber: string): { limited: boolean; resetTime?: number; remainingAttempts?: number } {
    const now = Date.now();
    const entry = this.attempts.get(phoneNumber);

    if (!entry) {
      return { limited: false, remainingAttempts: this.maxAttempts };
    }

    // If block period has expired, reset
    if (now > entry.resetTime) {
      this.attempts.delete(phoneNumber);
      return { limited: false, remainingAttempts: this.maxAttempts };
    }

    // If within window and exceeded max attempts
    if (entry.count >= this.maxAttempts) {
      return { limited: true, resetTime: entry.resetTime };
    }

    return { limited: false, remainingAttempts: this.maxAttempts - entry.count };
  }

  /**
   * Record an attempt for a phone number
   */
  recordAttempt(phoneNumber: string): void {
    const now = Date.now();
    const entry = this.attempts.get(phoneNumber);

    if (!entry || now > entry.resetTime) {
      // First attempt or expired window
      this.attempts.set(phoneNumber, {
        count: 1,
        resetTime: now + this.blockDurationMs,
        lastAttempt: now
      });
    } else {
      // Increment existing entry
      entry.count++;
      entry.lastAttempt = now;
      this.attempts.set(phoneNumber, entry);
    }
  }

  /**
   * Clean up expired entries (call periodically)
   */
  cleanup(): void {
    const now = Date.now();
    Array.from(this.attempts.entries()).forEach(([phoneNumber, entry]) => {
      if (now > entry.resetTime) {
        this.attempts.delete(phoneNumber);
      }
    });
  }

  /**
   * Get current status for a phone number
   */
  getStatus(phoneNumber: string): { attempts: number; resetTime: number; blocked: boolean } {
    const entry = this.attempts.get(phoneNumber);
    if (!entry) {
      return { attempts: 0, resetTime: 0, blocked: false };
    }

    const now = Date.now();
    const blocked = entry.count >= this.maxAttempts && now < entry.resetTime;

    return {
      attempts: entry.count,
      resetTime: entry.resetTime,
      blocked
    };
  }
}

// Global rate limiter instance
export const smsRateLimiter = new RateLimiter();

// Cleanup expired entries every 10 minutes
setInterval(() => {
  smsRateLimiter.cleanup();
}, 10 * 60 * 1000);