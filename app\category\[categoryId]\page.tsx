'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Card } from '@/components/ui/card';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { useCurrency } from '@/contexts/currency-context';
import { MakeApiCallAsync, Config, GetTokenForHeader } from "@/lib/api-helper";

import { Product, ApiResponse } from '@/types/product';

type DisplayProduct = {
  id: number;
  name: string;
  price: number;
  discountPrice?: number;
  image?: string;
  description?: string;
  categoryId: number;
};

export default function CategoryPage() {
  const { categoryId } = useParams() as { categoryId: string };
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const [products, setProducts] = useState<DisplayProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [categoryName, setCategoryName] = useState('');

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const param = {
          "PageNumber": 1,
          "PageSize": 100,
          "SortColumn": "Name",
          "SortOrder": "ASC",
          "CategoryId": categoryId
        };
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer ' + (await GetTokenForHeader() || '')
        };
        const response = await MakeApiCallAsync('get-products-list', null, param, headers, "POST", true);
        
        if (response?.data?.data) {
          const parsedData = JSON.parse(response.data.data) as Product[];
          const displayProducts: DisplayProduct[] = parsedData.map(product => ({
            id: product.ProductId,
            name: product.ProductName,
            price: product.Price,
            discountPrice: product.DiscountedPrice || undefined,
            image: product.ProductImagesJson?.[0]?.AttachmentURL,
            description: product.ShortDescription || undefined,
            categoryId: product.CategoryID
          }));
          setProducts(displayProducts);
          if (parsedData.length > 0) {
            setCategoryName(parsedData[0].CategoryName || '');
          }
        }
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [categoryId]);

  if (loading) {
    return <div className="container mx-auto py-8 px-4">Loading...</div>;
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">{t('home')}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{categoryName}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">{categoryName}</h1>
        
        {products.length === 0 ? (
          <Card className="p-6 text-center">
            <p className="text-muted-foreground">No products found in this category.</p>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <Link href={`/product/${product.id}`}>
                  <div className="aspect-[3/4] relative bg-muted">
                    <img
                      src={product.image ? (product.image.startsWith('http') ? product.image : product.image.startsWith('/') ? `${Config.ADMIN_BASE_URL.replace(/\/$/, '')}${product.image}` : `${Config.ADMIN_BASE_URL.replace(/\/$/, '')}/${product.image}`) : `/products/book${product.id}.jpg`}
                      alt={product.name}
                      className="object-cover w-full h-full"
                      onError={(e) => {
                        // Fallback to local image if remote image fails to load
                        const target = e.target as HTMLImageElement;
                        target.onerror = null; // Prevent infinite loop
                        target.src = `/products/book${product.id}.jpg`;
                      }}
                    />
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium mb-2 line-clamp-2">{product.name}</h3>
                    <div className="flex items-baseline gap-2">
                      {product.discountPrice ? (
                        <>
                          <span className="text-lg font-bold"  style={{ color: '#ff0000' }}>
                            ${product.discountPrice.toFixed(2)}
                          </span>
                          <span className="text-sm text-muted-foreground line-through">
                            ${product.price.toFixed(2)}
                          </span>
                        </>
                      ) : (
                        <span className="text-lg font-bold" style={{ color: primaryColor }}>
                          ${product.price.toFixed(2)}
                        </span>
                      )}
                    </div>
                  </div>
                </Link>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}