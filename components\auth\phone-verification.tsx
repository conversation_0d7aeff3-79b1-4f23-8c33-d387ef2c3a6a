'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Phone, Shield } from 'lucide-react';
import { sendSMSVerification, verifySMSCode, formatPhoneNumber, validatePhoneNumber } from '@/lib/sms-auth';
import { useColorThemeContext } from '@/contexts/color-theme-context';

interface PhoneVerificationProps {
  onVerificationSuccess: (phoneNumber: string) => void;
  onError?: (error: string) => void;
}

export default function PhoneVerification({ onVerificationSuccess, onError }: PhoneVerificationProps) {
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const [step, setStep] = useState<'phone' | 'code'>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleSendCode = async () => {
    if (!phoneNumber.trim()) {
      setError('Please enter a phone number');
      return;
    }

    const formattedPhone = formatPhoneNumber(phoneNumber);
    
    if (!validatePhoneNumber(formattedPhone)) {
      setError('Please enter a valid phone number');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const result = await sendSMSVerification(formattedPhone);
      
      if (result.success) {
        setSuccess('Verification code sent successfully!');
        setStep('code');
        setPhoneNumber(formattedPhone);
      } else {
        setError(result.message);
        onError?.(result.message);
      }
    } catch (err) {
      const errorMessage = 'Failed to send verification code';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!verificationCode.trim()) {
      setError('Please enter the verification code');
      return;
    }

    if (verificationCode.length !== 6) {
      setError('Verification code must be 6 digits');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await verifySMSCode(phoneNumber, verificationCode);
      
      if (result.success) {
        setSuccess('Phone number verified successfully!');
        onVerificationSuccess(phoneNumber);
      } else {
        setError(result.message);
        onError?.(result.message);
      }
    } catch (err) {
      const errorMessage = 'Failed to verify code';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    setVerificationCode('');
    await handleSendCode();
  };

  const handleBackToPhone = () => {
    setStep('phone');
    setVerificationCode('');
    setError('');
    setSuccess('');
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 rounded-full flex items-center justify-center mb-4" style={{ backgroundColor: `${primaryColor}1A` }}>
          {step === 'phone' ? (
            <Phone className="w-6 h-6 text-primary" />
          ) : (
            <Shield className="w-6 h-6 text-primary" />
          )}
        </div>
        <CardTitle>
          {step === 'phone' ? 'Verify Phone Number' : 'Enter Verification Code'}
        </CardTitle>
        <CardDescription>
          {step === 'phone' 
            ? 'We\'ll send you a verification code via SMS'
            : `Enter the 6-digit code sent to ${phoneNumber}`
          }
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {step === 'phone' ? (
          <div className="space-y-4">
            <div>
              <label htmlFor="phone" className="block text-sm font-medium mb-2">
                Phone Number
              </label>
              <Input
                id="phone"
                type="tel"
                placeholder="+****************"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                disabled={loading}
                autoComplete="tel"
                inputMode="tel"
                className="h-12 md:h-10 text-lg md:text-base"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Include country code (e.g., +1 for US)
              </p>
            </div>

            <Button 
              onClick={handleSendCode} 
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Sending Code...
                </>
              ) : (
                'Send Verification Code'
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <label htmlFor="code" className="block text-sm font-medium mb-2">
                Verification Code
              </label>
              <Input
                id="code"
                type="text"
                placeholder="123456"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                disabled={loading}
                maxLength={6}
                autoComplete="one-time-code"
                inputMode="numeric"
                className="text-center text-xl md:text-base h-16 md:h-10 tracking-widest font-mono px-4"
              />
            </div>

            <Button 
              onClick={handleVerifyCode} 
              disabled={loading || verificationCode.length !== 6}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Verifying...
                </>
              ) : (
                'Verify Code'
              )}
            </Button>

            <div className="flex justify-between text-sm">
              <Button 
                variant="ghost" 
                onClick={handleBackToPhone}
                disabled={loading}
              >
                Change Number
              </Button>
              <Button 
                variant="ghost" 
                onClick={handleResendCode}
                disabled={loading}
              >
                Resend Code
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}