"use client";

import { useEffect, useState } from "react";
import { MakeApiCallAsync, Config } from "@/lib/api-helper";
import { ImageSlider } from "./image-slider";
import { But<PERSON> } from "./button";
import Link from "next/link";

type Banner = {
  BannerID: number;
  TopTitle: string;
  MainTitle: string;
  BottomTitle: string;
  LeftButtonText: string;
  RightButtonText: string;
  BannerImgUrl: string;
  LeftButtonUrl?: string;
  RightButtonUrl?: string;
  ThemeTypeID?: number;
};

type BannerSliderProps = {
  className?: string;
};

export function BannerSlider({ className }: BannerSliderProps) {
  const [banners, setBanners] = useState<Banner[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const onMouseHover = (e: React.MouseEvent) => {
    // This function is not used by ImageSlider directly, but keeping it for now if it's used elsewhere.
    // If not used, it can be removed.
  };

  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await MakeApiCallAsync(
          Config.END_POINT_NAMES.GET_HOME_SCREEN_BANNER,
          null,
          {
            PageNumber: 1,
            PageSize: 100,
            SortColumn: "BannerID",
            SortOrder: "ASC",
          },
          {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          "POST",
          true
        );

        console.log("API Response:", response);

        if (response?.data) {
          try {
            let bannerData = response.data;
            if (typeof bannerData === "string") {
              bannerData = JSON.parse(bannerData);
            }

            // Check if bannerData has a data property that needs to be parsed
            if (bannerData.data && typeof bannerData.data === "string") {
              bannerData = JSON.parse(bannerData.data);
            }

            console.log("Parsed banner data:", bannerData);

            // Now bannerData should be the array we need
            if (Array.isArray(bannerData)) {
              const processedBanners = bannerData.map((banner: Banner) => ({
                ...banner,
                BannerImgUrl: banner.BannerImgUrl?.startsWith("http")
                  ? banner.BannerImgUrl
                  : `https://admin.codemedicalapps.com${
                      banner.BannerImgUrl || ""
                    }`,
              }));
              setBanners(processedBanners);
            } else if (bannerData.data && Array.isArray(bannerData.data)) {
              // If the array is nested in a data property
              const processedBanners = bannerData.data.map(
                (banner: Banner) => ({
                  ...banner,
                  BannerImgUrl: banner.BannerImgUrl?.startsWith("http")
                    ? banner.BannerImgUrl
                    : `https://admin.codemedicalapps.com${
                        banner.BannerImgUrl || ""
                      }`,
                })
              );
              setBanners(processedBanners);
            } else {
              console.error(
                "Banner data is not in expected format:",
                bannerData
              );
              setBanners([]);
            }
          } catch (error) {
            console.error("Error processing banner data:", error);
            setError("Failed to process banner data");
            setBanners([]);
          }
        } else {
          console.error("Invalid or empty response from API");
          setError("Failed to load banners");
          setBanners([]);
        }
      } catch (error) {
        console.error("Error fetching banners:", error);
        setError("Failed to fetch banners");
        setBanners([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBanners();
  }, []);

  if (isLoading) {
    return (
      <div
        className={`relative w-full h-[250px] md:h-[386px] overflow-hidden rounded-xl bg-accent/10 animate-pulse ${className}`}
      >
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (error || banners.length === 0) {
    // Use a mock banner if no banners are available from the API
    const mockBanners: Banner[] = [
      {
        BannerID: 1,
        TopTitle: "TOP TITLE",
        MainTitle: "main title",
        BottomTitle: "bottom title",
        LeftButtonText: "test",
        RightButtonText: "test right",
        BannerImgUrl:
          "https://placehold.co/1200x450/333333/FFFFFF?text=Banner+Image",
        LeftButtonUrl: "#",
        ThemeTypeID: 1,
      },
    ];

    setBanners(mockBanners);

    // If there's an actual error, show the error message
    if (error) {
      return (
        <div
          className={`relative w-full h-[250px] md:h-[386px] overflow-hidden rounded-xl bg-accent/5 flex items-center justify-center ${className}`}
        >
          <div className="text-center p-8">
            <h3 className="text-xl font-semibold mb-2">
              Failed to load banners
            </h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </div>
        </div>
      );
    }
  }

  const sliderImages = banners.map((banner) => ({
    url: banner.BannerImgUrl,
    alt: banner.MainTitle || "Banner",
    id: banner.BannerID,
    leftButtonUrl: banner.LeftButtonUrl,
    rightButtonUrl: banner.RightButtonUrl,
  }));

  const handleSlideChange = (index: number) => {
    setCurrentIndex(index);
  };

  const currentSlide = sliderImages[currentIndex];
  const currentSlideUrl = currentSlide?.rightButtonUrl || currentSlide?.leftButtonUrl;

  return (
    <div className={`relative w-full h-[250px] md:h-[386px] overflow-hidden rounded-xl ${className}`}>
      <ImageSlider
        images={sliderImages}
        onSlideChange={handleSlideChange}
      />
      {currentSlideUrl && (
        <Link href={currentSlideUrl} className="absolute inset-0 z-10 cursor-pointer" style={{ pointerEvents: 'none' }}>
          <div className="w-full h-full" style={{ pointerEvents: 'auto' }} />
        </Link>
      )}
    </div>
  );
}
