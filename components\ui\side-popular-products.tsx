'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useSettings } from '@/contexts/settings-context';
import { ProductRatingStars } from '@/components/ui/product-rating-stars';

import { Product as ApiProduct, ApiResponse } from '@/types/product';
import { Config } from '@/lib/config';

interface DisplayProduct {
  id: number;
  name: string;
  slug: string;
  price: number;
  discountedPrice?: number;
  images: {
    id: string;
    url: string;
    alt: string;
  }[];
  rating: number;
  categoryName: string;
  categorySlug: string;
}

interface SidePopularProductsProps {
  title?: string;
  limit?: number;
}

export function SidePopularProducts({ title = 'popularProducts', limit = 5 }: SidePopularProductsProps) {
  const { t } = useSettings();
  const [products, setProducts] = useState<DisplayProduct[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPopularProducts = async () => {
      setLoading(true);
      try {
        // TODO: Replace with actual API call
        const response = await fetch(`${Config.ADMIN_BASE_URL}api/products/popular?limit=${limit}`);
        const data = await response.json() as ApiResponse<string>;
        const parsedProducts = JSON.parse(data.data) as ApiProduct[];

        const displayProducts: DisplayProduct[] = parsedProducts.map(product => ({
          id: product.ProductId,
          name: product.ProductName,
          slug: product.ProductName.toLowerCase().replace(/ /g, '-'),
          price: product.Price,
          discountedPrice: product.DiscountedPrice || undefined,
          images: product.ProductImagesJson?.map(img => ({
            id: img.AttachmentName,
            url: img.AttachmentURL,
            alt: product.ProductName
          })) || [],
          rating: product.Rating,
          categoryName: product.CategoryName,
          categorySlug: product.CategoryName.toLowerCase().replace(/ /g, '-')
        }));
        setProducts(displayProducts);
      } catch (error) {
        console.error('Error fetching popular products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPopularProducts();
  }, [limit]);

  if (loading) {
    return (
      <Card className="p-3 sm:p-4">
        <Skeleton className="h-5 sm:h-6 w-1/2 mb-3 sm:mb-4" />
        <div className="space-y-3 sm:space-y-4">
          {Array.from({ length: limit }).map((_, index) => (
            <div key={index} className="flex space-x-3 sm:space-x-4">
              <Skeleton className="h-16 sm:h-20 w-16 sm:w-20 rounded" />
              <div className="flex-1 space-y-1.5 sm:space-y-2">
                <Skeleton className="h-3 sm:h-4 w-3/4" />
                <Skeleton className="h-3 sm:h-4 w-1/2" />
              </div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  if (products.length === 0) {
    return null;
  }

  return (
    <Card className="p-4">
      <h3 className="font-medium text-lg mb-4">{title}</h3>
      <div className="space-y-4">
        {products.map((product) => (
          <Link
            key={product.id}
            href={`/product/${product.slug}`}
            className="flex space-x-4 group"
          >
            <div className="relative h-20 w-20 overflow-hidden rounded">
              <Image
                src={product.images[0].url.startsWith('http') ? product.images[0].url : `${Config.ADMIN_BASE_URL}${product.images[0].url.startsWith('/') ? product.images[0].url.substring(1) : product.images[0].url}`}
                alt={product.images[0].alt}
                fill
                className="object-cover transition-transform group-hover:scale-105"
              />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-sm group-hover:text-primary line-clamp-2">
                {product.name}
              </h4>
              <div className="mt-1">
                <ProductRatingStars rating={product.rating} size="sm" />
              </div>
              <div className="mt-1 space-x-2">
                {product.discountedPrice ? (
                  <>
                    <span className="font-bold text-primary">
                      ${product.discountedPrice.toFixed(2)}
                    </span>
                    <span className="text-sm text-muted-foreground line-through">
                      ${product.price.toFixed(2)}
                    </span>
                  </>
                ) : (
                  <span className="font-bold text-primary">
                    ${product.price.toFixed(2)}
                  </span>
                )}
              </div>
            </div>
          </Link>
        ))}
      </div>
    </Card>
  );
}