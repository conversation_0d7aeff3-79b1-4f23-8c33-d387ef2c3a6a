import { NextRequest, NextResponse } from "next/server";
import { initializeTwilio } from "@/lib/twilio";
import { isValidPhoneNumber, formatPhoneNumber } from "@/lib/phone-utils";
import { smsRateLimiter } from "@/lib/rate-limiter";
import {
  validatePhoneNumber,
  generateSecureCode,
  detectBot,
  validateOrigin,
  checkSuspiciousIP,
} from "@/lib/security-utils";
import { securityMonitor } from "@/lib/security-monitor";
import { storeVerificationCode, trackVerificationAttempt } from "@/lib/verification-store";

export async function POST(request: NextRequest) {
  try {
    // Security checks
    const headers = request.headers;
    const ip =
      headers.get("x-forwarded-for") || headers.get("x-real-ip") || "unknown";

    // Check if IP should be blocked
    if (securityMonitor.shouldBlockIP(ip)) {
      securityMonitor.logEvent({
        type: "multiple_failures",
        ip,
        userAgent: headers.get("user-agent") || undefined,
        details: "IP temporarily blocked due to multiple security violations",
      });
      return NextResponse.json(
        { error: "Request blocked for security reasons" },
        { status: 403 }
      );
    }

    // 1. Bot detection
    const botCheck = detectBot(headers);
    if (botCheck.isBot) {
      securityMonitor.logEvent({
        type: "bot_detected",
        ip,
        userAgent: headers.get("user-agent") || undefined,
        details: botCheck.reason || "Bot detected",
      });
      return NextResponse.json(
        { error: "Request blocked for security reasons" },
        { status: 403 }
      );
    }

    // 2. Origin validation
    const originCheck = validateOrigin(headers);
    if (!originCheck.valid) {
      securityMonitor.logEvent({
        type: "invalid_origin",
        ip,
        userAgent: headers.get("user-agent") || undefined,
        details: originCheck.reason || "Invalid origin",
      });
      return NextResponse.json(
        { error: "Request blocked for security reasons" },
        { status: 403 }
      );
    }

    // 3. IP address check
    const ipCheck = checkSuspiciousIP(ip);
    if (ipCheck.suspicious) {
      console.warn(`Suspicious IP: ${ipCheck.reason} - ${ip}`);
      return NextResponse.json(
        { error: "Request blocked for security reasons" },
        { status: 403 }
      );
    }

    const { phoneNumber: rawPhoneNumber, useWhatsApp = false } =
      await request.json();

    if (!rawPhoneNumber) {
      return NextResponse.json(
        { error: "Phone number is required" },
        { status: 400 }
      );
    }

    // Format and validate phone number
    const phoneNumber = formatPhoneNumber(rawPhoneNumber);

    if (!isValidPhoneNumber(phoneNumber)) {
      return NextResponse.json(
        {
          error:
            "Invalid phone number format. Please include country code (e.g., +1234567890)",
        },
        { status: 400 }
      );
    }

    // Advanced phone number validation
    const phoneValidation = validatePhoneNumber(phoneNumber);
    if (!phoneValidation.valid) {
      // Track verification attempt for rate limiting
      const attemptData = await trackVerificationAttempt(phoneNumber);
      
      if (attemptData.count > 5) {
        return NextResponse.json(
          { 
            error: 'Too many verification attempts. Please try again later.',
            resetTime: attemptData.resetTime
          },
          { status: 429 }
        );
      }
      securityMonitor.logEvent({
        type: "suspicious_phone",
        phoneNumber,
        ip,
        userAgent: headers.get("user-agent") || undefined,
        details: phoneValidation.reason || "Suspicious phone number pattern",
      });
      return NextResponse.json(
        { error: "Invalid phone number" },
        { status: 400 }
      );
    }

    // Generate a secure 6-digit code
    const verificationCode = generateSecureCode(6);
    
    // Store the verification code with expiration (10 minutes)
    const stored = await storeVerificationCode(phoneNumber, verificationCode);
    
    if (!stored) {
      return NextResponse.json(
        { error: 'Failed to store verification code. Please try again.' },
        { status: 500 }
      );
    }

    // Debug log the request
    console.log("Sending WhatsApp verification to:", phoneNumber);

    // Initialize Twilio client for WhatsApp
    const twilio = initializeTwilio(true);
    const whatsappNumber = process.env.TWILIO_WHATSAPP_NUMBER;

    if (!whatsappNumber) {
      throw new Error("WhatsApp number is not configured");
    }

    // Prepare WhatsApp message parameters using template
    const messageParams = {
      contentSid: "HX608a58098215ff865df986c366285e5a",
      contentVariables: JSON.stringify({
        1: verificationCode,
      }),
      from: whatsappNumber,
      to: `whatsapp:${phoneNumber}`,
    };

    console.log("Using WhatsApp number:", whatsappNumber);

    console.log("Message parameters:", messageParams);

    // Send message using Twilio
    try {
      const message = await twilio.messages.create(messageParams);

      if (!message.sid) {
        console.error("Twilio SMS error: No message SID returned");
        return NextResponse.json(
          {
            error: "Failed to send verification code",
            details: "No message SID returned from Twilio",
          },
          { status: 500 }
        );
      }

      // Cleanup of old verification codes is now handled automatically by the KV store TTL
      // Log successful message send (without the code)
      const logMessage = `WhatsApp message sent successfully to ${phoneNumber.slice(
        0,
        5
      )}*****`;

      console.log(`${logMessage}, SID: ${message.sid}`);

      return NextResponse.json({
        success: true,
        message: "WhatsApp verification code sent successfully",
        messageSid: message.sid,
        channel: "whatsapp",
        // Remove this in production - only for testing
        verificationCode:
          process.env.NODE_ENV === "development" ? verificationCode : undefined,
      });
    } catch (error: any) {
      console.error("Error sending verification code:", error);

      // Handle specific Twilio errors
      if (error.code) {
        switch (error.code) {
          case 21211:
            return NextResponse.json(
              { error: "Invalid phone number" },
              { status: 400 }
            );
          case 21408:
            return NextResponse.json(
              {
                error:
                  "Permission to send message has not been enabled for the region",
              },
              { status: 400 }
            );
          case 21614:
            return NextResponse.json(
              { error: "Phone number is not a valid mobile number" },
              { status: 400 }
            );
          default:
            return NextResponse.json(
              { error: `Twilio error: ${error.message}` },
              { status: 500 }
            );
        }
      }

      return NextResponse.json(
        {
          error: "Failed to send verification code",
          details: error.message || "Unknown error occurred",
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("SMS sending error:", error);

    // Handle specific Twilio errors
    if (error.code) {
      switch (error.code) {
        case 21211:
          return NextResponse.json(
            { error: "Invalid phone number" },
            { status: 400 }
          );
        case 21408:
          return NextResponse.json(
            {
              error:
                "Permission to send SMS has not been enabled for the region",
            },
            { status: 400 }
          );
        case 21614:
          return NextResponse.json(
            { error: "Phone number is not a valid mobile number" },
            { status: 400 }
          );
        default:
          return NextResponse.json(
            { error: `Twilio error: ${error.message}` },
            { status: 500 }
          );
      }
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
