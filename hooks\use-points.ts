import { useState, useCallback } from 'react';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';
import { <PERSON><PERSON><PERSON><PERSON>per } from '@/lib/cookie-helper';

interface UsePointsReturn {
  points: string | null;
  isLoading: boolean;
  error: string | null;
  fetchPoints: () => Promise<string | null>;
  updatePointsInCookies: (newPoints: string) => void;
  clearPointsFromCookies: () => void;
}

export function usePointsApi(): UsePointsReturn {
  const [points, setPoints] = useState<string | null>(() => {
    // Initialize from cookies
    return CookieHelper.getUserPoints();
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPoints = useCallback(async (): Promise<string | null> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log("🔄 usePointsApi: Fetching points from API");

      const headers = {
        Accept: "application/json",
        "Content-Type": "application/json",
      };

      const param = {
        requestParameters: {
          recordValueJson: "[]"
        }
      };

      const response = await MakeApiCallAsync(
        Config.END_POINT_NAMES.GET_POINT,
        null,
        param,
        headers,
        "POST",
        true
      );

      console.log("📊 usePointsApi: API response:", response);

      if (response?.data?.statusCode === 200 && response.data.data !== undefined) {
        const pointValue = response.data.data.toString();
        setPoints(pointValue);
        CookieHelper.saveUserPoints(pointValue);
        console.log("✅ usePointsApi: Points fetched and saved:", pointValue);
        return pointValue;
      } else {
        const errorMsg = response?.data?.errorMessage || "Failed to fetch points";
        throw new Error(errorMsg);
      }
    } catch (error: any) {
      console.error("❌ usePointsApi: Error fetching points:", error);
      setError(error.message || "Failed to fetch points");
      
      // Keep existing points from cookies if API fails
      const savedPoints = CookieHelper.getUserPoints();
      if (savedPoints) {
        setPoints(savedPoints);
        console.log("🔄 usePointsApi: Using cached points due to API error:", savedPoints);
        return savedPoints;
      }
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updatePointsInCookies = useCallback((newPoints: string): void => {
    try {
      setPoints(newPoints);
      CookieHelper.saveUserPoints(newPoints);
      console.log("✅ usePointsApi: Points updated in cookies:", newPoints);
    } catch (error) {
      console.error("❌ usePointsApi: Error updating points in cookies:", error);
    }
  }, []);

  const clearPointsFromCookies = useCallback((): void => {
    try {
      setPoints(null);
      CookieHelper.clearUserPoints();
      console.log("✅ usePointsApi: Points cleared from cookies");
    } catch (error) {
      console.error("❌ usePointsApi: Error clearing points from cookies:", error);
    }
  }, []);

  return {
    points,
    isLoading,
    error,
    fetchPoints,
    updatePointsInCookies,
    clearPointsFromCookies,
  };
}
