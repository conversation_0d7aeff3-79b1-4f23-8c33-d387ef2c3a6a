'use client';

import { useSettings } from '@/contexts/settings-context';
import { Truck, Clock, CreditCard, RefreshCw } from 'lucide-react';

interface FacilityProps {
  icon: React.ReactNode;
  title: string;
  description?: string;
}

function Facility({ icon, title, description }: FacilityProps) {
  return (
    <div className="flex flex-col items-center text-center p-4 transition-all hover:shadow-md rounded-lg">
      <div className="mb-4 text-primary">
        {icon}
      </div>
      <h3 className="text-base font-semibold mb-1">{title}</h3>
      {description && <p className="text-sm text-muted-foreground">{description}</p>}
    </div>
  );
}

export function BestFacilities() {
  const { t } = useSettings();

  return (
    <section className="py-8 sm:py-12 bg-background">
      <div className="container mx-auto px-4">
        <h2 className="text-2xl font-bold text-center mb-8">Our Best Services</h2>

        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <Facility
            icon={<Truck className="h-10 w-10" />}
            title="Free Shipping"
            description="Free shipping on all orders over $50"
          />

          <Facility
            icon={<Clock className="h-10 w-10" />}
            title="24/7 Service"
            description="Customer support available 24 hours"
          />

          <Facility
            icon={<RefreshCw className="h-10 w-10" />}
            title="Easy Returns"
            description="30-day return policy for all products"
          />

          <Facility
            icon={<CreditCard className="h-10 w-10" />}
            title="Secure Payment"
            description="Multiple secure payment options"
          />
        </div>
      </div>
    </section>
  );
}