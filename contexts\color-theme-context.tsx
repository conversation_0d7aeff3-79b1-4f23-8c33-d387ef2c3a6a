"use client";

import React, { createContext, useContext, ReactNode } from "react";
import { useColorTheme, ColorTheme } from "@/hooks/use-color-theme";

interface ColorThemeContextType {
  currentTheme: ColorTheme;
  availableThemes: ColorTheme[];
  changeTheme: (theme: ColorTheme) => void;
  createCustomTheme: (primaryColor: string, name?: string) => ColorTheme;
  isLoading: boolean;
}

const ColorThemeContext = createContext<ColorThemeContextType | undefined>(
  undefined
);

interface ColorThemeProviderProps {
  children: ReactNode;
}

export function ColorThemeProvider({ children }: ColorThemeProviderProps) {
  const colorTheme = useColorTheme();

  return (
    <ColorThemeContext.Provider value={colorTheme}>
      {children}
    </ColorThemeContext.Provider>
  );
}

export function useColorThemeContext() {
  const context = useContext(ColorThemeContext);
  if (context === undefined) {
    throw new Error(
      "useColorThemeContext must be used within a ColorThemeProvider"
    );
  }
  return context;
}
