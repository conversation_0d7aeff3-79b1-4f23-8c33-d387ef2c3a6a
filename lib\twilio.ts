import twilio from 'twilio';

// Initialize Twilio client
let twilioClient: twilio.Twilio | null = null;

const initializeTwilio = (useWhatsApp: boolean = false) => {
  if (!twilioClient) {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const whatsappNumber = process.env.TWILIO_WHATSAPP_NUMBER;

    if (!accountSid || !authToken) {
      throw new Error('Twilio credentials are missing. Please set TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN environment variables.');
    }

    if (useWhatsApp && !whatsappNumber) {
      throw new Error('Twilio WhatsApp number is missing. Please set TWILIO_WHATSAPP_NUMBER environment variable.');
    }

    twilioClient = twilio(accountSid, authToken);
  }

  return twilioClient;
};

export { initializeTwilio };