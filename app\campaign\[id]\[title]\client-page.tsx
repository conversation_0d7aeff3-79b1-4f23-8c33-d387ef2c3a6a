"use client";

import { parseBodyLinks } from '@/lib/parse-body-links';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useEffect, useState } from 'react';

interface CampaignDetail {
  CampaignId: string;
  MainTitle: string;
  DiscountTitle: string;
  CoverPictureUrl: string;
  Body: string;
}

export default function CampaignDetailClient({ 
  initialCampaign, 
  adminPanelBaseURL 
}: { 
  initialCampaign: CampaignDetail | null; 
  adminPanelBaseURL: string;
}) {
  const [campaign, setCampaign] = useState<CampaignDetail | null>(initialCampaign);
  
  // Handle clicks on links within the campaign body
  const handleBodyClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const target = e.target as HTMLElement;
    if (target.tagName === 'A') {
      e.preventDefault();
      const link = target as HTMLAnchorElement;
      const href = link.getAttribute('href');
      if (href && (href.startsWith('http') || href.startsWith('https'))) {
        window.open(href, '_blank', 'noopener,noreferrer');
      }
    }
  };

  if (!campaign) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Campaign Not Found</h1>
          <p className="mb-6">The campaign you are looking for does not exist or has been removed.</p>
          <Link href="/" className="inline-block">
            <Button>Return to Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Campaign Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">{campaign.MainTitle}</h1>
          {campaign.DiscountTitle && (
            <p className="text-xl text-gray-600 mb-4">{campaign.DiscountTitle}</p>
          )}
        </div>

        {/* Campaign Cover Image */}
        {campaign.CoverPictureUrl && (
          <div className="relative aspect-video mb-8 rounded-lg overflow-hidden">
            <Image
              src={adminPanelBaseURL + campaign.CoverPictureUrl}
              alt={campaign.MainTitle}
              fill
              className="object-cover"
              priority
              onError={(e) => {
                // If image fails to load, replace with a placeholder
                const target = e.target as HTMLImageElement;
                target.src = 'https://placehold.co/1200x630/cccccc/666666?text=Campaign+Image';
              }}
            />
          </div>
        )}

        {/* Campaign Body Content */}
        {campaign.Body && (
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: parseBodyLinks(campaign.Body) }}
            onClick={handleBodyClick}
          />
        )}

        {/* Back to Home Button */}
        <div className="mt-12">
          <Link href="/" className="inline-block">
            <Button variant="outline">Back to Home</Button>
          </Link>
        </div>
      </div>
    </div>
  );
}