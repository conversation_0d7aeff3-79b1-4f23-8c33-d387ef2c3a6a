# Coupon Implementation Guide

## Overview
This document describes the coupon functionality implementation for the e-commerce application. The system allows users to apply discount coupons to their cart and receive appropriate discounts.

## Backend Implementation

### Database Structure
The coupon system uses the `Discounts` table with the following key fields:
- `DiscountId`: Primary key
- `Title`: Discount title
- `CouponCode`: The coupon code string
- `DiscountValue`: The discount amount/percentage
- `DiscountValueType`: 1 = Fixed Amount, 2 = Percentage
- `DiscountTypeId`: Type of discount (product, category, order total)
- `StartDate` & `EndDate`: Validity period
- `IsActive`: Whether the coupon is active
- `IsCouponCodeRequired`: Whether a code is required

### API Endpoint
**Endpoint**: `POST /api/v1/common/get-coupon-code-discount-value/calculate-coupon-discount`

**Request Format**:
```json
{
  "requestParameters": {
    "CouponCode": "TEST123",
    "cartJsonData": "[{\"ProductId\":1,\"ProductName\":\"Test Product\",\"Price\":100,\"Quantity\":1,\"IsDiscountAllowed\":true}]"
  }
}
```

**Response Format**:
```json
{
  "data": "{\"DiscountValueAfterCouponAppliedWithQuantity\":10.00,\"DiscountId\":1,\"CouponCode\":\"TEST123\"}"
}
```

### Backend Controller
The `ApiCommonController.GetCouponCodeDiscountedValue` method handles coupon validation:
1. Parses cart data from JSON
2. Validates coupon code against database
3. Calculates discount based on coupon type and rules
4. Returns discount amount and details

## Frontend Implementation

### Coupon Context (`contexts/coupon-context.tsx`)
Provides centralized coupon state management:
- `appliedCoupon`: Currently applied coupon
- `validateCoupon()`: Validates coupon via API
- `clearCoupon()`: Removes applied coupon
- `isLoading`: Loading state for API calls

### Cart Integration (`app/cart/page.tsx`)
The cart page includes:
- Coupon input field
- Apply/Remove coupon buttons
- Discount display in order summary
- Real-time total calculation with discount

### API Helper (`lib/api-helper.ts`)
Added coupon endpoint configuration:
```typescript
GET_COUPON_CODE_DISCOUNT: 'get-coupon-code-discount-value/calculate-coupon-discount'
```

## Usage Instructions

### For Users
1. Add items to cart
2. Navigate to cart page
3. Enter coupon code in the input field
4. Click "Apply" button
5. Discount will be applied if valid
6. Final total will reflect the discount

### For Developers
1. Create coupons in the admin panel
2. Set appropriate discount values and types
3. Configure validity dates
4. Test using the test page at `/test-coupon`

## Testing

### Test Coupon Page
Access `/test-coupon` to test the coupon API directly:
- Default test code: "test123"
- Shows raw API response
- Useful for debugging

### Sample Test Data
From the Postman collection, you can test with:
- Coupon Code: "test123"
- Sample cart data with ProductId: 44

## Configuration

### Environment Setup
Make sure the following are configured:
- Database connection string in `appsettings.json`
- API base URL in frontend config
- CORS settings for API calls

### Database Requirements
Ensure the following tables exist:
- `Discounts` (main coupon table)
- `DiscountTypes` (discount type lookup)
- `DiscountUsageHistory` (usage tracking)

## Error Handling

The system handles various error scenarios:
- Invalid coupon codes
- Expired coupons
- Network errors
- API failures

Error messages are displayed to users via SweetAlert notifications.

## Security Considerations

- Coupon codes are validated server-side
- Usage limits can be enforced
- Expiration dates are checked
- Cart data is validated before discount calculation

## Future Enhancements

Potential improvements:
- Usage limit tracking per user
- Minimum order amount requirements
- Category-specific coupons
- Bulk coupon generation
- Analytics and reporting
