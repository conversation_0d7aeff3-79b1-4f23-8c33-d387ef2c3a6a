'use client';

import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';

interface LoadingScreenProps {
  fullScreen?: boolean;
  message?: string;
  className?: string;
}

export function LoadingScreen({ 
  fullScreen = true, 
  message, 
  className = '' 
}: LoadingScreenProps) {
  const {  } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  
  const containerClasses = fullScreen 
    ? 'fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm' 
    : 'flex items-center justify-center py-8';
  
  return (
    <div className={`${containerClasses} ${className}`}>
      <div className="flex flex-col items-center">
        <div className="relative">
          <div 
            className="h-16 w-16 rounded-full border-4 border-muted animate-spin"
            style={{ borderTopColor: primaryColor }}
          />
        </div>
        
        {message && (
          <p className="mt-4 text-center text-muted-foreground">{message}</p>
        )}
      </div>
    </div>
  );
}