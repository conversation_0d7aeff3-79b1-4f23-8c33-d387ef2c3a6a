import { NextRequest, NextResponse } from 'next/server';
import { Config } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    console.log('🔍 Check User Review API: Received request body:', body);

    // Extract JWT token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : authHeader;

    console.log('🔍 Check User Review API: JWT token present:', !!token);

    // Prepare headers for the remote API call
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add JWT token to headers if available
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
      // Also add to Token header for backward compatibility
      headers['Token'] = token;
      console.log('🔐 Check User Review API: Added JWT token to headers');
    } else {
      console.error('❌ Check User Review API: No JWT token found in request');
      return NextResponse.json(
        { 
          error: 'Authentication required',
          message: 'Please log in to check reviews.',
          errorMessage: 'Authentication required'
        },
        { status: 401 }
      );
    }

    console.log('🔍 Check User Review API: Request body:', body);

    // Forward the request to the remote API
    const response = await fetch(
      `${Config.ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-product-review-by-user`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      }
    );

    console.log('🔍 Check User Review API: Backend response status:', response.status);

    const data = await response.json();
    console.log('🔍 Check User Review API: Backend response data:', data);

    if (!response.ok) {
      console.error('❌ Check User Review API: External API error:', data);
      return NextResponse.json(
        { 
          error: 'Failed to check user review', 
          details: data,
          errorMessage: data.message || 'Failed to check user review'
        },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('❌ Check User Review API: Route error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'An error occurred while checking user review.',
        errorMessage: 'Internal server error'
      },
      { status: 500 }
    );
  }
}
