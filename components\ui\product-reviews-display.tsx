'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Star, User, ThumbsUp, MessageCircle } from 'lucide-react';
import { useColorThemeContext } from '@/contexts/color-theme-context';
// Removed MakeApiCallAsync import - now using direct API route

interface Review {
  ReviewID: number;
  ProductID: number;
  Title: string;
  Body: string;
  Rating: number;
  ReviewerName: string;
  ReviewerEmail: string;
  UserID: number;
  CreatedOn: string;
  ReviewDate: string; // Added ReviewDate field
  IsApproved: boolean;
  HelpfulCount?: number;
}

interface ProductReviewsDisplayProps {
  productId: number;
  showTitle?: boolean;
}

export default function ProductReviewsDisplay({ productId, showTitle = true }: ProductReviewsDisplayProps) {
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    if (productId) {
      fetchProductReviews();
    }
  }, [productId]);

  const fetchProductReviews = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔍 Fetching product reviews for ProductId:', productId);

      const response = await fetch('/api/reviews/get-product-reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          requestParameters: {
            ProductId: productId,
            recordValueJson: "[]"
          }
        })
      });

      const responseData = await response.json();
      console.log('🔍 Product reviews API response:', responseData);

      if (response.ok && responseData && !responseData.errorMessage) {
        let reviewsData = responseData.data || responseData;

        console.log('🔍 Raw reviews data:', reviewsData);

        // Handle nested response structure
        if (typeof reviewsData === 'string') {
          try {
            reviewsData = JSON.parse(reviewsData);
            console.log('🔍 Parsed reviews data from string:', reviewsData);
          } catch (e) {
            console.error('❌ Failed to parse reviews data string:', e);
            reviewsData = [];
          }
        }

        // Handle double-nested structure if needed
        if (Array.isArray(reviewsData) && reviewsData.length > 0 && reviewsData[0].DATA) {
          try {
            const innerData = JSON.parse(reviewsData[0].DATA);
            console.log('🔍 Parsed inner DATA:', innerData);
            if (Array.isArray(innerData)) {
              setReviews(innerData.filter((review: Review) => review.IsApproved !== false));
            } else {
              setReviews([]);
            }
          } catch (e) {
            console.error('❌ Failed to parse inner DATA:', e);
            setReviews([]);
          }
        } else if (Array.isArray(reviewsData)) {
          console.log('🔍 Using reviews data as array:', reviewsData);

          // Log date fields for debugging
          if (reviewsData.length > 0) {
            console.log('🔍 Sample review date fields:', {
              ReviewDate: reviewsData[0].ReviewDate,
              CreatedOn: reviewsData[0].CreatedOn,
              availableFields: Object.keys(reviewsData[0])
            });
          }

          setReviews(reviewsData.filter((review: Review) => review.IsApproved !== false));
        } else {
          console.log('ℹ️ No valid reviews data found');
          setReviews([]);
        }
      } else {
        console.error('❌ API error:', responseData);
        setError(responseData?.errorMessage || responseData?.message || 'Failed to load reviews');
      }
    } catch (error) {
      console.error('❌ Error fetching product reviews:', error);
      setError('Failed to load reviews');
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating
                ? 'fill-yellow-400 text-yellow-400'
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  // Helper function to get the review date (prefer ReviewDate over CreatedOn)
  const getReviewDate = (review: Review) => {
    return review.ReviewDate || review.CreatedOn || '';
  };

  const displayedReviews = showAll ? reviews : reviews.slice(0, 3);
  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.Rating, 0) / reviews.length 
    : 0;

  if (loading) {
    return (
      <div className="space-y-4">
        {showTitle && <Skeleton className="h-6 w-48" />}
        {[1, 2, 3].map((i) => (
          <Card key={i} className="p-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="p-6 text-center">
        <MessageCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-500">{error}</p>
      </Card>
    );
  }

  if (reviews.length === 0) {
    return (
      <Card className="p-6 text-center">
        <MessageCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <h3 className="font-medium mb-1">No Reviews Yet</h3>
        <p className="text-gray-500 text-sm">Be the first to review this product!</p>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {showTitle && (
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Customer Reviews</h3>
          <div className="flex items-center gap-2">
            {renderStars(Math.round(averageRating))}
            <span className="text-sm text-gray-600">
              {averageRating.toFixed(1)} ({reviews.length} review{reviews.length !== 1 ? 's' : ''})
            </span>
          </div>
        </div>
      )}

      <div className="space-y-4">
        {displayedReviews.map((review) => (
          <Card key={review.ReviewID} className="p-4">
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-8 h-8 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: `${primaryColor}1A` }}
                  >
                    <User className="h-4 w-4" style={{ color: primaryColor }} />
                  </div>
                  <div>
                    <p className="font-medium text-sm">{review.ReviewerName}</p>
                    <div className="flex items-center gap-2">
                      {renderStars(review.Rating)}
                      <span className="text-xs text-gray-500">
                        {formatDate(getReviewDate(review))}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-1">{review.Title}</h4>
                <p className="text-gray-700 text-sm leading-relaxed">{review.Body}</p>
              </div>

              {review.HelpfulCount && review.HelpfulCount > 0 && (
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <ThumbsUp className="h-3 w-3" />
                  <span>{review.HelpfulCount} people found this helpful</span>
                </div>
              )}
            </div>
          </Card>
        ))}
      </div>

      {reviews.length > 3 && (
        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => setShowAll(!showAll)}
          >
            {showAll ? 'Show Less' : `Show All ${reviews.length} Reviews`}
          </Button>
        </div>
      )}
    </div>
  );
}