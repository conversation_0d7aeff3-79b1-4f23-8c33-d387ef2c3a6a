'use client';

import { ViewportInfo } from '@/components/test/viewport-info';
import PopularCategories from '@/components/ui/popular-categories';
import Link from 'next/link';

export default function TestResponsivePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <ViewportInfo />
      
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8 text-center">Responsive Design Test</h1>
        
        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Test Instructions</h2>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <p className="mb-4">
              This page is for testing the responsive design fixes for tablet landscape view (768px-1329px).
            </p>
            <div className="space-y-2 text-sm">
              <div><strong>Target Range:</strong> 768px - 1329px viewport width</div>
              <div><strong>Expected Behavior:</strong></div>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Popular Categories: 4×2 grid layout with autoplay</li>
                <li>Account Page: Optimized sidebar and content layout</li>
                <li>Proper spacing and element positioning</li>
              </ul>
            </div>
            <div className="mt-4 space-x-4">
              <Link 
                href="/account" 
                className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                Test Account Page
              </Link>
              <Link 
                href="/" 
                className="inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                Test Home Page (Categories)
              </Link>
            </div>
          </div>
        </div>

        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Popular Categories Test</h2>
          <div className="bg-white rounded-lg shadow-sm">
            <PopularCategories />
          </div>
        </div>

        <div className="mb-12">
          <h2 className="text-2xl font-semibold mb-4">Account Page Layout Preview</h2>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-[200px_1fr] lg:grid-cols-[250px_1fr] gap-4 md:gap-6 xl:gap-8 account-grid">
              {/* Sidebar Preview */}
              <div className="space-y-4 account-sidebar">
                <div className="border rounded-lg p-4 card-content">
                  <div className="flex flex-col items-center text-center mb-4 account-user-info">
                    <div className="w-16 h-16 md:w-20 md:h-20 rounded-full bg-blue-100 flex items-center justify-center mb-3 account-user-avatar">
                      <span className="text-blue-600">👤</span>
                    </div>
                    <h3 className="font-medium text-sm md:text-base">John Doe</h3>
                    <p className="text-xs md:text-sm text-gray-600"><EMAIL></p>
                  </div>
                  <div className="space-y-1">
                    <div className="w-full p-2 text-left text-xs md:text-sm account-nav-button border rounded">
                      Profile
                    </div>
                    <div className="w-full p-2 text-left text-xs md:text-sm account-nav-button border rounded">
                      Orders
                    </div>
                    <div className="w-full p-2 text-left text-xs md:text-sm account-nav-button border rounded">
                      Addresses
                    </div>
                  </div>
                </div>
              </div>

              {/* Main Content Preview */}
              <div className="w-full min-w-0 account-main-content">
                <div className="border rounded-lg p-6">
                  <div className="grid w-full grid-cols-1 md:grid-cols-3 mb-4 gap-2 account-tabs-list">
                    <div className="p-3 text-center border rounded account-tab-trigger bg-blue-50">
                      Personal Information
                    </div>
                    <div className="p-3 text-center border rounded account-tab-trigger">
                      Security
                    </div>
                    <div className="p-3 text-center border rounded account-tab-trigger">
                      Account Details
                    </div>
                  </div>
                  <div className="account-tab-content">
                    <h3 className="text-xl font-semibold mb-6">Account Details</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 account-overview-grid">
                      <div className="border rounded-lg p-4 account-overview-card">
                        <h4 className="font-semibold mb-3">Account Information</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between py-2 border-b account-info-row">
                            <span className="text-sm text-gray-600 account-info-label">Phone</span>
                            <span className="font-medium account-info-value">+**********</span>
                          </div>
                          <div className="flex justify-between py-2 border-b account-info-row">
                            <span className="text-sm text-gray-600 account-info-label">Gender</span>
                            <span className="font-medium account-info-value">Male</span>
                          </div>
                        </div>
                      </div>
                      <div className="border rounded-lg p-4 account-overview-card">
                        <h4 className="font-semibold mb-3">Preferences</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between py-2 border-b account-info-row">
                            <span className="text-sm text-gray-600 account-info-label">Language</span>
                            <span className="font-medium account-info-value">English</span>
                          </div>
                          <div className="flex justify-between py-2 border-b account-info-row">
                            <span className="text-sm text-gray-600 account-info-label">Theme</span>
                            <span className="font-medium account-info-value">Light</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center text-sm text-gray-600">
          <p>Resize your browser window to test different viewport sizes.</p>
          <p>The viewport info in the top-right corner shows current dimensions and detected device type.</p>
        </div>
      </div>
    </div>
  );
}
