import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
}

/**
 * Telegram brand icon rendered from official SVG path.
 */
const TelegramIcon: React.FC<IconProps> = ({ className, ...props }) => (
  <svg
    viewBox="0 0 455 455"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    {...props}
  >
    <path d="M0 0v455h455V0H0zm384.814 100.68l-53.458 257.136a8.001 8.001 0 0 1-13.401 5.172l-72.975-52.981a11.996 11.996 0 0 0-11.942-.744l-40.46 32.981c-4.695 3.84-11.771 1.7-13.569-4.083l-28.094-90.351-72.583-27.089c-7.373-2.762-7.436-13.171-.084-16.003L373.36 90.959c6.315-2.442 12.83 3.09 11.454 9.721z" />
    <path d="M313.567 147.179l-141.854 87.367c-5.437 3.355-7.996 9.921-6.242 16.068l15.337 53.891a4.002 4.002 0 0 0 7.162-.517l3.986-29.553a20.016 20.016 0 0 1 7.522-14.522l117.069-108.822c2.729-2.53.105-5.977-2.653-4.912z" fill="#ffffff" />
  </svg>
);

export default TelegramIcon;
