'use client'

import { useState, useRef, useEffect, useCallback, KeyboardEvent } from 'react'
import { 
  Play, Pause, Settings, Wifi, WifiOff, Download, Volume2, VolumeX, 
  Maximize, Minimize, SkipBack, SkipForward, FastForward, Rewind
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { ConnectionSpeedDetector, useConnectionSpeed, getOptimalVideoSettings } from '@/lib/connection-speed'

interface VideoQuality {
  label: string
  value: string
  bandwidth: number // in kbps
  resolution?: string
}

interface AdaptiveVideoPlayerProps {
  src: string
  className?: string
  onPlay?: () => void
  onPause?: () => void
  onEnded?: () => void
  autoPlay?: boolean
  poster?: string
  initialVolume?: number
  initialMuted?: boolean
  showControls?: boolean
  disableFullscreen?: boolean
  disableKeyboardControls?: boolean
}

const VIDEO_QUALITIES: VideoQuality[] = [
  { label: 'Auto', value: 'auto', bandwidth: 0 },
  { label: '240p (Low)', value: '240p', bandwidth: 400, resolution: '426x240' },
  { label: '360p (Medium)', value: '360p', bandwidth: 800, resolution: '640x360' },
  { label: '480p (High)', value: '480p', bandwidth: 1200, resolution: '854x480' },
  { label: '720p (HD)', value: '720p', bandwidth: 2500, resolution: '1280x720' },
]

// Define playback speed options
const PLAYBACK_SPEEDS = [
  { label: '0.5x', value: 0.5 },
  { label: '0.75x', value: 0.75 },
  { label: '1x', value: 1 },
  { label: '1.25x', value: 1.25 },
  { label: '1.5x', value: 1.5 },
  { label: '2x', value: 2 },
]

export function AdaptiveVideoPlayer({
  src,
  className,
  onPlay,
  onPause,
  onEnded,
  autoPlay = false,
  poster,
  initialVolume = 1,
  initialMuted = false,
  showControls = true,
  disableFullscreen = false,
  disableKeyboardControls = false,
}: AdaptiveVideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(initialVolume)
  const [isMuted, setIsMuted] = useState(initialMuted)
  const [selectedQuality, setSelectedQuality] = useState<VideoQuality>(VIDEO_QUALITIES[0])
  const [connectionSpeed, setConnectionSpeed] = useState<number | null>(null)
  const [isBuffering, setIsBuffering] = useState(false)
  const [controlsVisible, setControlsVisible] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [connectionQuality, setConnectionQuality] = useState<'slow' | 'medium' | 'fast' | 'very-fast'>('medium')
  const [autoQualityEnabled, setAutoQualityEnabled] = useState(true)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [playbackSpeed, setPlaybackSpeed] = useState(PLAYBACK_SPEEDS[2]) // Default to 1x
  const [isVolumeSliderVisible, setIsVolumeSliderVisible] = useState(false)
  
  const videoRef = useRef<HTMLVideoElement>(null)
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const playerContainerRef = useRef<HTMLDivElement>(null)
  const volumeSliderRef = useRef<HTMLDivElement>(null)
  const { testSpeed, getCachedInfo } = useConnectionSpeed()

  // Detect connection speed using the new utility
  const detectConnectionSpeed = useCallback(async () => {
    try {
      // Check for cached info first
      const cachedInfo = getCachedInfo()
      if (cachedInfo) {
        setConnectionSpeed(cachedInfo.speed)
        setConnectionQuality(cachedInfo.quality)
        
        if (autoQualityEnabled) {
          const recommendedQuality = VIDEO_QUALITIES.find(q => q.label === cachedInfo.recommendedVideoQuality) || VIDEO_QUALITIES[0]
          setSelectedQuality(recommendedQuality)
        }
        return
      }
      
      // Perform new speed test
      const connectionInfo = await testSpeed()
      setConnectionSpeed(connectionInfo.speed)
      setConnectionQuality(connectionInfo.quality)
      
      // Auto-select quality based on connection info
      if (autoQualityEnabled) {
        const recommendedQuality = VIDEO_QUALITIES.find(q => q.label === connectionInfo.recommendedVideoQuality) || VIDEO_QUALITIES[0]
        setSelectedQuality(recommendedQuality)
      }
      
    } catch (error) {
      console.error('Connection speed detection failed:', error)
      setConnectionSpeed(null)
      setConnectionQuality('medium')
      // Default to medium quality on error
      if (autoQualityEnabled) {
        setSelectedQuality(VIDEO_QUALITIES.find(q => q.label === '360p') || VIDEO_QUALITIES[1])
      }
    }
  }, [testSpeed, getCachedInfo, autoQualityEnabled])

  // Fallback URL state
  const [hasFallbackAttempted, setHasFallbackAttempted] = useState(false)
  const [fallbackUrl, setFallbackUrl] = useState<string | null>(null)
  
  // Try fallback URL (direct access without proxy)
  const tryFallbackUrl = useCallback(() => {
    if (hasFallbackAttempted || fallbackUrl) return
    
    // Extract original URL from proxy URL or use src directly
    let originalUrl = src
    if (src.includes('/api/video-proxy')) {
      try {
        const url = new URL(src, window.location.origin)
        const urlParam = url.searchParams.get('url')
        if (urlParam) {
          originalUrl = decodeURIComponent(urlParam)
        }
      } catch (e) {
        console.error('Error extracting original URL from proxy:', e)
      }
    }
    
    console.log('Attempting fallback to direct URL:', originalUrl)
    setFallbackUrl(originalUrl)
    setHasFallbackAttempted(true)
    setError(null)
    
    // Update video source to fallback URL
    if (videoRef.current && originalUrl !== src) {
      const currentTime = videoRef.current.currentTime
      const wasPlaying = isPlaying
      
      videoRef.current.src = originalUrl
      videoRef.current.currentTime = currentTime
      
      if (wasPlaying) {
        videoRef.current.play().catch(console.error)
      }
    }
  }, [src, hasFallbackAttempted, fallbackUrl, isPlaying])
  
  // Generate quality-specific video URL
  const getVideoUrl = useCallback((quality: VideoQuality) => {
    // Use fallback URL if available and proxy failed
    const sourceUrl = fallbackUrl || src
    
    // If using fallback URL, return it directly (no proxy)
    if (fallbackUrl) {
      console.log('Using fallback URL (direct access):', fallbackUrl)
      return fallbackUrl
    }
    
    // Check if the source is already a proxy URL
    if (sourceUrl.includes('/api/video-proxy')) {
      // Source is already a proxy URL, just add quality parameters if needed
      if (quality.value === 'auto' || !quality.resolution) {
        return sourceUrl
      }
      
      try {
        const url = new URL(sourceUrl, window.location.origin)
        url.searchParams.set('quality', quality.value)
        url.searchParams.set('bandwidth', quality.bandwidth.toString())
        if (connectionQuality === 'slow') {
          url.searchParams.set('optimize', 'true')
        }
        // Add timestamp to prevent caching issues on Amplify
        url.searchParams.set('t', Date.now().toString())
        return url.toString()
      } catch (e) {
        console.error('Error parsing video URL:', e)
        return sourceUrl
      }
    } else {
      // Create a new proxy URL
      const proxyUrl = `/api/video-proxy/?url=${encodeURIComponent(sourceUrl)}`
      
      if (quality.value === 'auto' || !quality.resolution) {
        const url = new URL(proxyUrl, window.location.origin)
        if (connectionQuality === 'slow') {
          url.searchParams.set('optimize', 'true')
        }
        // Add timestamp to prevent caching issues on Amplify
        url.searchParams.set('t', Date.now().toString())
        return url.toString()
      }

      // For specific quality, add quality parameters
      const url = new URL(proxyUrl, window.location.origin)
      url.searchParams.set('quality', quality.value)
      url.searchParams.set('bandwidth', quality.bandwidth.toString())
      if (connectionQuality === 'slow') {
        url.searchParams.set('optimize', 'true')
      }
      // Add timestamp to prevent caching issues on Amplify
      url.searchParams.set('t', Date.now().toString())
      
      return url.toString()
    }
  }, [src, connectionQuality, fallbackUrl])

  // Handle video events
  const handleLoadStart = () => {
    setIsLoading(true)
    setError(null)
  }

  const handleLoadedData = () => {
    setIsLoading(false)
    if (videoRef.current) {
      setDuration(videoRef.current.duration)
    }
  }

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime)
    }
  }

  const handlePlay = () => {
    setIsPlaying(true)
    onPlay?.()
  }

  const handlePause = () => {
    setIsPlaying(false)
    onPause?.()
  }

  const handleEnded = () => {
    setIsPlaying(false)
    onEnded?.()
  }

  const handleError = useCallback((e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    const video = e.currentTarget
    const videoError = video.error
    
    if (videoError) {
      switch (videoError.code) {
        case MediaError.MEDIA_ERR_NETWORK:
          setError('Network error occurred while loading video')
          // Try fallback: direct URL without proxy
          tryFallbackUrl()
          break
        case MediaError.MEDIA_ERR_DECODE:
          setError('Video format not supported')
          break
        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
          setError('Video source not supported')
          // Try fallback: direct URL without proxy
          tryFallbackUrl()
          break
        default:
          setError('An error occurred while loading the video')
          tryFallbackUrl()
      }
    } else {
      setError('An unknown error occurred')
      tryFallbackUrl()
    }
    
    setIsLoading(false)
  }, [])

  const handleWaiting = () => {
    setIsBuffering(true)
  }

  const handleCanPlay = () => {
    setIsBuffering(false)
  }

  // Toggle play/pause
  const togglePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play().catch(console.error)
      }
    }
  }

  // Volume control handlers
  const handleVolumeChange = useCallback((value: number[]) => {
    if (!videoRef.current) return
    const newVolume = value[0]
    setVolume(newVolume)
    videoRef.current.volume = newVolume
    
    // If volume is set to 0, mute the video
    // If volume is increased from 0, unmute the video
    if (newVolume === 0 && !isMuted) {
      setIsMuted(true)
      videoRef.current.muted = true
    } else if (newVolume > 0 && isMuted) {
      setIsMuted(false)
      videoRef.current.muted = false
    }
  }, [isMuted])

  const handleToggleMute = useCallback(() => {
    if (!videoRef.current) return
    const newMutedState = !isMuted
    setIsMuted(newMutedState)
    videoRef.current.muted = newMutedState
  }, [isMuted])

  const handleVolumeSliderVisibility = useCallback((visible: boolean) => {
    setIsVolumeSliderVisible(visible)
  }, [])

  // Fullscreen handlers
  const handleToggleFullscreen = useCallback(() => {
    if (!playerContainerRef.current) return

    if (!document.fullscreenElement) {
      playerContainerRef.current.requestFullscreen().catch(err => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`)
      })
    } else {
      document.exitFullscreen()
    }
  }, [])

  // Playback speed handlers
  const handlePlaybackSpeedChange = useCallback((speed: { label: string, value: number }) => {
    if (!videoRef.current) return
    setPlaybackSpeed(speed)
    videoRef.current.playbackRate = speed.value
  }, [])

  // Skip forward/backward
  const handleSkip = useCallback((seconds: number) => {
    if (!videoRef.current) return
    const newTime = videoRef.current.currentTime + seconds
    videoRef.current.currentTime = Math.max(0, Math.min(newTime, videoRef.current.duration))
  }, [])

  // Handle quality change
  const handleQualityChange = (quality: VideoQuality) => {
    // If selecting auto, enable auto quality and detect speed
    if (quality.value === 'auto') {
      setAutoQualityEnabled(true)
      detectConnectionSpeed()
      return
    }
    
    // Manual quality selection disables auto mode
    setAutoQualityEnabled(false)
    setSelectedQuality(quality)
    setError(null)
    
    if (videoRef.current) {
      const currentTime = videoRef.current.currentTime
      const wasPlaying = isPlaying
      
      // Update video source
      videoRef.current.src = getVideoUrl(quality)
      videoRef.current.currentTime = currentTime
      
      if (wasPlaying) {
        videoRef.current.play().catch(console.error)
      }
    }
  }

  // Handle seek
  const handleSeek = (time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time
      setCurrentTime(time)
    }
  }

  // Format time
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Auto-hide controls
  const resetControlsTimeout = () => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }
    
    setControlsVisible(true)
    
    if (isPlaying) {
      controlsTimeoutRef.current = setTimeout(() => {
        setControlsVisible(false)
      }, 3000)
    }
  }

  // Initialize speed detection
  useEffect(() => {
    detectConnectionSpeed()
  }, [detectConnectionSpeed])

  // Handle mouse movement for controls
  useEffect(() => {
    resetControlsTimeout()
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
    }
  }, [isPlaying])

  // Handle fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [])

  // Handle keyboard controls
  const handleKeyDown = useCallback((e: KeyboardEvent<HTMLDivElement>) => {
    if (disableKeyboardControls) return
    
    switch (e.key) {
      case ' ':
      case 'k':
        e.preventDefault()
        togglePlayPause()
        break
      case 'ArrowRight':
        e.preventDefault()
        handleSkip(10) // Skip forward 10 seconds
        break
      case 'ArrowLeft':
        e.preventDefault()
        handleSkip(-10) // Skip backward 10 seconds
        break
      case 'f':
        e.preventDefault()
        if (!disableFullscreen) handleToggleFullscreen()
        break
      case 'm':
        e.preventDefault()
        handleToggleMute()
        break
      case 'ArrowUp':
        e.preventDefault()
        if (!videoRef.current) return
        const newVolumeUp = Math.min(1, volume + 0.1)
        handleVolumeChange([newVolumeUp])
        break
      case 'ArrowDown':
        e.preventDefault()
        if (!videoRef.current) return
        const newVolumeDown = Math.max(0, volume - 0.1)
        handleVolumeChange([newVolumeDown])
        break
      default:
        break
    }
  }, [togglePlayPause, handleSkip, handleToggleFullscreen, handleToggleMute, handleVolumeChange, volume, disableKeyboardControls, disableFullscreen])

  return (
    <div 
      ref={playerContainerRef}
      className={cn('relative w-full h-full bg-black rounded-lg overflow-hidden', className)}
      onMouseMove={resetControlsTimeout}
      onMouseLeave={() => isPlaying && setControlsVisible(false)}
      onKeyDown={handleKeyDown}
      tabIndex={0} // Make the container focusable for keyboard events
      aria-label="Video player"
    >
      <video
        ref={videoRef}
        src={getVideoUrl(selectedQuality)}
        className="w-full h-full object-contain"
        poster={poster}
        autoPlay={autoPlay}
        playsInline
        preload="metadata"
        onLoadStart={handleLoadStart}
        onLoadedData={handleLoadedData}
        onTimeUpdate={handleTimeUpdate}
        onPlay={handlePlay}
        onPause={handlePause}
        onEnded={handleEnded}
        onError={handleError}
        onWaiting={handleWaiting}
        onCanPlay={handleCanPlay}
        muted={isMuted}
      />

      {/* Loading indicator */}
      {(isLoading || isBuffering) && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/80">
          <div className="text-white text-center p-4 max-w-md">
            <WifiOff className="h-8 w-8 mx-auto mb-2" />
            <p className="text-sm mb-3">{error}</p>
            {!hasFallbackAttempted && (
              <div className="text-xs text-gray-300 mb-2">
                Attempting direct video access...
              </div>
            )}
            {hasFallbackAttempted && (
              <div className="text-xs text-gray-300 mb-2">
                <div className="mb-1">Tried both proxy and direct access.</div>
                <div>This may be due to Amplify serverless limitations or video hosting restrictions.</div>
              </div>
            )}
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-2"
              onClick={() => handleQualityChange(VIDEO_QUALITIES[1])}
            >
              Try Lower Quality
            </Button>
          </div>
        </div>
      )}

      {/* Play/Pause overlay */}
      {!isLoading && !error && (
        <button
          onClick={togglePlayPause}
          className={cn(
            'absolute inset-0 flex items-center justify-center transition-all duration-300',
            isPlaying && !controlsVisible ? 'opacity-0 hover:opacity-100' : 
            isPlaying && controlsVisible ? 'opacity-0 hover:opacity-100' : 'opacity-80 hover:opacity-100'
          )}
          aria-label={isPlaying ? 'Pause' : 'Play'}
        >
          <div className="bg-black/50 text-white rounded-full p-4 transition-all duration-300 hover:bg-black/70 hover:scale-110">
            {isPlaying ? <Pause size={32} /> : <Play size={32} />}
          </div>
        </button>
      )}

      {/* Skip backward/forward overlay buttons */}
      <div className={cn(
        "absolute inset-0 flex items-center justify-between pointer-events-none px-12 transition-opacity duration-300",
        controlsVisible ? "opacity-100" : "opacity-0 hover:opacity-100"
      )}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <button 
                className="rounded-full bg-black/30 p-2 transition-transform hover:scale-110 pointer-events-auto"
                onClick={() => handleSkip(-10)}
                aria-label="Skip backward 10 seconds"
              >
                <Rewind className="h-6 w-6 text-white" />
              </button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>Skip backward 10s (Left Arrow)</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <button 
                className="rounded-full bg-black/30 p-2 transition-transform hover:scale-110 pointer-events-auto"
                onClick={() => handleSkip(10)}
                aria-label="Skip forward 10 seconds"
              >
                <FastForward className="h-6 w-6 text-white" />
              </button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>Skip forward 10s (Right Arrow)</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Controls */}
      {(controlsVisible || !isPlaying) && !isLoading && !error && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          {/* Progress bar */}
          <div className="mb-3">
            <div className="relative h-2 bg-white/30 rounded-full cursor-pointer group"
                 onClick={(e) => {
                   const rect = e.currentTarget.getBoundingClientRect()
                   const percent = (e.clientX - rect.left) / rect.width
                   handleSeek(percent * duration)
                 }}
                 role="slider"
                 aria-label="Video progress"
                 aria-valuemin={0}
                 aria-valuemax={100}
                 aria-valuenow={duration ? Math.round((currentTime / duration) * 100) : 0}>
              <div 
                className="absolute h-full bg-white rounded-full"
                style={{ width: `${(currentTime / duration) * 100}%` }}
              />
              <div 
                className="absolute top-1/2 h-4 w-4 -translate-x-1/2 -translate-y-1/2 rounded-full bg-white opacity-0 transition-opacity group-hover:opacity-100"
                style={{ left: `${duration ? (currentTime / duration) * 100 : 0}%` }}
              />
            </div>
          </div>

          <div className="flex items-center justify-between text-white">
            <div className="flex items-center gap-3">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                       variant="ghost"
                       size="sm"
                       onClick={togglePlayPause}
                       className="text-white hover:bg-white/20"
                     >
                       {isPlaying ? <Pause size={16} /> : <Play size={16} />}
                     </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>{isPlaying ? 'Pause (k)' : 'Play (k)'}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              {/* Volume control */}
              <div 
                className="relative flex items-center"
                onMouseEnter={() => handleVolumeSliderVisibility(true)}
                onMouseLeave={() => handleVolumeSliderVisibility(false)}
              >
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleToggleMute}
                        className="text-white hover:bg-white/20"
                      >
                        {isMuted || volume === 0 ? <VolumeX size={16} /> : <Volume2 size={16} />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p>{isMuted ? 'Unmute (m)' : 'Mute (m)'}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* Volume slider */}
                {isVolumeSliderVisible && (
                  <div 
                    ref={volumeSliderRef}
                    className="absolute bottom-full left-0 mb-2 w-32 rounded-md bg-black/90 p-2"
                  >
                    <Slider
                      value={[isMuted ? 0 : volume]}
                      min={0}
                      max={1}
                      step={0.01}
                      onValueChange={handleVolumeChange}
                      aria-label="Volume"
                    />
                  </div>
                )}
              </div>
              
              <span className="text-xs">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </div>

            <div className="flex items-center gap-2">
              {/* Playback speed control */}
              <DropdownMenu>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-white hover:bg-white/20"
                        >
                          <span className="text-xs">{playbackSpeed.label}</span>
                        </Button>
                      </DropdownMenuTrigger>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p>Playback speed</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <DropdownMenuContent align="end" className="bg-black/90 text-white border-white/20">
                  <DropdownMenuLabel>Playback Speed</DropdownMenuLabel>
                  {PLAYBACK_SPEEDS.map((speed) => (
                    <DropdownMenuItem
                      key={speed.value}
                      onClick={() => handlePlaybackSpeedChange(speed)}
                      className={cn(
                        'cursor-pointer hover:bg-white/20',
                        speed.value === playbackSpeed.value && 'bg-white/30'
                      )}
                    >
                      <div className="flex items-center justify-between w-full">
                        <span>{speed.label}</span>
                        {speed.value === playbackSpeed.value && (
                          <span className="text-xs text-green-400">●</span>
                        )}
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Connection speed indicator */}
              {connectionSpeed && (
                <div className="flex items-center gap-1 text-xs text-white/80">
                  {connectionQuality === 'slow' ? (
                    <WifiOff className="h-3 w-3 text-red-400" />
                  ) : connectionQuality === 'medium' ? (
                    <Wifi className="h-3 w-3 text-yellow-400" />
                  ) : (
                    <Wifi className="h-3 w-3 text-green-400" />
                  )}
                  <span>
                    {Math.round(connectionSpeed)} kbps ({connectionQuality})
                    {autoQualityEnabled && <span className="ml-1 text-blue-400">AUTO</span>}
                  </span>
                </div>
              )}

              {/* Quality selector */}
              <DropdownMenu>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-white hover:bg-white/20"
                        >
                          <Settings size={16} />
                          <span className="ml-1 text-xs">
                            {autoQualityEnabled ? `Auto (${selectedQuality.label})` : selectedQuality.label}
                          </span>
                        </Button>
                      </DropdownMenuTrigger>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p>Video quality</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <DropdownMenuContent align="end" className="bg-black/90 text-white border-white/20">
                  <DropdownMenuLabel>Video Quality</DropdownMenuLabel>
                  {VIDEO_QUALITIES.map((quality) => (
                    <DropdownMenuItem
                      key={quality.value}
                      onClick={() => handleQualityChange(quality)}
                      className={cn(
                        'cursor-pointer hover:bg-white/20',
                        (
                          (quality.value === 'auto' && autoQualityEnabled) ||
                          (quality.value === selectedQuality.value && !autoQualityEnabled)
                        ) && 'bg-white/30'
                      )}
                    >
                      <div className="flex items-center justify-between w-full">
                        <span>{quality.label}</span>
                        <div className="flex items-center gap-2">
                          {quality.bandwidth > 0 && (
                            <span className="text-xs text-gray-400">
                              {quality.bandwidth} kbps
                            </span>
                          )}
                          {quality.value === 'auto' && autoQualityEnabled && (
                            <span className="text-xs text-blue-400">●</span>
                          )}
                          {quality.value === selectedQuality.value && !autoQualityEnabled && (
                            <span className="text-xs text-green-400">●</span>
                          )}
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Fullscreen toggle */}
              {!disableFullscreen && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleToggleFullscreen}
                        className="text-white hover:bg-white/20"
                        aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
                      >
                        {isFullscreen ? <Minimize size={16} /> : <Maximize size={16} />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="top">
                      <p>{isFullscreen ? 'Exit fullscreen (f)' : 'Enter fullscreen (f)'}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdaptiveVideoPlayer