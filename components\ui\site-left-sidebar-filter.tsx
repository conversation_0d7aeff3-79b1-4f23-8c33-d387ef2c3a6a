'use client';

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { Input } from "./input";
import { Label } from "./label";
import { Checkbox } from "./checkbox";
import { ProductRatingStars } from "./product-rating-stars";

interface FilterProps {
  RowColCssCls?: string;
  setFilterValueInParent: (e: React.MouseEvent<HTMLElement> | React.ChangeEvent<HTMLInputElement>, value: string | number, filterType: string) => void;
  setLeftSidebarOpenCloseFromFilter: (e: React.MouseEvent<HTMLElement>, isOpen: boolean) => void;
}

interface Category {
  CategoryID: string;
  Name: string;
  ParentCategoryID: string | null;
  LocalizationJsonData?: Array<{ langId: string; text: string }>;
}

interface Manufacturer {
  ManufacturerID: string;
  Name: string;
}

interface Size {
  SizeID: string;
  ShortName: string;
  LocalizationJsonData?: Array<{ langId: string; text: string }>;
}

interface Tag {
  TagID: string;
  Name: string;
}

interface PriceRange {
  id: string;
  name: string;
}

export const SiteLeftSidebarFilter = (props: FilterProps) => {
  const [isCategoryOpen, setIsCategoryOpen] = useState(true);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [selectedColor, setSelectedColor] = useState<string[]>([]);
  const [selectedPrice, setSelectedPrice] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string[]>([]);
  const toggleCategory = () => setIsCategoryOpen(!isCategoryOpen);
  const [isBrandOpen, setIsBrandOpen] = useState(true);
  const [isSizeOpen, setIsSizeOpen] = useState(false);
  const [isRatingOpen, setIsRatingOpen] = useState(false);
  const toggleBrand = () => setIsBrandOpen(!isBrandOpen);
  const toggleSize = () => setIsSizeOpen(!isSizeOpen);
  const toggleRating = () => setIsRatingOpen(!isRatingOpen);
  const [isTagOpen, setIsTagOpen] = useState(false);
  const toggleTag = () => setIsTagOpen(!isTagOpen);

  const [isColorOpen, setIsColorOpen] = useState(true);
  const toggleColor = () => setIsColorOpen(!isColorOpen);
  const [isPriceOpen, setIsPriceOpen] = useState(false);
  const [radioChecked, setRadioChecked] = useState<string | null>(null);

  const togglePrice = () => {
    setIsPriceOpen(!isPriceOpen);
  };

  const [RowColCssCls] = useState(props.RowColCssCls);
  const [SizeList, setSizeList] = useState<Size[]>([]);
  const [CategoriesList, setCategoriesList] = useState<Category[]>([]);
  const [ManufacturerList, setManufacturerList] = useState<Manufacturer[]>([]);
  const [TagsList, setTagsList] = useState<Tag[]>([]);
  const [LocalizationLabelsArray, setLocalizationLabelsArray] = useState<any[]>([]);
  const [langCode, setLangCode] = useState('');
  const [defaultCurrency, setDefaultCurrency] = useState('$');

  const [PriceValuesArray, setPriceValuesArray] = useState<PriceRange[]>([
    {
      id: "10-100",
      name: `${defaultCurrency}10 - ${defaultCurrency}100`
    },
    {
      id: "100-200",
      name: `${defaultCurrency}100 - ${defaultCurrency}200`
    },
    {
      id: "200-300",
      name: `${defaultCurrency}200 - ${defaultCurrency}300`
    },
    {
      id: "300-400",
      name: `${defaultCurrency}300 - ${defaultCurrency}400`
    },
    {
      id: "400-500",
      name: `${defaultCurrency}400 - ${defaultCurrency}500`
    },
    {
      id: "500-600",
      name: `${defaultCurrency}500 - ${defaultCurrency}600`
    },
    {
      id: "600-1000000000",
      name: `Above ${defaultCurrency}600`
    }
  ]);

  const clearFilter = (e: React.MouseEvent<HTMLAnchorElement>) => {
    window.location.reload();
  };

  useEffect(() => {
    const GetFiltersAllValues = async () => {
      try {
        // Mock data for categories
        const mockCategories: Category[] = [
          { CategoryID: '1', Name: 'Electronics', ParentCategoryID: null },
          { CategoryID: '2', Name: 'Clothing', ParentCategoryID: null },
          { CategoryID: '3', Name: 'Laptops', ParentCategoryID: '1' },
          { CategoryID: '4', Name: 'Smartphones', ParentCategoryID: '1' },
          { CategoryID: '5', Name: 'Men', ParentCategoryID: '2' },
          { CategoryID: '6', Name: 'Women', ParentCategoryID: '2' },
        ];
        setCategoriesList(mockCategories);

        // Mock data for sizes
        const mockSizes: Size[] = [
          { SizeID: '1', ShortName: 'S' },
          { SizeID: '2', ShortName: 'M' },
          { SizeID: '3', ShortName: 'L' },
          { SizeID: '4', ShortName: 'XL' },
        ];
        setSizeList(mockSizes);

        // Mock data for manufacturers
        const mockManufacturers: Manufacturer[] = [
          { ManufacturerID: '1', Name: 'Apple' },
          { ManufacturerID: '2', Name: 'Samsung' },
          { ManufacturerID: '3', Name: 'Nike' },
          { ManufacturerID: '4', Name: 'Adidas' },
        ];
        setManufacturerList(mockManufacturers);

        // Mock data for tags
        const mockTags: Tag[] = [
          { TagID: '1', Name: 'New' },
          { TagID: '2', Name: 'Sale' },
          { TagID: '3', Name: 'Hot' },
          { TagID: '4', Name: 'Trending' },
        ];
        setTagsList(mockTags);
      } catch (error) {
        console.error('Error fetching filter values:', error);
      }
    };

    GetFiltersAllValues();
  }, []);

  useEffect(() => {
    const dataOperationFunc = async () => {
      // Get language code - in a real app, this would come from a session or context
      const lnCode = 'en'; // Default to English
      setLangCode(lnCode);

      // In a real app, this would fetch localization data
      const mockLocalizationData: any[] = [];
      setLocalizationLabelsArray(mockLocalizationData);
    };
    
    dataOperationFunc();
  }, []);

  // Helper function to make strings shorter if needed
  const makeAnyStringLengthShort = (str: string, maxLength: number): string => {
    if (!str) return '';
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength) + '...';
  };

  // Helper function for localization
  const replaceLoclizationLabel = (labelsArray: any[], defaultText: string, labelKey: string): string => {
    // In a real app, this would look up the localized text
    return defaultText;
  };

  return (
    <div className="collection-filter-block creative-card creative-inner category-side">
      <div className="collection-mobile-back">
        <span className="filter-back"
          onClick={(e: React.MouseEvent<HTMLSpanElement>) => {
            props.setLeftSidebarOpenCloseFromFilter(e, false);
          }}
        >
          <i className="fa fa-angle-left" aria-hidden="true"></i> back
        </span>
      </div>

      {CategoriesList && CategoriesList.length > 0 ? (
        <div className="collection-collapse-block open">
          <h3 className="collapse-block-title mt-0" onClick={toggleCategory}>
            {LocalizationLabelsArray.length > 0 ?
              replaceLoclizationLabel(LocalizationLabelsArray, "Category", "lbl_lftfilt_category")
              :
              "Category"
            }
          </h3>
          <div className={`${isCategoryOpen ? 'block' : 'hidden'}`}>
            <div className="collection-collapse-block-content">
              <div className="collection-brand-filter">
                <ul className="category-list">
                  {CategoriesList.map((item, idx) => {
                    if (CategoriesList.filter(obj => obj.ParentCategoryID === item.CategoryID).length > 0) {
                      return (
                        <li key={idx} style={{ marginTop: "21px" }}>
                          <div>
                            <Link href="#!">
                              {makeAnyStringLengthShort(item.Name, 30)}
                            </Link>
                            <div style={{ marginLeft: "20px", fontSize: "12px", lineHeight: "14px", maxHeight: "270px", overflowY: "auto" }} className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                              {CategoriesList.filter(obj => obj.ParentCategoryID === item.CategoryID).sort((a, b) => a.Name.localeCompare(b.Name)).map((elementChild, idxChild) => (
                                <div key={idxChild} className="custom-control custom-checkbox collection-filter-checkbox">
                                  <Checkbox
                                    onCheckedChange={(checked) => {
                                      const e = { target: { checked } } as unknown as React.ChangeEvent<HTMLInputElement>;
                                      props.setFilterValueInParent(e, elementChild.CategoryID, "category");
                                    }}
                                    id={`category_${idxChild}`}
                                    className="custom-control-input"
                                  />
                                  <label className="custom-control-label">
                                    {makeAnyStringLengthShort(elementChild.Name, 30)}
                                  </label>
                                </div>
                              ))}
                            </div>
                          </div>
                        </li>
                      );
                    }
                    return null;
                  })}
                </ul>
              </div>
            </div>
          </div>
        </div>
      ) : null}

      {ManufacturerList && ManufacturerList.length > 0 ? (
        <div className="collection-collapse-block open">
          <h3 className="collapse-block-title mt-0" onClick={toggleBrand}>
            {LocalizationLabelsArray.length > 0 ?
              replaceLoclizationLabel(LocalizationLabelsArray, "Brands", "lbl_lftfilt_brand")
              :
              "Brands"
            }
          </h3>
          <div className={`${isBrandOpen ? 'block' : 'hidden'}`}>
            <div className="collection-collapse-block-content">
              <div className="collection-brand-filter">
                {ManufacturerList.slice(0, 10).map((item, idx) => (
                  <div key={idx} className="custom-control custom-checkbox collection-filter-checkbox">
                    <Checkbox
                      onCheckedChange={(checked) => {
                        const e = { target: { checked } } as unknown as React.ChangeEvent<HTMLInputElement>;
                        props.setFilterValueInParent(e, item.ManufacturerID, "brand");
                      }}
                      className="custom-control-input"
                      id={`brad_${idx}`}
                    />
                    <label className="custom-control-label">{item.Name}</label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : null}

      {SizeList && SizeList.length > 0 ? (
        <div className="collection-collapse-block open">
          <h3 className="collapse-block-title mt-0" onClick={toggleSize}>
            {LocalizationLabelsArray.length > 0 ?
              replaceLoclizationLabel(LocalizationLabelsArray, "Size", "lbl_lftfilt_size")
              :
              "Size"
            }
          </h3>
          <div className={`${isSizeOpen ? 'block' : 'hidden'}`}>
            <div className="collection-collapse-block-content">
              <div className="collection-brand-filter">
                {SizeList.slice(0, 10).map((item, idx) => (
                  <div key={idx} className="custom-control custom-checkbox collection-filter-checkbox">
                    <Checkbox
                      onCheckedChange={(checked) => {
                        const e = { target: { checked } } as unknown as React.ChangeEvent<HTMLInputElement>;
                        props.setFilterValueInParent(e, item.SizeID, "size");
                      }}
                      className="custom-control-input"
                      id={`size_${idx}`}
                    />
                    <label className="custom-control-label">
                      {makeAnyStringLengthShort(item.ShortName, 30)}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : null}

      {PriceValuesArray && PriceValuesArray.length > 0 ? (
        <div className="collection-collapse-block border-0 open">
          <h3 className="collapse-block-title" onClick={togglePrice}>
            {LocalizationLabelsArray.length > 0 ?
              replaceLoclizationLabel(LocalizationLabelsArray, "Price", "lbl_lftfilt_price")
              :
              "Price"
            }
          </h3>
          <div className={`${isPriceOpen ? 'block' : 'hidden'}`}>
            <div className="collection-collapse-block-content">
              <div className="collection-brand-filter">
                {PriceValuesArray.map((item, idx) => (
                  <div key={idx} className="custom-control custom-checkbox collection-filter-checkbox">
                    <Input
                      onClick={(e: React.MouseEvent<HTMLInputElement>) => {
                        props.setFilterValueInParent(e, item.id, "price");
                      }}
                      type="radio"
                      name="price-filter"
                      className="custom-control-input"
                      id={`price_${idx}`}
                      checked={radioChecked === item.id}
                      onChange={() => setRadioChecked(item.id)}
                    />
                    <Label className="custom-control-label">{item.name}</Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : null}

      <div className="collection-collapse-block open">
        <h3 className="collapse-block-title mt-0" onClick={toggleRating}>
          {LocalizationLabelsArray.length > 0 ?
            replaceLoclizationLabel(LocalizationLabelsArray, "Rating", "lbl_lftfilt_rating")
            :
            "Rating"
          }
        </h3>
        <div className={`${isRatingOpen ? 'block' : 'hidden'}`}>
          <div className="collection-collapse-block-content">
            <div className="collection-brand-filter">
              <div className="custom-control custom-checkbox collection-filter-checkbox">
                <Link href="#" onClick={(e: React.MouseEvent<HTMLAnchorElement>) => { props.setFilterValueInParent(e, 5, "rating"); }}>
                  <ProductRatingStars rating={5} />
                </Link>
              </div>
              <div className="custom-control custom-checkbox collection-filter-checkbox">
                <Link href="#" onClick={(e: React.MouseEvent<HTMLAnchorElement>) => { props.setFilterValueInParent(e, 4, "rating"); }}>
                  <ProductRatingStars rating={4} />
                </Link>
              </div>
              <div className="custom-control custom-checkbox collection-filter-checkbox">
                <Link href="#" onClick={(e: React.MouseEvent<HTMLAnchorElement>) => { props.setFilterValueInParent(e, 3, "rating"); }}>
                  <ProductRatingStars rating={3} />
                </Link>
              </div>
              <div className="custom-control custom-checkbox collection-filter-checkbox">
                <Link href="#" onClick={(e: React.MouseEvent<HTMLAnchorElement>) => { props.setFilterValueInParent(e, 2, "rating"); }}>
                  <ProductRatingStars rating={2} />
                </Link>
              </div>
              <div className="custom-control custom-checkbox collection-filter-checkbox">
                <Link href="#" onClick={(e: React.MouseEvent<HTMLAnchorElement>) => { props.setFilterValueInParent(e, 1, "rating"); }}>
                  <ProductRatingStars rating={1} />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {TagsList && TagsList.length > 0 ? (
        <div className="collection-collapse-block open">
          <h3 className="collapse-block-title mt-0" onClick={toggleTag}>
            {LocalizationLabelsArray.length > 0 ?
              replaceLoclizationLabel(LocalizationLabelsArray, "Tags", "lbl_lftfilt_tags")
              :
              "Tags"
            }
          </h3>
          <div className={`${isTagOpen ? 'block' : 'hidden'}`}>
            <div className="collection-collapse-block-content">
              <div className="collection-brand-filter">
                {TagsList.slice(0, 10).map((item, idx) => (
                  <div key={idx} className="custom-control custom-checkbox collection-filter-checkbox">
                    <Checkbox
                      onCheckedChange={(checked) => {
                        const e = { target: { checked } } as unknown as React.ChangeEvent<HTMLInputElement>;
                        props.setFilterValueInParent(e, item.TagID, "tag");
                      }}
                      className="custom-control-input"
                      id={`tag_${idx}`}
                    />
                    <label className="custom-control-label">{item.Name}</label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : null}

      <div className="filter-buttons">
        <a href="#" className="btn btn-theme filter-btn" onClick={clearFilter}>
          {LocalizationLabelsArray.length > 0 ?
            replaceLoclizationLabel(LocalizationLabelsArray, "Clear Filters", "lbl_lftfilt_clearfilters")
            :
            "Clear Filters"
          }
        </a>
      </div>
    </div>
  );
};