'use client';

import { useEffect } from 'react';
import { generateProductStructuredData, ProductStructuredData } from '@/lib/structured-data';

interface ProductSEOProps {
  product: {
    id: string | number;
    name: string;
    description: string;
    image?: string;
    images?: string[];
    price?: number;
    originalPrice?: number;
    discountPrice?: number;
    currency?: string;
    brand?: string;
    sku?: string;
    availability?: boolean;
    rating?: number;
    reviewCount?: number;
    category?: string;
  };
  baseUrl?: string;
}

export function ProductStructuredData({ product, baseUrl = '' }: ProductSEOProps) {
  useEffect(() => {
    // Safety check - don't proceed if product is missing essential data
    if (!product || !product.id || !product.name) {
      return;
    }

    // Generate image URLs
    const images: string[] = [];
    if (product.images && product.images.length > 0) {
      images.push(...product.images.map(img => img.startsWith('http') ? img : `${baseUrl}${img}`));
    } else if (product.image) {
      const imageUrl = product.image.startsWith('http') ? product.image : `${baseUrl}${product.image}`;
      images.push(imageUrl);
    } else {
      // Fallback image
      images.push(`${baseUrl}/placeholder-image.svg`);
    }

    // Determine the price to use
    const finalPrice = product.discountPrice || product.price;

    // Create structured data
    const structuredDataInput: ProductStructuredData = {
      name: product.name,
      description: product.description || `${product.name} - Medical course/resource available at Code Medical`,
      image: images,
      sku: product.sku || product.id.toString(),
      brand: product.brand || 'Code Medical',
      url: `${window.location.origin}/products/${product.id}`,
    };

    // Add pricing if available
    if (finalPrice !== undefined) {
      structuredDataInput.price = finalPrice;
      structuredDataInput.priceCurrency = product.currency || 'USD';
      structuredDataInput.availability = product.availability !== false ? 'InStock' : 'OutOfStock';
      structuredDataInput.condition = 'NewCondition';
      
      // Set price valid until (1 year from now)
      const nextYear = new Date();
      nextYear.setFullYear(nextYear.getFullYear() + 1);
      structuredDataInput.priceValidUntil = nextYear.toISOString().split('T')[0];
    }

    // Add rating if available
    if (product.rating && product.reviewCount) {
      structuredDataInput.rating = {
        ratingValue: product.rating,
        bestRating: 5,
        reviewCount: product.reviewCount
      };
    }

    const structuredData = generateProductStructuredData(structuredDataInput);

    // Create and inject the script tag
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData, null, 2);
    script.id = `product-structured-data-${product.id}`;

    // Remove existing script if it exists
    const existingScript = document.getElementById(`product-structured-data-${product.id}`);
    if (existingScript) {
      existingScript.remove();
    }

    // Add new script to head
    document.head.appendChild(script);

    // Cleanup function
    return () => {
      const scriptToRemove = document.getElementById(`product-structured-data-${product.id}`);
      if (scriptToRemove) {
        scriptToRemove.remove();
      }
    };
  }, [product, baseUrl]);

  return null; // This component doesn't render anything visible
}

