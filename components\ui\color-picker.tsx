'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from './button';
import { X, Check } from 'lucide-react';
import { useColorThemeContext } from '@/contexts/color-theme-context';

interface ColorPickerProps {
  onColorSelect?: (color: string) => void;
  onClose: () => void;
}

// Color palette matching the image shown
const colors = [
  // Row 1: Blues and greens
  '#0074b2', '#2d5a3d', '#2a9d8f',
  // Row 2: Light colors
  '#87ceeb', '#f295ce', '#fce4ec',
  // Row 3: Purples and browns
  '#b39ddb', '#bcaaa4', '#ffccbc',
  // Row 4: More colors
  '#b2dfdb', '#6c9bcf', '#ffd552',
  // Row 5: Final row
  '#0891b2', '#7986cb', '#003554'
];

export function ColorPicker({ onColorSelect, onClose }: ColorPickerProps) {
  const { currentTheme, createCustomTheme } = useColorThemeContext();
  const [selectedColor, setSelectedColor] = useState<string>(currentTheme.primary);

  useEffect(() => {
    setSelectedColor(currentTheme.primary);
  }, [currentTheme.primary]);

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    // Create and apply the custom theme - this will save to cookies automatically
    createCustomTheme(color, 'Custom Color');
    // Call the optional callback
    onColorSelect?.(color);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl p-6 w-[400px] max-w-[90vw]">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Choose a Color</h3>
          <Button variant="ghost" size="icon" onClick={onClose} className="hover:bg-muted">
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="grid grid-cols-3 gap-3">
          {colors.map((color) => (
            <button
              key={color}
              className={`relative w-full aspect-square rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-md border-2 ${
                selectedColor === color 
                  ? 'border-gray-400 shadow-md' 
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              style={{ backgroundColor: color }}
              onClick={() => handleColorSelect(color)}
            >
              {selectedColor === color && (
                <Check className="absolute inset-0 m-auto h-5 w-5 text-white drop-shadow-lg" />
              )}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}