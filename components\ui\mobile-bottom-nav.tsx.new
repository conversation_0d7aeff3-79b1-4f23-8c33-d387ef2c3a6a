'use client';

import { Home, Search, Package, User, ShoppingCart, Heart, Grid3X3, ChevronRight, ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useSettings } from '@/contexts/settings-context';
import { useState, useEffect } from 'react';
import { Button } from './button';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';

interface Category {
  CategoryID: string | number;
  ParentCategoryID: string | number | null;
  Name: string;
  // Add other category properties as needed
}

export function MobileBottomNav() {
  const pathname = usePathname();
  const router = useRouter();
  const { totalItems: cartCount } = useCart();
  const { totalItems: wishlistCount } = useWishlist();
  const { primaryColor, t } = useSettings();
  const [mounted, setMounted] = useState(false);
  const [showCategories, setShowCategories] = useState(false);
  const [allCategories, setAllCategories] = useState<Category[]>([]);
  const [parentCategories, setParentCategories] = useState<Category[]>([]);
  const [selectedParent, setSelectedParent] = useState<Category | null>(null);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch categories from API
  const fetchCategories = async () => {
    if (parentCategories.length > 0) {
      setShowCategories(true);
      return;
    }

    setIsLoadingCategories(true);
    try {
      const param = {
        "PageNumber": 1,
        "PageSize": 100,
        "SortColumn": "Name",
        "SortOrder": "ASC"
      };
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + (typeof window !== 'undefined' ? localStorage.getItem('token') : '')
      };
      const categoriesResponse = await MakeApiCallAsync(
        Config.END_POINT_NAMES.GET_CATEGORIES_LIST, 
        null, 
        param, 
        headers, 
        "POST", 
        true
      );

      if (categoriesResponse?.data?.data) {
        try {
          const parsedData = JSON.parse(categoriesResponse.data.data);
          if (Array.isArray(parsedData)) {
            setAllCategories(parsedData);
            // Get parent categories (those without a ParentCategoryID)
            const parents = parsedData.filter((cat: Category) => !cat.ParentCategoryID);
            setParentCategories(parents);
            setShowCategories(true);
          }
        } catch (parseError) {
          console.error('Error parsing categories data:', parseError);
        }
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setIsLoadingCategories(false);
    }
  };

  const handleCategoryClick = (category: Category) => {
    const childCategories = allCategories.filter(cat => cat.ParentCategoryID === category.CategoryID);
    if (childCategories.length > 0) {
      setSelectedParent(category);
    } else {
      router.push(`/products?category=${category.CategoryID}`);
      setShowCategories(false);
    }
  };

  if (!mounted) return null;

  const navItems = [
    {
      href: '/',
      icon: Home,
      label: t('home') || 'الرئيسية',
      isActive: pathname === '/',
      onClick: null
    },
    {
      href: '#',
      icon: Grid3X3,
      label: t('categories') || 'التصنيفات',
      isActive: false,
      onClick: fetchCategories
    },
    {
      href: '/cart',
      icon: ShoppingCart,
      label: t('cart') || 'سلة التسوق',
      isActive: pathname === '/cart',
      badge: cartCount || 0,
      onClick: null
    },
    {
      href: '/wishlist',
      icon: Heart,
      label: t('wishlist') || 'المفضلة',
      isActive: pathname === '/wishlist',
      badge: wishlistCount || 0,
      onClick: null
    },
    {
      href: '/login',
      icon: User,
      label: t('login') || 'حسابي',
      isActive: pathname === '/login' || pathname === '/signup',
      onClick: null
    }
  ];

  const renderCategories = () => {
    if (isLoadingCategories) {
      return (
        <div className="p-8 text-center">
          <div 
            className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" 
            style={{ borderColor: primaryColor }}
          />
          <p className="text-gray-500">جاري التحميل...</p>
        </div>
      );
    }

    if (selectedParent) {
      const childCategories = allCategories.filter(
        cat => cat.ParentCategoryID === selectedParent.CategoryID
      );

      return (
        <>
          <Link
            href={`/products?category=${selectedParent.CategoryID}`}
            className="block p-4 font-medium text-center border-b"
            style={{ color: primaryColor, backgroundColor: `${primaryColor}10` }}
            onClick={() => setShowCategories(false)}
          >
            عرض الكل في {selectedParent.Name}
          </Link>
          {childCategories.map((category) => (
            <Link
              key={category.CategoryID}
              href={`/products?category=${category.CategoryID}`}
              className="block p-4 border-b hover:bg-gray-50"
              onClick={() => setShowCategories(false)}
            >
              <div className="flex items-center justify-between">
                <span>{category.Name}</span>
              </div>
            </Link>
          ))}
        </>
      );
    }

    return parentCategories.map((category) => {
      const hasChildren = allCategories.some(cat => cat.ParentCategoryID === category.CategoryID);
      return (
        <button
          key={category.CategoryID}
          onClick={() => handleCategoryClick(category)}
          className="w-full text-left p-4 border-b hover:bg-gray-50 flex justify-between items-center"
        >
          <span>{category.Name}</span>
          {hasChildren && <ChevronLeft className="h-4 w-4 text-gray-400" />}
        </button>
      );
    });
  };

  return (
    <>
      {/* Mobile Navigation Bar */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb">
        <div className="flex items-center justify-around py-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isButton = !!item.onClick;
            const className = "flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1" + 
              (isButton ? " bg-transparent border-none" : "");
            
            const content = (
              <>
                <div className="relative">
                  <Icon
                    className="h-6 w-6 mb-1"
                    style={{
                      color: item.isActive ? primaryColor : '#6B7280'
                    }}
                  />
                  {item.badge !== undefined && item.badge > 0 && (
                    <span
                      className="absolute -top-1 -right-1 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium shadow-md"
                      style={{ backgroundColor: primaryColor }}
                    >
                      {item.badge > 99 ? '99+' : item.badge}
                    </span>
                  )}
                </div>
                <span
                  className="text-xs font-medium text-center leading-tight mt-1"
                  style={{
                    color: item.isActive ? primaryColor : '#6B7280'
                  }}
                >
                  {item.label}
                </span>
              </>
            );

            return isButton ? (
              <button
                key={item.href}
                onClick={item.onClick || undefined}
                className={className}
                type="button"
              >
                {content}
              </button>
            ) : (
              <Link
                key={item.href}
                href={item.href}
                className={className}
              >
                {content}
              </Link>
            );
          })}
        </div>
      </div>

      {/* Categories Modal */}
      {showCategories && (
        <div 
          className="md:hidden fixed inset-0 bg-black/50 z-50 flex items-end transition-opacity"
          onClick={() => {
            setShowCategories(false);
            setSelectedParent(null);
          }}
        >
          <div 
            className="bg-white w-full max-h-[80vh] rounded-t-xl overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between p-4 border-b sticky top-0 bg-white z-10">
              <div className="flex items-center">
                {selectedParent && (
                  <button 
                    onClick={() => setSelectedParent(null)}
                    className="mr-2 p-1 text-gray-500 hover:text-gray-700"
                  >
                    <ChevronRight className="h-5 w-5" />
                  </button>
                )}
                <h2 
                  className="text-lg font-semibold" 
                  style={{ color: primaryColor }}
                >
                  {selectedParent ? selectedParent.Name : 'التصنيفات'}
                </h2>
              </div>
              <button
                onClick={() => {
                  setShowCategories(false);
                  setSelectedParent(null);
                }}
                className="text-gray-500 hover:text-gray-700 p-1"
              >
                ✕
              </button>
            </div>
            <div className="overflow-y-auto max-h-[calc(80vh-64px)]">
              {renderCategories()}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
