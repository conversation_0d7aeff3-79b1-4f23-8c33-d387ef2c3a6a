'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { useColorThemeContext } from '@/contexts/color-theme-context';

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  categories?: {
    id: string;
    name: string;
    icon?: React.ReactNode;
  }[];
}

export function Sidebar({ className, categories = [], ...props }: SidebarProps) {
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <div
        className={cn(
          "fixed inset-0 bg-black/20 backdrop-blur-sm z-30 md:hidden transition-opacity duration-300",
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={() => setIsOpen(false)}
      />
      <button
        className="md:hidden fixed top-4 left-4 z-50 p-2 rounded-lg bg-background/80 backdrop-blur-sm hover:bg-accent/10 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Toggle menu"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <line x1="3" y1="12" x2="21" y2="12" />
          <line x1="3" y1="6" x2="21" y2="6" />
          <line x1="3" y1="18" x2="21" y2="18" />
        </svg>
      </button>
      <div
        className={cn(
          'fixed md:relative top-0 left-0 h-full w-64 bg-gradient-to-br from-background via-background/95 to-background/90 rounded-r-2xl shadow-lg px-5 py-6 space-y-6 backdrop-blur-sm z-40',
          'transform transition-transform duration-300 ease-in-out md:transform-none',
          isOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0',
          className
        )}
        {...props}
      >
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">Categories</h2>
        <div className="h-1 w-10 bg-gradient-to-r from-primary to-primary/30 rounded-full"></div>
      </div>

      <nav className="space-y-2">
        {categories.map((category) => {
          const isActive = activeCategory === category.id;
          return (
            <a
              key={category.id}
              href={`/category/${category.id}`}
              className={cn(
                'flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300',
                'hover:bg-accent/10 hover:shadow-lg hover:translate-x-1',
                'focus:outline-none focus:ring-2',
                isActive ? 'bg-accent/20 font-medium shadow-md' : 'text-foreground/80'
              )}
              style={{
                ...(isActive && { color: primaryColor }),
                '--focus-ring-color': `${primaryColor}33`
              } as React.CSSProperties & { '--focus-ring-color': string }}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.icon && (
                <span style={{ color: `${primaryColor}CC` }}>{category.icon}</span>
              )}
              <span className="text-sm font-medium">{category.name}</span>
              {isActive && (
                <div
                  className="ml-auto h-2 w-2 rounded-full shadow-lg"
                  style={{ 
                    backgroundColor: primaryColor,
                    boxShadow: `0 4px 6px -1px ${primaryColor}33, 0 2px 4px -1px ${primaryColor}1A`
                  }}
                />
              )}
            </a>
          );
        })}
      </nav>

      {categories.length === 0 && (
        <div className="text-sm text-muted-foreground italic px-4 py-3 rounded-lg bg-accent/5">No categories available</div>
      )}
    </div>
    </div>
  );
}