"use client"

import Link from "next/link"
import { ArrowLeft, AlertCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface ProductErrorProps {
  error: string
  retry: () => void
}

export default function ProductError({ error, retry }: ProductErrorProps) {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col items-center justify-center text-center p-8 border rounded-lg bg-red-50">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h1 className="text-2xl font-bold mb-2">Error Loading Product</h1>
        <p className="text-gray-600 mb-6">{error}</p>
        <div className="flex gap-4">
          <Button onClick={retry}>Try Again</Button>
          <Link href="/products">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
