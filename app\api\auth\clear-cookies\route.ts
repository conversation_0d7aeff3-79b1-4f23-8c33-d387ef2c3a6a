import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Create response
    const response = NextResponse.json({ success: true });

    // Fix domain configuration for Vercel and Amplify
    let domain: string | undefined = undefined;
    if (process.env.NODE_ENV === 'production') {
      domain = process.env.NEXT_PUBLIC_DOMAIN || 
               process.env.VERCEL_URL ||
               process.env.AMPLIFY_URL ||
               request.headers.get('host')?.replace('www.', '');
      
      if (domain && !domain.startsWith('.')) {
        domain = `.${domain}`;
      }
    }

    console.log('🗑️ Clearing cookies with domain:', domain);

    // Clear all authentication cookies
    const cookiesToClear = [
      'auth_token',
      'auth_token_client',
      'refresh_token', 
      'auth_user',
      'auth_user_public',
      'is_logged_in'
    ];

    cookiesToClear.forEach(cookieName => {
      const isHttpOnly = ['auth_token', 'auth_user', 'refresh_token'].includes(cookieName);
      
      response.cookies.set(cookieName, '', {
        httpOnly: isHttpOnly,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict'
        path: '/',
        maxAge: 0, // Expire immediately
        domain,
      });
    });

    // Also clear any potential session storage or local storage related cookies
    // by setting additional cleanup cookies
    const additionalCleanup = ['session_id', 'user_session', 'login_state'];
    additionalCleanup.forEach(cookieName => {
      response.cookies.set(cookieName, '', {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict'
        path: '/',
        maxAge: 0,
        domain,
      });
    });

    console.log('🔐 All authentication cookies cleared');
    return response;

  } catch (error) {
    console.error('Error clearing cookies:', error);
    return NextResponse.json(
      { error: 'Failed to clear cookies' },
      { status: 500 }
    );
  }
}
