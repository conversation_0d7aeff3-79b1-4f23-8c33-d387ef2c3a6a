"use client";

import {
  Bread<PERSON>rumb,
  Bread<PERSON>rumbItem,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card } from "@/components/ui/card";
import Link from "next/link";
import { useSettings } from "@/contexts/settings-context";
import { useColorThemeContext } from "@/contexts/color-theme-context";
import { Award, Users, BookOpen, Heart, Clock, MapPin } from "lucide-react";

export default function AboutPage() {
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">{t("home")}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t("about")}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Page Content */}
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">{t("about")}</h1>

        {/* Hero Section */}
        <div className="relative rounded-lg overflow-hidden mb-12">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/90 to-primary/70 z-10"></div>
          <img
            src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
            alt="WE Code Medical Website Ecommrece"
            className="w-full h-64 md:h-96 object-cover"
          />
          <div className="absolute inset-0 z-20 flex items-center justify-center text-center p-6">
            <div className="max-w-3xl">
              <h2 className="text-2xl md:text-4xl font-bold text-white mb-4 drop-shadow-lg [text-shadow:2px_2px_4px_#000]">
                We are Code Medical Website Ecommrece
              </h2>
              <p className="text-white/90 text-lg md:text-xl drop-shadow-md [text-shadow:1px_1px_2px_#000]">
                A professional team specialized in providing well-known valuable
                medical courses, eBooks, printed books, and popular medical
                accounts for all medical field staff around the world at low
                cost and in short time.
              </p>
            </div>
          </div>
        </div>

        {/* Our Mission */}
        <Card className="mb-12 overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-2">
            <div className="p-8 md:p-12 flex flex-col justify-center">
              <h2 className="text-2xl font-bold mb-6">Our Mission</h2>
              <p className="text-muted-foreground mb-6">
                At Code Medical Website, our mission is to make high-quality
                medical education accessible to healthcare professionals
                worldwide. We believe that knowledge should not be limited by
                geographical boundaries or financial constraints.
              </p>
              <p className="text-muted-foreground">
                We strive to provide comprehensive, up-to-date, and practical
                resources that help medical professionals enhance their skills,
                stay current with the latest developments, and deliver better
                patient care.
              </p>
            </div>
            <div className="bg-gray-100 flex items-center justify-center p-8">
              <img
                src="https://images.unsplash.com/photo-1505751172876-fa1923c5c528?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                alt="Medical professionals"
                className="rounded-lg max-h-80 object-cover shadow-lg"
              />
            </div>
          </div>
        </Card>

        {/* Our Values */}
        <h2 className="text-2xl font-bold mb-6">Our Values</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card>
            <div className="p-6">
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center mb-4"
                style={{ backgroundColor: `${primaryColor}20` }}
              >
                <Award className="h-6 w-6" style={{ color: primaryColor }} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Excellence</h3>
              <p className="text-muted-foreground">
                We are committed to excellence in everything we do, from the
                quality of our educational materials to our customer service.
              </p>
            </div>
          </Card>

          <Card>
            <div className="p-6">
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center mb-4"
                style={{ backgroundColor: `${primaryColor}20` }}
              >
                <Users className="h-6 w-6" style={{ color: primaryColor }} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Accessibility</h3>
              <p className="text-muted-foreground">
                We believe in making medical education accessible to all,
                regardless of location or financial constraints.
              </p>
            </div>
          </Card>

          <Card>
            <div className="p-6">
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center mb-4"
                style={{ backgroundColor: `${primaryColor}20` }}
              >
                <BookOpen className="h-6 w-6" style={{ color: primaryColor }} />
              </div>
              <h3 className="text-xl font-semibold mb-2">Innovation</h3>
              <p className="text-muted-foreground">
                We continuously seek innovative ways to deliver medical
                education and improve the learning experience.
              </p>
            </div>
          </Card>
        </div>

        {/* Why Choose Us */}
        <h2 className="text-2xl font-bold mb-6">Why Choose Code Medical</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <Card>
            <div className="p-6 flex">
              <div className="mr-4">
                <div
                  className="w-10 h-10 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: `${primaryColor}20` }}
                >
                  <Heart className="h-5 w-5" style={{ color: primaryColor }} />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">
                  Quality Resources
                </h3>
                <p className="text-muted-foreground">
                  Our educational materials are developed by experienced medical
                  professionals and undergo rigorous quality checks.
                </p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="p-6 flex">
              <div className="mr-4">
                <div
                  className="w-10 h-10 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: `${primaryColor}20` }}
                >
                  <Clock className="h-5 w-5" style={{ color: primaryColor }} />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">Fast Delivery</h3>
                <p className="text-muted-foreground">
                  We ensure quick delivery of our products, whether digital or
                  physical, to save your valuable time.
                </p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="p-6 flex">
              <div className="mr-4">
                <div
                  className="w-10 h-10 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: `${primaryColor}20` }}
                >
                  <MapPin className="h-5 w-5" style={{ color: primaryColor }} />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">Global Reach</h3>
                <p className="text-muted-foreground">
                  We serve medical professionals worldwide, with resources
                  tailored to different regions and specialties.
                </p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="p-6 flex">
              <div className="mr-4">
                <div
                  className="w-10 h-10 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: `${primaryColor}20` }}
                >
                  <Users className="h-5 w-5" style={{ color: primaryColor }} />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">Customer Support</h3>
                <p className="text-muted-foreground">
                  Our dedicated customer support team is always ready to assist
                  you with any queries or concerns.
                </p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
