'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { fetchCurrencyRate, convertUSDToIQD, formatPrice } from '@/lib/api-helper';

interface CurrencyContextType {
  rate: number;
  isLoading: boolean;
  convertToIQD: (usdPrice: number) => number;
  formatUSD: (price: number | undefined | null) => string;
  formatIQD: (price: number | undefined | null) => string;
  refreshRate: () => Promise<void>;
  lastUpdated?: Date;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

export function CurrencyProvider({ children }: { children: React.ReactNode }) {
  const [rate, setRate] = useState<number>(1500); // Default rate
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [lastUpdated, setLastUpdated] = useState<Date | undefined>(undefined);

  const loadCurrencyRate = async () => {
    console.log('💱 CurrencyProvider: Loading exchange rate...');
    setIsLoading(true);
    try {
      const fetchedRate = await fetchCurrencyRate();
      console.log('💱 CurrencyProvider: Fetched rate:', fetchedRate);
      
      // Validate the fetched rate
      if (fetchedRate && !isNaN(fetchedRate) && fetchedRate > 0) {
        setRate(fetchedRate);
        setLastUpdated(new Date());
        console.log('✅ CurrencyProvider: Exchange rate updated successfully to:', fetchedRate);
      } else {
        console.warn('⚠️ CurrencyProvider: Invalid rate received, keeping default:', rate);
      }
    } catch (error) {
      console.error('❌ CurrencyProvider: Failed to load currency rate:', error);
      // Keep default rate
    } finally {
      setIsLoading(false);
      console.log('💱 CurrencyProvider: Loading complete. Final rate:', rate);
    }
  };

  const refreshRate = async () => {
    console.log('💱 CurrencyProvider: Manually refreshing exchange rate...');
    await loadCurrencyRate();
  };

  const convertToIQD = (usdPrice: number): number => {
    const result = convertUSDToIQD(usdPrice, rate);
    console.log(`💱 CurrencyProvider: Converting $${usdPrice} at rate ${rate} = ${result} IQD`);
    return result;
  };

  const formatUSD = (price: number | undefined | null): string => {
    return formatPrice(price, 'USD');
  };

  const formatIQD = (price: number | undefined | null): string => {
    return formatPrice(price, 'IQD');
  };

  useEffect(() => {
    console.log('💱 CurrencyProvider: Initializing...');
    loadCurrencyRate();
  }, []);

  return (
    <CurrencyContext.Provider
      value={{
        rate,
        isLoading,
        convertToIQD,
        formatUSD,
        formatIQD,
        refreshRate,
        lastUpdated
      }}
    >
      {children}
    </CurrencyContext.Provider>
  );
}

export function useCurrency() {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
}
