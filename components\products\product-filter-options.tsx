'use client';

import { useState } from 'react';
import { Grid2X2, Grid3X3, List } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useSettings } from '@/contexts/settings-context';

interface ProductFilterOptionsProps {
  onGridChange: (gridType: string) => void;
  onPageSizeChange: (pageSize: number) => void;
  onSortChange: (sortOption: string) => void;
  pageSize: number;
  totalRecords: number;
  currentPage: number;
}

export function ProductFilterOptions({
  onGridChange,
  onPageSizeChange,
  onSortChange,
  pageSize,
  totalRecords,
  currentPage
}: ProductFilterOptionsProps) {
  const { t } = useSettings();
  const [activeGrid, setActiveGrid] = useState('grid-3');

  const handleGridChange = (gridType: string) => {
    setActiveGrid(gridType);
    onGridChange(gridType);
  };

  const handlePageSizeChange = (value: string) => {
    onPageSizeChange(parseInt(value));
  };

  const handleSortChange = (value: string) => {
    onSortChange(value);
  };

  // Calculate showing products range
  const startRecord = (currentPage - 1) * pageSize + 1;
  const endRecord = Math.min(currentPage * pageSize, totalRecords);

  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
      <div className="flex items-center space-x-2">
        <Button
          variant={activeGrid === 'grid-2' ? 'default' : 'outline'}
          size="icon"
          onClick={() => handleGridChange('grid-2')}
          className="h-8 w-8"
        >
          <Grid2X2 className="h-4 w-4" />
        </Button>
        <Button
          variant={activeGrid === 'grid-3' ? 'default' : 'outline'}
          size="icon"
          onClick={() => handleGridChange('grid-3')}
          className="h-8 w-8"
        >
          <Grid3X3 className="h-4 w-4" />
        </Button>
        <Button
          variant={activeGrid === 'list' ? 'default' : 'outline'}
          size="icon"
          onClick={() => handleGridChange('list')}
          className="h-8 w-8"
        >
          <List className="h-4 w-4" />
        </Button>

        <p className="text-sm text-muted-foreground ml-4 hidden sm:block">
          Showing {startRecord}-{endRecord} of {totalRecords} products
        </p>
      </div>

      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 w-full sm:w-auto">
        <div className="flex items-center space-x-2 w-full sm:w-auto">
          <span className="text-sm whitespace-nowrap">Show:</span>
          <Select defaultValue={pageSize.toString()} onValueChange={handlePageSizeChange}>
            <SelectTrigger className="w-[80px]">
              <SelectValue placeholder={pageSize.toString()} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="9">9</SelectItem>
              <SelectItem value="12">12</SelectItem>
              <SelectItem value="24">24</SelectItem>
              <SelectItem value="36">36</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2 w-full sm:w-auto">
          <span className="text-sm whitespace-nowrap">Sort By:</span>
          <Select defaultValue="0" onValueChange={handleSortChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Default Sorting" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">Default Sorting</SelectItem>
              <SelectItem value="1">Price: Low to High</SelectItem>
              <SelectItem value="2">Price: High to Low</SelectItem>
              <SelectItem value="3">Name: A to Z</SelectItem>
              <SelectItem value="4">Name: Z to A</SelectItem>
              <SelectItem value="5">Newest First</SelectItem>
              <SelectItem value="6">Oldest First</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
