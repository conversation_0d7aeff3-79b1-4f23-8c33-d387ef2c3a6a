'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { ProductBox } from '@/components/ui/product-box';
import { Product as ApiProduct, ApiResponse } from '@/types/product';
import { Config } from '@/lib/config';

interface ProductImage {
  id: string;
  url: string;
  alt: string;
}

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  discountedPrice?: number;
  images: ProductImage[];
  rating: number;
  categoryName: string;
  categorySlug: string;
  isNew?: boolean;
  inStock?: boolean;
}

interface PopularProductsProps {
  title?: string;
  limit?: number;
  hoverEffect?: string;
}

export function PopularProducts({ title = 'popularProducts', limit = 8, hoverEffect }: PopularProductsProps) {
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const destructiveColor = '#dc2626'; // Red color for error states
  
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const fetchPopularProducts = async () => {
      setLoading(true);
      try {
        // Prepare parameters and headers according to API requirements
        const param = {
          "requestParameters": {
            "PageNo": 1,
            "PageSize": limit,
            "SortColumn": "ViewCount", // Sort by view count for popular products
            "SortOrder": "DESC", // Highest views first
            "FilterColumn": "ShowOnHomePage", // Only show products marked for home page
            "FilterValue": "true",
            "recordValueJson": "[]"
          }
        };

        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        };

        // Import MakeApiCallAsync from api-helper
        const { MakeApiCallAsync } = await import('@/lib/api-helper');

        try {
          // Use the API helper to make the call with the exact endpoint from the screenshot
          const response = await MakeApiCallAsync(
            'api/v1/products/get-all-products', // Exact endpoint from the screenshot
            null,
            {
              requestParameters: {
                SearchTerm: "",
                CategoryID: null,
                TagID: null,
                ManufacturerID: null,
                MinPrice: null,
                MaxPrice: null,
                Rating: null,
                OrderByColumnName: "Price DESC", // Sort by price descending as shown in screenshot
                PageNo: 1,
                PageSize: limit,
                recordValueJson: "[]"
              }
            },
            headers,
            "POST",
            true
          );

          // Process the response
          if (response?.data?.data) {
            try {
              const parsedData = JSON.parse(response.data.data);
              console.log('Popular products data:', parsedData);

              if (Array.isArray(parsedData)) {
                // Transform the API data to match our Product interface
                const transformedProducts = parsedData.map(item => {
                  // Handle different possible image structures
                  let productImages = [];

                  if (item.ProductImagesJson && Array.isArray(item.ProductImagesJson)) {
                    productImages = item.ProductImagesJson.map((img: any) => ({
                      id: img.AttachmentID?.toString() || '',
                      url: img.AttachmentURL ?
                        (img.AttachmentURL.startsWith('http') ?
                          img.AttachmentURL :
                          `${Config.ADMIN_BASE_URL}${img.AttachmentURL.startsWith('/') ? img.AttachmentURL : '/' + img.AttachmentURL}`) :
                        '/images/placeholder.jpg',
                      alt: item.ProductName || 'Product image'
                    }));
                  } else if (item.ProductImages && Array.isArray(item.ProductImages)) {
                    productImages = item.ProductImages.map((img: any) => ({
                      id: img.AttachmentID?.toString() || '',
                      url: img.AttachmentURL ?
                        (img.AttachmentURL.startsWith('http') ?
                          img.AttachmentURL :
                          `${Config.ADMIN_BASE_URL}${img.AttachmentURL.startsWith('/') ? img.AttachmentURL : '/' + img.AttachmentURL}`) :
                        '/images/placeholder.jpg',
                      alt: item.ProductName || 'Product image'
                    }));
                  }

                  // If no images were found, add a placeholder using the exact path from the API response
                  if (productImages.length === 0) {
                    productImages.push({
                      id: '0',
                      url: '/images/no-image.jpg', // Use the exact path from the API response
                      alt: item.ProductName || 'Product image',
                      isPrimary: true
                    });
                  }

                  return {
                    id: item.ProductId?.toString() || '',
                    name: item.ProductName || '',
                    slug: (item.ProductName || '').toLowerCase().replace(/\s+/g, '-'),
                    price: item.Price || 0,
                    discountedPrice: item.DiscountedPrice,
                    images: productImages,
                    rating: item.Rating || 0,
                    categoryName: item.CategoryName || '',
                    categorySlug: (item.CategoryName || '').toLowerCase().replace(/\s+/g, '-'),
                    isNew: item.MarkAsNew === true || item.IsNew === true || false,
                    inStock: (item.StockQuantity || 0) > 0
                  };
                });

                console.log('Transformed popular products:', transformedProducts);
                setProducts(transformedProducts);
                return; // Exit early if we successfully got data
              }
            } catch (parseError) {
              console.error('Error parsing products data:', parseError);
              // Continue to fallback data
            }
          }
        } catch (apiError) {
          console.error('API call failed:', apiError);
          // Continue to fallback data
        }

        // If we reach here, either the API call failed or returned invalid data
        console.log('No popular products data available from API');
        setProducts([]);

      } catch (error) {
        console.error('Error in fetchPopularProducts:', error);
        setProducts([]);

        if (error && typeof error === 'object' && 'message' in error) {
          setErrorMessage((error as Error).message);
        } else {
          setErrorMessage('An unexpected error occurred while fetching products');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchPopularProducts();
  }, [limit]);

  if (errorMessage) {
    return (
      <div className="w-full p-4 mb-4 text-center">
        <div className="p-4 rounded-md" style={{ backgroundColor: `${primaryColor}10`, border: `1px solid ${primaryColor}40` }}>
          <p style={{ color: destructiveColor }}>{errorMessage}</p>
          <button
            onClick={() => {
              setErrorMessage(null);
              setLoading(true);
              // Re-fetch products
              const fetchData = async () => {
                try {
                  const { MakeApiCallAsync } = await import('@/lib/api-helper');
                  const response = await MakeApiCallAsync(
                    'api/v1/products/get-all-products', // Exact endpoint from the screenshot
                    null,
                    {
                      requestParameters: {
                        SearchTerm: "",
                        CategoryID: null,
                        TagID: null,
                        ManufacturerID: null,
                        MinPrice: null,
                        MaxPrice: null,
                        Rating: null,
                        OrderByColumnName: "Price DESC", // Sort by price descending as shown in screenshot
                        PageNo: 1,
                        PageSize: limit,
                        recordValueJson: "[]"
                      }
                    },
                    {
                      'Content-Type': 'application/json',
                      'Accept': 'application/json'
                    },
                    "POST",
                    true
                  );
                  // Process response...
                  setLoading(false);
                } catch (error) {
                  console.error('Error retrying fetch:', error);
                  setLoading(false);
                  setErrorMessage('Failed to fetch products. Please try again later.');
                }
              };
              fetchData();
            }}
            className="mt-2 px-4 py-2 rounded-md text-sm transition-colors"
            style={{ 
              backgroundColor: `${destructiveColor}20`, 
              color: destructiveColor
            }}
            onMouseEnter={(e) => (e.target as HTMLButtonElement).style.backgroundColor = `${primaryColor}30`}
            onMouseLeave={(e) => (e.target as HTMLButtonElement).style.backgroundColor = `${primaryColor}20`}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="w-full">
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
          {Array.from({ length: limit }).map((_, index) => (
            <Card key={index} className="p-4">
              <Skeleton className="h-48 w-full rounded-md mb-4" />
              <Skeleton className="h-4 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="w-full text-center py-8">
        <p className="text-muted-foreground">No popular products found</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
        {products.map((product) => (
          <ProductBox
            key={product.id}
            product={product}
            effect={hoverEffect}
          />
        ))}
      </div>
    </div>
  );
}