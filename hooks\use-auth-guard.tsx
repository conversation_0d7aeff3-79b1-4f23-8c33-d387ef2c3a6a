'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useUser } from '@/contexts/user-context';

interface AuthGuardOptions {
  redirectTo?: string;
  requireAuth?: boolean;
  redirectIfAuthenticated?: boolean;
}

export function useAuthGuard(options: AuthGuardOptions = {}) {
  const {
    redirectTo = '/login',
    requireAuth = true,
    redirectIfAuthenticated = false
  } = options;

  const { isLoggedIn, isLoading } = useUser();
  const router = useRouter();
  const pathname = usePathname();

  console.log('🔧 useAuthGuard: Auth state check', { isLoggedIn, isLoading, requireAuth, redirectIfAuthenticated });

  // <PERSON>le redirects with useEffect to avoid render issues
  useEffect(() => {
    if (!isLoading) {
      if (requireAuth && !isLoggedIn) {
        console.log('🔒 useAuthGuard: Redirecting to login - auth required but not logged in');
        const redirectUrl = `${redirectTo}?redirect=${encodeURIComponent(pathname || '')}`;
        router.replace(redirectUrl);
      } else if (redirectIfAuthenticated && isLoggedIn) {
        console.log('🔓 useAuthGuard: Redirecting authenticated user');
        router.replace('/account');
      }
    }
  }, [isLoading, isLoggedIn, requireAuth, redirectIfAuthenticated, router, redirectTo, pathname]);

  // Calculate authorization state
  let isAuthorized: boolean | null = null;

  if (isLoading) {
    console.log('🔧 useAuthGuard: Still loading, isAuthorized = null');
    isAuthorized = null;
  } else if (requireAuth && !isLoggedIn) {
    console.log('🔒 useAuthGuard: Authentication required but user not logged in, isAuthorized = false');
    isAuthorized = false;
  } else if (redirectIfAuthenticated && isLoggedIn) {
    console.log('🔓 useAuthGuard: User authenticated but should be redirected, isAuthorized = false');
    isAuthorized = false;
  } else {
    console.log('✅ useAuthGuard: User is authorized, isAuthorized = true');
    isAuthorized = true;
  }

  console.log('🔧 useAuthGuard: Final result', { isAuthorized, isLoading, isLoggedIn });

  return {
    isAuthorized,
    isLoading,
    isLoggedIn
  };
}

// Higher-order component for protecting pages
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: AuthGuardOptions = {}
) {
  return function AuthGuardedComponent(props: P) {
    const { isAuthorized, isLoading } = useAuthGuard(options);

    // Show loading state while checking authentication
    if (isLoading || isAuthorized === null) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      );
    }

    // Don't render component if not authorized (redirect is handled by hook)
    if (!isAuthorized) {
      return null;
    }

    return <Component {...props} />;
  };
}

// Utility function to get redirect URL from current page
export function getRedirectUrl(): string {
  if (typeof window === 'undefined') return '/account';
  
  const urlParams = new URLSearchParams(window.location.search);
  const redirectUrl = urlParams.get('redirect');
  
  if (redirectUrl) {
    const decodedUrl = decodeURIComponent(redirectUrl);
    // Validate the redirect URL to prevent open redirects
    if (decodedUrl.startsWith('/') && !decodedUrl.startsWith('//')) {
      return decodedUrl;
    }
  }
  
  return '/account';
}

// Utility function to create login URL with redirect
export function createLoginUrl(currentPath: string): string {
  const encodedPath = encodeURIComponent(currentPath);
  return `/login?redirect=${encodedPath}`;
}

// Protected route component
export function ProtectedRoute({ 
  children, 
  fallback = null,
  ...options 
}: AuthGuardOptions & { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { isAuthorized, isLoading } = useAuthGuard(options);

  if (isLoading || isAuthorized === null) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthorized) {
    return null;
  }

  return <>{children}</>;
}
