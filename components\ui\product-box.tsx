'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Heart, Search, RefreshCw, ShoppingBag } from 'lucide-react';
import { Config } from '@/lib/config';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ProductRatingStars } from '@/components/ui/product-rating-stars';
import { useSettings } from '@/contexts/settings-context';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { toast } from 'sonner';
import { showModernAddToCartToast } from './modern-toast';
import { ProductStructuredData } from '@/components/seo/product-structured-data';

interface ProductImage {
  id: string;
  url: string;
  alt: string;
}

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  discountedPrice?: number;
  images: ProductImage[];
  rating: number;
  categoryName: string;
  categorySlug: string;
  isNew?: boolean;
  inStock?: boolean;
}

interface ProductBoxProps {
  product: Product;
  effect?: string;
  layout?: string;
}

export function ProductBox({ product, effect = '', layout = '' }: ProductBoxProps) {
  const { t, language } = useSettings();
  const cart = useCart();
  const wishlist = useWishlist();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const [currentImage, setCurrentImage] = useState(product.images?.[0] || { url: '', alt: product.name });
  const [addingToCart, setAddingToCart] = useState(false);
  const [addingToWishlist, setAddingToWishlist] = useState(false);

  const handleAddToCart = () => {
    if (!product.inStock) return;

    setAddingToCart(true);

    try {
      // Add to cart using the cart context
      cart.addToCart({
        id: parseInt(product.id),
        name: product.name,
        price: product.discountedPrice || product.price,
        discountPrice: product.discountedPrice,
        image: currentImage.url,
        originalPrice: product.price, // Always store the original price
      }, 1, [], undefined);

      // Show modern toast notification
      showModernAddToCartToast({
        productName: product.name,
        quantity: 1,
        productImage: product.images?.[0]?.url || '/placeholder.svg',
        onViewCart: () => {
          window.location.href = '/cart';
        }
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Failed to add product to cart');
    } finally {
      setAddingToCart(false);
    }
  };

  const discount = product.discountedPrice
    ? Math.round(((product.price - product.discountedPrice) / product.price) * 100)
    : 0;

  // Render list layout
  if (layout === 'list') {
    return (
      <>
        {/* Add structured data for this product */}
        <ProductStructuredData 
          product={{
            id: product.id,
            name: product.name,
            description: `${product.name} - Medical course/resource available at Code Medical`,
            image: currentImage?.url || '',
            images: product.images?.map(img => img.url) || [],
            price: product.price,
            discountPrice: product.discountedPrice,
            currency: 'USD',
            brand: 'Code Medical',
            sku: product.id,
            availability: product.inStock !== false,
            rating: product.rating,
            reviewCount: 89, // You can make this dynamic based on actual review data
            category: product.categoryName
          }}
          baseUrl={Config.ADMIN_BASE_URL || ''}
        />
        
        <Card className="group relative overflow-hidden transition-all hover:shadow-lg">
        <div className="flex flex-col md:flex-row">
          {/* Product image */}
          <div className="relative w-full md:w-1/3 aspect-square md:aspect-auto overflow-hidden">
            <Link href={`/product/${product.slug}`}>
              <Image
                src={
                  currentImage.url.startsWith('http')
                    ? currentImage.url
                    : currentImage.url.startsWith('/')
                      ? `${Config.ADMIN_BASE_URL}${currentImage.url.substring(1)}`
                      : `${Config.ADMIN_BASE_URL}${currentImage.url}`
                }
                alt={currentImage.alt}
                fill
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
                className="object-cover transition-transform group-hover:scale-105"
                onError={(e) => {
                  // If image fails to load, replace with a placeholder
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://placehold.co/400x400/cccccc/666666?text=Image+Not+Found';
                }}
              />
            </Link>

            {/* Product badges */}
            <div className="absolute left-2 top-2 z-10 space-y-1">
              {product.isNew && (
                <Badge variant="secondary" className="text-white text-xs sm:text-sm font-semibold px-2 py-1 shadow-md" style={{ backgroundColor: primaryColor }}>
                  NEW
                </Badge>
              )}
              {discount > 0 && (
                <Badge variant="destructive" className="text-xs sm:text-sm font-semibold px-2 py-1 shadow-md">
                  -{discount}%
                </Badge>
              )}
            </div>
          </div>

          {/* Product info */}
          <div className="p-4 md:p-6 flex-1 flex flex-col">
            <div className="mb-auto">
              <Link
                href={`/category/${product.categorySlug}`}
                className="text-sm text-muted-foreground hover:text-primary"
              >
                {product.categoryName}
              </Link>

              <Link
                href={`/product/${product.slug}`}
                className="mt-1 block text-base sm:text-lg font-medium hover:text-primary line-clamp-2"
              >
                {product.name}
              </Link>

              <div className="mt-2">
                <ProductRatingStars rating={product.rating} size="sm" />
              </div>

              <p className="mt-4 text-muted-foreground line-clamp-3">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
              </p>
            </div>

            <div className="mt-4 flex items-center justify-between">
              <div className="space-x-2">
                {product.discountedPrice ? (
                  <>
                    <span className="text-xl font-bold" style={{ color: '#ff0000' }}>
                  ${product.discountedPrice.toFixed(2)}
                </span>
                    <span className="text-sm text-muted-foreground line-through">
                      ${product.price.toFixed(2)}
                    </span>
                  </>
                ) : (
                  <span className="text-xl font-bold" style={{ color: '#16a34a' }}>
                    ${product.price.toFixed(2)}
                  </span>
                )}
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9"
                  onClick={() => {
                    setAddingToWishlist(true);
                    try {
                      if (wishlist.isInWishlist(parseInt(product.id))) {
                        wishlist.removeFromWishlist(parseInt(product.id));
                        toast.success(`${product.name} removed from wishlist`);
                      } else {
                        const productUrl = `/product/${product.slug}`;
                        const imageUrl = product.images?.[0]?.url || "/placeholder.svg";
                        const price = product.discountedPrice || product.price;
                        
                        wishlist.addToWishlist(
                          parseInt(product.id),
                          product.name,
                          productUrl,
                          imageUrl,
                          price
                        );
                        toast.success(`${product.name} added to wishlist`);
                      }
                    } catch (error) {
                      console.error('Error updating wishlist:', error);
                      toast.error('Failed to update wishlist');
                    } finally {
                      setAddingToWishlist(false);
                    }
                  }}
                >
                  <Heart
                    className="h-4 w-4 mr-2"
                    fill={wishlist.isInWishlist(parseInt(product.id)) ? "red" : "none"}
                    color={wishlist.isInWishlist(parseInt(product.id)) ? "red" : "currentColor"}
                  />
                  {addingToWishlist ? 'Updating...' : 'Wishlist'}
                </Button>

                <Button
                  variant="default"
                  size="sm"
                  className="h-9"
                  disabled={!product.inStock || addingToCart}
                  onClick={handleAddToCart}
                >
                  <ShoppingBag className="h-4 w-4 mr-2" />
                  {addingToCart ? 'Adding...' : 'Add to Cart'}
                </Button>
              </div>
            </div>

            {!product.inStock && (
              <p className="mt-2 text-sm text-destructive">Out of Stock</p>
            )}
          </div>
        </div>
      </Card>
      </>
    );
  }

  // Render grid layout (default)
  return (
    <>
      {/* Add structured data for this product */}
      <ProductStructuredData 
        product={{
          id: product.id,
          name: product.name,
          description: `${product.name} - Medical course/resource available at Code Medical`,
          image: currentImage?.url || '',
          images: product.images?.map(img => img.url) || [],
          price: product.price,
          discountPrice: product.discountedPrice,
          currency: 'USD',
          brand: 'Code Medical',
          sku: product.id,
          availability: product.inStock !== false,
          rating: product.rating,
          reviewCount: 89, // You can make this dynamic based on actual review data
          category: product.categoryName
        }}
        baseUrl={Config.ADMIN_BASE_URL || ''}
      />
      
      <Card className="group relative overflow-hidden transition-all hover:shadow-lg">
      {/* Product badges */}
      <div className="absolute left-2 top-2 z-10 space-y-1">
        {product.isNew && (
          <Badge variant="secondary" className="text-white text-xs sm:text-sm font-semibold px-2 py-1 shadow-md" style={{ backgroundColor: primaryColor }}>
            NEW
          </Badge>
        )}
        {discount > 0 && (
          <Badge variant="destructive" className="text-xs sm:text-sm font-semibold px-2 py-1 shadow-md">
            -{discount}%
          </Badge>
        )}
      </div>

      {/* Product image */}
      <div className="relative aspect-square overflow-hidden">
        <Link href={`/product/${product.slug}`}>
          <Image
            src={
              currentImage.url.startsWith('http')
                ? currentImage.url
                : currentImage.url.startsWith('/')
                  ? `${Config.ADMIN_BASE_URL}${currentImage.url.substring(1)}`
                  : `${Config.ADMIN_BASE_URL}${currentImage.url}`
            }
            alt={currentImage.alt}
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
            className="object-cover transition-transform group-hover:scale-105"
            onError={(e) => {
              // If image fails to load, replace with a placeholder
              const target = e.target as HTMLImageElement;
              target.src = 'https://placehold.co/400x400/cccccc/666666?text=Image+Not+Found';
            }}
          />
        </Link>

        {/* Quick action buttons */}
        <div className={`absolute right-2 top-2 z-10 flex flex-col gap-2 transition-all ${effect}`}>
          <Button
            variant="secondary"
            size="icon"
            className="h-8 w-8 bg-white/80 backdrop-blur-sm hover:bg-white"
            onClick={async () => {
              if (!product.inStock) return;
              setAddingToWishlist(true);
              try {
                if (wishlist.isInWishlist(parseInt(product.id))) {
                  wishlist.removeFromWishlist(parseInt(product.id));
                  toast.success('Removed from wishlist');
                } else {
                  const productUrl = `/product/${product.slug}`;
                  const imageUrl = product.images?.[0]?.url || "/placeholder.svg";
                  const price = product.discountedPrice || product.price;
                  
                  wishlist.addToWishlist(
                    parseInt(product.id),
                    product.name,
                    productUrl,
                    imageUrl,
                    price
                  );
                  toast.success('Added to wishlist');
                }
              } catch (error) {
                toast.error('Error updating wishlist');
              } finally {
                setAddingToWishlist(false);
              }
            }}
            disabled={addingToWishlist || !product.inStock}
          >
            <Heart className="h-4 w-4" fill={wishlist.isInWishlist(parseInt(product.id)) ? 'red' : 'none'} color={wishlist.isInWishlist(parseInt(product.id)) ? 'red' : 'currentColor'} />
          </Button>
          <Button
            variant="secondary"
            size="icon"
            className="h-8 w-8 bg-white/80 backdrop-blur-sm hover:bg-white"
            onClick={() => {}}
          >
            <Search className="h-4 w-4" />
          </Button>
          <Button
            variant="secondary"
            size="icon"
            className="h-8 w-8 bg-white/80 backdrop-blur-sm hover:bg-white"
            onClick={() => {}}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Product info */}
      <div className="p-4">
        <Link
          href={`/category/${product.categorySlug}`}
          className="text-sm text-muted-foreground hover:text-primary"
        >
          {product.categoryName}
        </Link>

        <Link
          href={`/product/${product.slug}`}
          className="mt-1 block text-xs sm:text-sm font-medium hover:text-primary line-clamp-2"
        >
          {product.name}
        </Link>

        <div className="mt-1">
          <ProductRatingStars rating={product.rating} size="sm" />
        </div>

        <div className="mt-2 flex items-center justify-between">
          <div className="space-x-2">
            {product.discountedPrice ? (
              <>
                <span className="text-lg font-bold" style={{ color: '#ff0000' }}>
                  ${product.discountedPrice.toFixed(2)}
                </span>
                <span className="text-sm text-muted-foreground line-through">
                  ${product.price.toFixed(2)}
                </span>
              </>
            ) : (
              <span className="text-lg font-bold" style={{ color: '#16a34a' }}>
                ${product.price.toFixed(2)}
              </span>
            )}
          </div>

          <Button
            variant="secondary"
            size="icon"
            className="h-8 w-8"
            disabled={!product.inStock || addingToCart}
            onClick={handleAddToCart}
          >
            {addingToCart ? <RefreshCw className="h-4 w-4 animate-spin" /> : <ShoppingBag className="h-4 w-4" />}
          </Button>
        </div>

        {!product.inStock && (
          <p className="mt-2 text-sm text-destructive">Out of Stock</p>
        )}
      </div>
    </Card>
    </>
  );
}