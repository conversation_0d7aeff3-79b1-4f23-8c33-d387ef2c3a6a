'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { fetchWebsiteLogo } from '@/lib/api-helper';

interface LogoContextType {
  logoUrl: string;
  isLoading: boolean;
  refreshLogo: () => Promise<void>;
}

const LogoContext = createContext<LogoContextType | undefined>(undefined);

export function LogoProvider({ children }: { children: React.ReactNode }) {
  const [logoUrl, setLogoUrl] = useState<string>('/default-logo.png');
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const loadLogo = async () => {
    setIsLoading(true);
    try {
      const fetchedLogoUrl = await fetchWebsiteLogo();
      setLogoUrl(fetchedLogoUrl);
    } catch (error) {
      console.error('Failed to load logo:', error);
      // Keep default logo
    } finally {
      setIsLoading(false);
    }
  };

  const refreshLogo = async () => {
    await loadLogo();
  };

  useEffect(() => {
    loadLogo();
  }, []);

  return (
    <LogoContext.Provider
      value={{
        logoUrl,
        isLoading,
        refreshLogo
      }}
    >
      {children}
    </LogoContext.Provider>
  );
}

export function useLogo() {
  const context = useContext(LogoContext);
  if (context === undefined) {
    throw new Error('useLogo must be used within a LogoProvider');
  }
  return context;
}