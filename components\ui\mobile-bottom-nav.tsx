"use client";

import {
  Home,
  Search,
  Package,
  User,
  ShoppingCart,
  Heart,
  Grid3X3,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useCart } from "@/contexts/cart-context";
import { useWishlist } from "@/contexts/wishlist-context";
import { useSettings } from "@/contexts/settings-context";
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { useUser } from "@/contexts/user-context";
import { useState, useEffect } from "react";

import { MakeApiCallAsync, Config, GetTokenForHeader } from "@/lib/api-helper";

interface Category {
  CategoryID: string | number;
  ParentCategoryID: string | number | null;
  Name: string;
  AttachmentURL?: string;
  // Add other category properties as needed
}

export function MobileBottomNav() {
  const [mounted, setMounted] = useState(false);
  const [showCategories, setShowCategories] = useState(false);
  const [allCategories, setAllCategories] = useState<Category[]>([]);
  const [parentCategories, setParentCategories] = useState<Category[]>([]);
  const [expandedParents, setExpandedParents] = useState<
    Record<string | number, boolean>
  >({});
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);

  // Initialize router and other hooks after component mounts
  const router = useRouter();
  const pathname = usePathname();
  const { totalItems: cartCount } = useCart();
  const { totalItems: wishlistCount } = useWishlist();
  const { user, isLoggedIn } = useUser();
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;

  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch categories from API
  const fetchCategories = async () => {
    if (parentCategories.length > 0) {
      // Reset states when showing categories modal
      setSearchQuery("");
      setExpandedParents({});
      setShowCategories(true);
      return;
    }

    setIsLoadingCategories(true);
    try {
      const param = {
        PageNumber: 1,
        PageSize: 100,
        SortColumn: "Name",
        SortOrder: "ASC",
      };
      const headers = {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: "Bearer " + (await GetTokenForHeader() || ""),
      };
      const categoriesResponse = await MakeApiCallAsync(
        Config.END_POINT_NAMES.GET_CATEGORIES_LIST,
        null,
        param,
        headers,
        "POST",
        true
      );

      if (categoriesResponse?.data?.data) {
        try {
          const parsedData = JSON.parse(categoriesResponse.data.data);
          if (Array.isArray(parsedData)) {
            const allCategories = parsedData;
            const parentCategories = allCategories
              .filter((cat) => !cat.ParentCategoryID)
              .sort((a, b) => a.Name.localeCompare(b.Name));

            setParentCategories(parentCategories);
            setAllCategories(allCategories);
            setShowCategories(true);
          }
        } catch (parseError) {
          console.error("Error parsing categories data:", parseError);
        }
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    } finally {
      setIsLoadingCategories(false);
    }
  };

  // Handle category click
  const handleCategoryClick = (category: Category) => {
    const childCategories = allCategories.filter(
      (cat) => cat.ParentCategoryID === category.CategoryID
    );
    if (childCategories.length > 0) {
      setExpandedParents((prev) => ({
        ...prev,
        [category.CategoryID]: !prev[category.CategoryID],
      }));
    } else {
      // Check if we're already on the products page
      const isOnProductsPage = pathname === '/products' || pathname?.startsWith('/products');
      
      if (isOnProductsPage) {
        // If on products page, force reload with new category
        window.location.href = `/products/?category=${category.CategoryID}`;
      } else {
        // If not on products page, navigate normally
        router.push(`/products/?category=${category.CategoryID}`);
      }
      setShowCategories(false);
    }
  };

  // Handle child category click - navigate to parent category instead
  const handleChildCategoryClick = (
    childCategory: Category,
    parentCategory: Category
  ) => {
    // Check if we're already on the products page
    const isOnProductsPage = pathname === '/products' || pathname?.startsWith('/products');
    
    if (isOnProductsPage) {
      // If on products page, force reload with new category
      window.location.href = `/products/?category=${childCategory.CategoryID}`;
    } else {
      // If not on products page, navigate normally
      router.push(`/products/?category=${childCategory.CategoryID}`);
    }
    setShowCategories(false);
  };

  if (!mounted) return null;

  const navItems = [
    {
      href: "/",
      icon: Home,
      label: t("home") || "Home",
      isActive: pathname === "/",
      onClick: null,
    },
    {
      href: "#",
      icon: Grid3X3,
      label: t("categories") || "Categories",
      isActive: false,
      onClick: fetchCategories,
    },
    {
      href: "/cart",
      icon: ShoppingCart,
      label: t("cart") || "Cart",
      isActive: pathname === "/cart",
      badge: cartCount || 0,
      onClick: null,
    },
    {
      href: "/wishlist",
      icon: Heart,
      label: t("wishlist") || "Wishlist",
      isActive: pathname === "/wishlist",
      badge: wishlistCount || 0,
      onClick: null,
    },
    {
      href: isLoggedIn ? "/account" : "/login",
      icon: User,
      label: isLoggedIn
        ? user?.FirstName || t("myAccount") || "My Account"
        : t("login") || "Login",
      isActive:
        pathname === "/login" ||
        pathname === "/signup" ||
        pathname === "/profile",
      onClick: null,
    },
  ];

  const renderCategories = () => {
    if (isLoadingCategories) {
      return (
        <div className="p-8 text-center">
          <div className="relative">
            <div
              className="animate-spin rounded-full h-12 w-12 border-4 border-t-transparent mx-auto mb-4"
              style={{
                borderColor: `${primaryColor}30`,
                borderTopColor: primaryColor,
              }}
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div
                className="w-3 h-3 rounded-full animate-pulse"
                style={{ backgroundColor: primaryColor }}
              />
            </div>
          </div>
          <p className="text-gray-600 font-medium animate-pulse">Loading...</p>
        </div>
      );
    }

    // Filter and sort categories based on search query
    const filteredParentCategories =
      searchQuery.trim() === ""
        ? parentCategories.sort((a, b) => a.Name.localeCompare(b.Name))
        : parentCategories
            .filter(
              (cat) =>
                cat.Name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                allCategories.some(
                  (child) =>
                    child.ParentCategoryID === cat.CategoryID &&
                    child.Name.toLowerCase().includes(searchQuery.toLowerCase())
                )
            )
            .sort((a, b) => a.Name.localeCompare(b.Name));

    return (
      <div className="h-full flex flex-col bg-gradient-to-b from-gray-50 to-white">
        {/* Enhanced Search Bar */}
        <div className="p-4 bg-white shadow-sm">
          <div className="relative group">
            <input
              type="text"
              placeholder="Search for category..."
              className="w-full p-3 pr-12 border-2 rounded-xl focus:outline-none focus:ring-0 transition-all duration-300 bg-gray-50 focus:bg-white focus:shadow-lg"
              style={{
                borderColor: searchQuery ? primaryColor : "#e5e7eb",
                paddingRight: "3rem",
              }}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Search
                className="h-5 w-5 transition-colors duration-300"
                style={{ color: searchQuery ? primaryColor : "#9ca3af" }}
              />
            </div>
            {searchQuery && (
              <button
                onClick={() => setSearchQuery("")}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                ✕
              </button>
            )}
          </div>
        </div>

        {/* Enhanced Categories List */}
        <div className="flex-1 overflow-y-auto px-2">
          {filteredParentCategories.length === 0 ? (
            <div className="p-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                <Search className="h-8 w-8 text-gray-400" />
              </div>
              <p className="text-gray-500 font-medium">
                No matching categories found
              </p>
              <p className="text-gray-400 text-sm mt-1">
                Try searching with different keywords
              </p>
            </div>
          ) : (
            <div className="space-y-2 py-2">
              {filteredParentCategories.map((category, index) => {
                const hasChildren = allCategories.some(
                  (cat) => cat.ParentCategoryID === category.CategoryID
                );
                const isExpanded = expandedParents[category.CategoryID];
                const childCategories = hasChildren
                  ? allCategories
                      .filter(
                        (cat) => cat.ParentCategoryID === category.CategoryID
                      )
                      .sort((a, b) => a.Name.localeCompare(b.Name))
                  : [];

                // Filter and sort child categories based on search query
                const filteredChildCategories =
                  searchQuery.trim() === ""
                    ? childCategories
                    : childCategories
                        .filter((child) =>
                          child.Name.toLowerCase().includes(
                            searchQuery.toLowerCase()
                          )
                        )
                        .sort((a, b) => a.Name.localeCompare(b.Name));

                return (
                  <div
                    key={category.CategoryID}
                    className="group animate-in slide-in-from-right duration-300"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    {/* Parent Category Card */}
                    <div className="relative overflow-hidden rounded-xl bg-white shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300 hover:scale-[1.02] hover:border-gray-200">
                      <button
                        onClick={() => handleCategoryClick(category)}
                        className="w-full text-right p-4 flex justify-between items-center relative z-10"
                      >
                        {/* Gradient Background on Hover */}
                        <div
                          className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300"
                          style={{
                            background: `linear-gradient(135deg, ${primaryColor}, ${primaryColor}80)`,
                          }}
                        />

                        <div className="flex items-center gap-3">
                          {/* Category Icon */}
                          <div
                            className="w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300 group-hover:scale-110"
                            style={{ backgroundColor: `${primaryColor}15` }}
                          >
                            <Package
                              className="h-5 w-5 transition-colors duration-300"
                              style={{ color: primaryColor }}
                            />
                          </div>

                          {/* Category Name */}
                          <div className="text-right">
                            <span className="font-semibold text-gray-800 group-hover:text-gray-900 transition-colors duration-300">
                              {category.Name}
                            </span>
                            {hasChildren && (
                              <p className="text-xs text-gray-500 mt-1">
                                {childCategories.length} subcategories
                              </p>
                            )}
                          </div>
                        </div>

                        {/* Expand/Collapse Icon */}
                        {hasChildren && (
                          <div className="flex items-center">
                            <div
                              className="w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110"
                              style={{ backgroundColor: `${primaryColor}10` }}
                            >
                              {isExpanded ? (
                                <ChevronUp
                                  className="h-4 w-4 transition-all duration-300"
                                  style={{ color: primaryColor }}
                                />
                              ) : (
                                <ChevronDown
                                  className="h-4 w-4 transition-all duration-300"
                                  style={{ color: primaryColor }}
                                />
                              )}
                            </div>
                          </div>
                        )}
                      </button>
                    </div>

                    {/* Subcategories with Animation */}
                    {hasChildren && (
                      <div
                        className={`overflow-hidden transition-all duration-500 ease-in-out ${
                          isExpanded
                            ? "max-h-80 opacity-100 mt-2"
                            : "max-h-0 opacity-0"
                        }`}
                      >
                        <div className="bg-gradient-to-r from-gray-50 to-white rounded-xl p-3 border border-gray-100">
                          {/* Subcategories Grid with Scroll */}
                          <div className="max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                            <div className="grid grid-cols-2 gap-2 mb-3 pr-2">
                              {filteredChildCategories.map(
                                (child, childIndex) => (
                                  <button
                                    key={child.CategoryID}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleChildCategoryClick(child, category);
                                    }}
                                    className="group/child p-2 text-xs text-right rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-md border border-transparent hover:border-gray-200 bg-white hover:bg-gradient-to-r"
                                    style={
                                      {
                                        animationDelay: `${childIndex * 100}ms`,
                                        "--tw-gradient-from": `${primaryColor}05`,
                                        "--tw-gradient-to": `${primaryColor}10`,
                                      } as React.CSSProperties
                                    }
                                  >
                                    <div className="flex items-center justify-between">
                                      <span
                                        className="font-medium transition-colors duration-300 group-hover/child:font-semibold text-xs leading-tight"
                                        style={{ color: primaryColor }}
                                      >
                                        {child.Name}
                                      </span>
                                      <div
                                        className="w-1.5 h-1.5 rounded-full transition-all duration-300 group-hover/child:scale-150 flex-shrink-0"
                                        style={{
                                          backgroundColor: `${primaryColor}40`,
                                        }}
                                      />
                                    </div>
                                  </button>
                                )
                              )}
                            </div>
                          </div>

                          {/* View All Button */}
                          <button
                            onClick={() => {
                              // Don't navigate if already on products page
                              if (pathname && pathname.startsWith('/products')) {
                                setShowCategories(false);
                                return;
                              }
                              router.push(
                                `/products?category=${category.CategoryID}`
                              );
                              setShowCategories(false);
                            }}
                            className="w-full py-3 text-center text-sm font-semibold rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg relative overflow-hidden group/all"
                            style={{
                              backgroundColor: primaryColor,
                              color: "white",
                            }}
                          >
                            {/* Shimmer Effect */}
                            <div className="absolute inset-0 -translate-x-full group-hover/all:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 to-transparent" />

                            <span className="relative z-10 flex items-center justify-center gap-2">
                              View All in {category.Name}
                              <ChevronDown className="h-4 w-4 rotate-[-90deg] transition-transform duration-300 group-hover/all:translate-x-1" />
                            </span>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="mobile-bottom-nav lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb">
        <div className="flex items-center justify-center py-2 px-4">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isButton = !!item.onClick;
            const className =
              "mobile-nav-item flex flex-col items-center justify-center min-w-0 py-2 px-3 mx-1" +
              (isButton ? " bg-transparent border-none" : "");

            const content = (
              <>
                <div className="relative">
                  <Icon
                    className="mobile-nav-icon h-6 w-6 mb-1"
                    style={{
                      color: item.isActive ? primaryColor : "#6B7280",
                    }}
                  />
                  {item.badge !== undefined && item.badge > 0 && (
                    <span
                      className="mobile-nav-badge absolute -top-1 -right-1 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium shadow-md"
                      style={{ backgroundColor: primaryColor }}
                    >
                      {item.badge > 99 ? "99+" : item.badge}
                    </span>
                  )}
                </div>
                <span
                  className="mobile-nav-text text-xs font-medium text-center leading-tight mt-1"
                  style={{
                    color: item.isActive ? primaryColor : "#6B7280",
                  }}
                >
                  {item.label}
                </span>
              </>
            );

            return isButton ? (
              <button
                key={item.href}
                onClick={item.onClick || undefined}
                className={className}
                type="button"
              >
                {content}
              </button>
            ) : (
              <Link key={item.href} href={item.href} className={className}>
                {content}
              </Link>
            );
          })}
        </div>
      </div>

      {/* Enhanced Categories Modal */}
      {showCategories && (
        <div className="md:hidden fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-end animate-in fade-in duration-300">
          <div className="bg-white w-full max-h-[75vh] rounded-t-2xl overflow-hidden shadow-2xl animate-in slide-in-from-bottom duration-500">
            {/* Enhanced Header */}
            <div className="relative overflow-hidden">
              {/* Gradient Background */}
              <div
                className="absolute inset-0 opacity-10"
                style={{
                  background: `linear-gradient(135deg, ${primaryColor}, ${primaryColor}80)`,
                }}
              />

              <div className="relative flex items-center justify-between p-5 border-b border-gray-100">
                <div className="flex items-center gap-3">
                  {/* Animated Icon */}
                  <div
                    className="w-10 h-10 rounded-xl flex items-center justify-center animate-pulse"
                    style={{ backgroundColor: `${primaryColor}20` }}
                  >
                    <Grid3X3
                      className="h-5 w-5"
                      style={{ color: primaryColor }}
                    />
                  </div>

                  <div>
                    <h2
                      className="text-xl font-bold"
                      style={{ color: primaryColor }}
                    >
                      {t("categories") || "Categories"}
                    </h2>
                    <p className="text-xs text-gray-500 mt-1">
                      Choose the right category for you
                    </p>
                  </div>
                </div>

                {/* Enhanced Close Button */}
                <button
                  onClick={() => setShowCategories(false)}
                  className="w-10 h-10 rounded-xl bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-all duration-300 hover:scale-110 hover:rotate-90 group"
                >
                  <span className="text-gray-500 group-hover:text-gray-700 font-bold text-lg">
                    ✕
                  </span>
                </button>
              </div>

              {/* Decorative Line */}
              <div
                className="h-1 w-full"
                style={{
                  background: `linear-gradient(90deg, transparent, ${primaryColor}, transparent)`,
                }}
              />
            </div>

            {/* Categories Content */}
            <div className="overflow-y-auto max-h-[calc(75vh-120px)] scrollbar-hide">
              {renderCategories()}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
