import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';
import { useColorThemeContext } from '@/contexts/color-theme-context';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: "text-white",
        destructive:
          'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline:
          "border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",
        secondary:
          'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: "bg-transparent hover:bg-accent hover:text-accent-foreground",
        link: 'underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, style, ...props }, ref) => {
    const { currentTheme } = useColorThemeContext();
    const primaryColor = currentTheme.primary;
    const Comp = asChild ? Slot : 'button';
    
    const getVariantStyle = () => {
      if (variant === 'default') {
        return {
          backgroundColor: primaryColor,
          ...style
        };
      }
      if (variant === 'link') {
        return {
          color: primaryColor,
          ...style
        };
      }
      return style;
    };
    
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        style={getVariantStyle()}
        onMouseEnter={(e) => {
          if (variant === 'default') {
            e.currentTarget.style.backgroundColor = `${primaryColor}E6`;
          }
        }}
        onMouseLeave={(e) => {
          if (variant === 'default') {
            e.currentTarget.style.backgroundColor = primaryColor;
          }
        }}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
