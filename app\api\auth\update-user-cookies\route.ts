import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import CryptoJS from 'crypto-js';

const SECRET_KEY = process.env.ENCRYPTION_KEY || process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'change-me';

function decryptJson(encrypted: string) {
  try {
    const bytes = CryptoJS.AES.decrypt(encrypted, SECRET_KEY);
    const plaintext = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(plaintext);
  } catch {
    try { return JSON.parse(encrypted); } catch { return null; }
  }
}

function encryptJson(data: any) {
  const plaintext = JSON.stringify(data);
  return CryptoJS.AES.encrypt(plaintext, SECRET_KEY).toString();
}

function pickPublicUser(user: any) {
  if (!user) return null;
  return {
    UserId: user.UserId || user.UserID,
    UserName: user.UserName || user.Username || user.Name,
    Email: user.Email || user.EmailAddress,
    FirstName: user.FirstName,
    LastName: user.LastName,
    PhoneNumber: user.PhoneNumber || user.PhoneNo || user.MobileNo,
    AvatarUrl: user.AvatarUrl || user.ImageUrl || user.ProfileImageUrl || null,
    UserTypeID: user.UserTypeID || user.UserTypeId || user.UserType || null,
    // Improved handling of category/specialist fields
    Gender: user.Gender,
    CategoryID: user.CategoryID || user.CategoryId || user.CatID || null,
    CategoryId: user.CategoryId || user.CategoryID || user.CatID || null,
    SpecialistId: user.SpecialistId || null,
    CatID: user.CatID || user.CategoryId || user.CategoryID || null,
  };
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { FirstName, LastName, UserName, ...otherData } = body;

    // Get current user data from cookies
    const cookieStore = await cookies();
    const userCookie = cookieStore.get('auth_user');
    
    if (!userCookie) {
      return NextResponse.json(
        { success: false, error: 'No user session found' },
        { status: 401 }
      );
    }

    let currentUser;
    try {
      currentUser = decryptJson(userCookie.value);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Invalid user session' },
        { status: 401 }
      );
    }

    // Update user data with new information
    const updatedUser = {
      ...currentUser,
      FirstName: FirstName || currentUser.FirstName,
      LastName: LastName || currentUser.LastName,
      UserName: UserName || currentUser.UserName,
      ...otherData
    };

    // Set updated user cookie
    const response = NextResponse.json({ 
      success: true, 
      message: 'User cookies updated successfully',
      user: updatedUser
    });

    // Fix domain configuration for Vercel and Amplify
    let domain = undefined;
    if (process.env.NODE_ENV === 'production') {
      domain = process.env.NEXT_PUBLIC_DOMAIN || 
               process.env.VERCEL_URL ||
               request.headers.get('host')?.replace('www.', '');
      
      if (domain && !domain.startsWith('.')) {
        domain = `.${domain}`;
      }
    }

    // HttpOnly encrypted full profile
    response.cookies.set('auth_user', encryptJson(updatedUser), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: '/',
      domain,
    });

    // Update public cookie too
    response.cookies.set('auth_user_public', JSON.stringify(pickPublicUser(updatedUser)), {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax', // Changed from 'strict'
      path: '/',
      maxAge: 60 * 60 * 24 * 7,
      domain,
    });

    return response;

  } catch (error) {
    console.error('Error updating user cookies:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update user cookies' },
      { status: 500 }
    );
  }
}
