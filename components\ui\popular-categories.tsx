'use client';

import { useEffect, useState, useMemo } from 'react';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import useEmblaCarousel from 'embla-carousel-react';
import Autoplay from 'embla-carousel-autoplay';
import { useMediaQuery } from '@/hooks/use-media-query';

interface Category {
  id: number;
  title: string;
  image: string;
  parentId?: number;
  parentName?: string;
}

interface CarouselOptions {
  loop?: boolean;
  align?: 'start' | 'center' | 'end';
  slidesToScroll?: number;
  containScroll?: 'trimSnaps' | 'keepSnaps';
  dragFree?: boolean;
  skipSnaps?: boolean;
  inViewThreshold?: number;
  speed?: number;
}

export default function PopularCategories() {
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const t = (key: string) => {
    const translations: Record<string, string> = {
      popularCategories: 'Popular Categories',
    };
    return translations[key] || key;
  };

  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [categoriesInPages, setCategoriesInPages] = useState<Category[][]>([]);
  
  // Media queries for responsive behavior
  const isDesktop = useMediaQuery('(min-width: 1330px)');
  const isTabletLandscape = useMediaQuery('(min-width: 768px) and (max-width: 1329px)');
  const isTablet = useMediaQuery('(min-width: 768px) and (max-width: 1023px)');
  const isMobile = useMediaQuery('(max-width: 767px)');
  
  // Autoplay plugin with 4 seconds delay and stop on interaction
  const autoplayOptions = {
    delay: 4000,
    stopOnInteraction: false, // Keep autoplay running even after user interaction
    stopOnMouseEnter: true, // Pause on hover
    rootNode: (emblaRoot: any) => emblaRoot.parentElement,
  };
  
  // Configure carousel options based on device type
  const getCarouselOptions = (): CarouselOptions => {
    if (isDesktop) {
      return {
        loop: true,
        align: 'start',
        slidesToScroll: 1,
        speed: 20, // Slower animation for desktop
      };
    } else if (isTabletLandscape) {
      return {
        loop: true,
        align: 'start',
        slidesToScroll: 1,
        dragFree: false, // Better control for tablet landscape
        skipSnaps: false, // Ensure proper snapping for 4x2 grid
        speed: 15, // Medium speed for tablet landscape
        containScroll: 'trimSnaps', // Better handling of partial slides
      };
    } else if (isTablet) {
      return {
        loop: true,
        align: 'start',
        slidesToScroll: 1,
        dragFree: true,
        skipSnaps: false, // Better snapping for tablet
        speed: 15, // Medium speed for tablet
      };
    } else {
      return {
        loop: true,
        align: 'start',
        slidesToScroll: 1,
        dragFree: true,
        skipSnaps: true,
        speed: 10, // Faster for mobile for immediate response
      };
    }
  };

  const getItemsPerRow = () => {
    if (typeof window !== 'undefined') {
      const width = window.innerWidth;
      if (width >= 1330) { // xl - desktop
        return 6;
      }
      if (width >= 1024 && width <= 1329) { // lg - large tablet landscape
        return 4; // Force 4 items for consistency in tablet landscape
      }
      if (width >= 768 && width <= 1023) { // md - tablet landscape (4x2 grid)
        return 4; // 4x2 grid for tablet landscape
      }
      return 3; // mobile
    }
    return 4; // Default for SSR
  };

  const [itemsPerRow, setItemsPerRow] = useState(5);
  
  useEffect(() => {
    setItemsPerRow(getItemsPerRow());
    const handleResize = () => {
      setItemsPerRow(getItemsPerRow());
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const totalItems = itemsPerRow * 2;

  const constructImageUrl = (attachmentUrl: string | null) => {
    if (!attachmentUrl) return `${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}images/no-image.jpg`;

    if (attachmentUrl.startsWith('http')) {
      return attachmentUrl;
    }

    const baseUrl = process.env.NEXT_PUBLIC_ADMIN_BASE_URL || '';

    let normalizedPath = attachmentUrl.startsWith('/') ? attachmentUrl : `/${attachmentUrl}`;
    normalizedPath = normalizedPath.replace(/\/+/g, '/');

    return `${baseUrl}${normalizedPath}`;
  };

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const axios = (await import('axios')).default;
        const param = {
          requestParameters: {
            recordValueJson: '[]',
          },
        };
        const config = {
          method: 'post',
          url: `${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-popular-categories`,
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
          data: param,
        };
        const response = await axios(config);
        if (response?.data?.data) {
          try {
            const parsedData = JSON.parse(response.data.data);
            if (Array.isArray(parsedData)) {
              const popularCategories = parsedData
                .map((item) => ({
                  id: item.CategoryID,
                  title: item.Name,
                  image: constructImageUrl(item.AttachmentURL),
                  parentId: item.ParentCategoryID || undefined,
                  parentName: item.ParentCategoryName || undefined,
                }))
                .sort((a, b) => a.title.localeCompare(b.title));
              setCategories(popularCategories);
            } else {
              console.error('Categories data is not an array:', parsedData);
              setCategories([]);
            }
          } catch (parseError) {
            console.error('Error parsing data:', parseError);
            setCategories([]);
          }
        } else {
          console.error('No data returned from API');
          setCategories([]);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  useEffect(() => {
    const updatePages = () => {
      const pages = [];
      if (categories.length > 0) {
        for (let i = 0; i < categories.length; i += totalItems) {
          pages.push(categories.slice(i, i + totalItems));
        }
      }
      setCategoriesInPages(pages);
    };

    updatePages();

    window.addEventListener('resize', updatePages);
    return () => window.removeEventListener('resize', updatePages);
  }, [categories, itemsPerRow, totalItems]);

  if (loading) {
    return (
      <section className="py-6 md:py-8">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <h2 className="text-xl md:text-2xl font-bold mb-4 md:mb-6">{t('popularCategories')}</h2>
          <div className="w-full flex justify-center items-center py-12">
            <div className="animate-pulse text-lg">Loading popular categories...</div>
          </div>
        </div>
      </section>
    );
  }

  if (!categories.length) {
    return (
      <section className="py-6 md:py-8">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <h2 className="text-xl md:text-2xl font-bold mb-4 md:mb-6">{t('popularCategories')}</h2>
          <div className="w-full flex justify-center items-center py-12">
            <div className="text-lg text-gray-500">No categories available at the moment. Please check back later.</div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-6 md:py-8 popular-categories-section">
      <div className="container mx-auto px-4 md:px-6 lg:px-8 popular-categories-container">
        <div className="mb-4 md:mb-6">
          <h2 className="text-xl md:text-2xl font-bold text-center popular-categories-title">{t('popularCategories')}</h2>
        </div>
        
        <div className="relative">
          <Carousel
            opts={getCarouselOptions()}
            plugins={[Autoplay(autoplayOptions)]}
            className="w-full popular-categories-carousel"
          >
            <CarouselContent>
              {categoriesInPages.map((page, pageIndex) => (
                <CarouselItem key={pageIndex}>
                  <div className="grid grid-cols-1 gap-4">
                    {/* First row */}
                    <div className={`grid ${
                      isTabletLandscape
                        ? 'grid-cols-4 gap-6'
                        : 'grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-2 sm:gap-3 md:gap-4 lg:gap-4 xl:gap-6'
                    }`}>
                      {page.slice(0, itemsPerRow).map((category) => (
                        <div key={category.id} className={`flex flex-col items-center popular-categories-grid-item ${
                          isTabletLandscape
                            ? 'px-3 py-4'
                            : 'px-1 sm:px-2 md:px-3 py-2 md:py-3'
                        }`}>
                          <a href={`/products?category=${category.id}`} className="group flex flex-col items-center cursor-pointer transition-all duration-300 hover:scale-105">
                            <div className={`mb-2 md:mb-3 relative popular-categories-image-container ${
                              isTabletLandscape
                                ? 'w-24 h-24'
                                : 'w-16 h-16 sm:w-20 sm:h-20 md:w-20 md:h-20 lg:w-24 lg:h-24 xl:w-28 xl:h-28'
                            }`}>
                              <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full p-1 group-hover:shadow-lg transition-all duration-300">
                                <div className="w-full h-full rounded-full overflow-hidden border-2 border-white bg-white">
                                  <img
                                    src={category.image || '/placeholder.svg?height=150&width=150'}
                                    alt={category.title}
                                    width={144}
                                    height={144}
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      if (target.src !== '/placeholder.svg?height=150&width=150') {
                                        target.src = '/placeholder.svg?height=150&width=150';
                                      }
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                            <div className="text-center">
                              <h3 className={`font-medium text-gray-800 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2 popular-categories-title-text ${
                                isTabletLandscape
                                  ? 'text-base'
                                  : 'text-xs sm:text-sm md:text-sm lg:text-base'
                              }`}>
                                {category.title}
                              </h3>
                              {category.parentName && <p className="text-xs text-gray-500 mt-1 popular-categories-parent-text">{category.parentName}</p>}
                            </div>
                          </a>
                        </div>
                      ))}
                    </div>
                    {/* Second row */}
                    <div className={`grid ${
                      isTabletLandscape
                        ? 'grid-cols-4 gap-6'
                        : 'grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-2 sm:gap-3 md:gap-4 lg:gap-4 xl:gap-6'
                    }`}>
                      {page.slice(itemsPerRow, totalItems).map((category) => (
                        <div key={category.id} className={`flex flex-col items-center popular-categories-grid-item ${
                          isTabletLandscape
                            ? 'px-3 py-4'
                            : 'px-1 sm:px-2 md:px-3 py-2 md:py-3'
                        }`}>
                          <a href={`/products?category=${category.id}`} className="group flex flex-col items-center cursor-pointer transition-all duration-300 hover:scale-105">
                            <div className={`mb-2 md:mb-3 relative popular-categories-image-container ${
                              isTabletLandscape
                                ? 'w-24 h-24'
                                : 'w-16 h-16 sm:w-20 sm:h-20 md:w-20 md:h-20 lg:w-24 lg:h-24 xl:w-28 xl:h-28'
                            }`}>
                              <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full p-1 group-hover:shadow-lg transition-all duration-300">
                                <div className="w-full h-full rounded-full overflow-hidden border-2 border-white bg-white">
                                  <img
                                    src={category.image || '/placeholder.svg?height=150&width=150'}
                                    alt={category.title}
                                    width={144}
                                    height={144}
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement;
                                      if (target.src !== '/placeholder.svg?height=150&width=150') {
                                        target.src = '/placeholder.svg?height=150&width=150';
                                      }
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                            <div className="text-center">
                              <h3 className={`font-medium text-gray-800 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2 popular-categories-title-text ${
                                isTabletLandscape
                                  ? 'text-base'
                                  : 'text-xs sm:text-sm md:text-sm lg:text-base'
                              }`}>
                                {category.title}
                              </h3>
                              {category.parentName && <p className="text-xs text-gray-500 mt-1 popular-categories-parent-text">{category.parentName}</p>}
                            </div>
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            
            {/* Navigation arrows for desktop and tablet */}
            {(isDesktop || isTabletLandscape || isTablet) && (
              <>
                <CarouselPrevious className="-left-2 md:-left-4 lg:-left-6 bg-white/80 hover:bg-white border border-gray-200 shadow-md carousel-previous" />
                <CarouselNext className="-right-2 md:-right-4 lg:-right-6 bg-white/80 hover:bg-white border border-gray-200 shadow-md carousel-next" />
              </>
            )}
          </Carousel>
        </div>
      </div>
    </section>
  );

}
