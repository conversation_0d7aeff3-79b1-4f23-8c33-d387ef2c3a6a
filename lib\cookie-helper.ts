'use client';

// <PERSON>ie utility functions for secure authentication
export class <PERSON><PERSON>Hel<PERSON> {
  private static readonly COOKIE_OPTIONS = {
    secure: process.env.NODE_ENV === 'production', // Only use secure cookies in production
    httpOnly: false, // Client-side accessible cookies for React context
    sameSite: 'strict' as const,
    path: '/',
    maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
  };

  // HttpOnly cookie options for sensitive data (tokens)
  private static readonly HTTPONLY_COOKIE_OPTIONS = {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true, // Server-side only for security
    sameSite: 'strict' as const,
    path: '/',
    maxAge: 60 * 60, // 1 hour for JWT tokens (matches JWT expiration)
  };

  private static readonly USER_COOKIE = 'auth_user';
  private static readonly TOKEN_COOKIE = 'auth_token';
  private static readonly LOGIN_STATUS_COOKIE = 'is_logged_in';
  private static readonly POINT_COOKIE = 'user_points';

  /**
   * Set a secure cookie
   */
  private static setCookie(name: string, value: string, options: any = {}): void {
    if (typeof document === 'undefined') return; // Server-side check

    const cookieOptions = { ...this.COOKIE_OPTIONS, ...options };
    let cookieString = `${name}=${encodeURIComponent(value)}`;
    
    if (cookieOptions.maxAge) {
      cookieString += `; Max-Age=${cookieOptions.maxAge}`;
    }
    
    if (cookieOptions.path) {
      cookieString += `; Path=${cookieOptions.path}`;
    }
    
    if (cookieOptions.secure) {
      cookieString += '; Secure';
    }
    
    if (cookieOptions.sameSite) {
      cookieString += `; SameSite=${cookieOptions.sameSite}`;
    }

    // Add domain if in production
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_DOMAIN) {
      cookieString += `; Domain=${process.env.NEXT_PUBLIC_DOMAIN}`;
    }

    document.cookie = cookieString;
  }

  /**
   * Get a cookie value
   */
  private static getCookie(name: string): string | null {
    if (typeof document === 'undefined') return null; // Server-side check

    const nameEQ = name + '=';
    const cookies = document.cookie.split(';');
    
    for (let cookie of cookies) {
      let c = cookie.trim();
      if (c.indexOf(nameEQ) === 0) {
        return decodeURIComponent(c.substring(nameEQ.length));
      }
    }
    return null;
  }

  /**
   * Delete a cookie
   */
  private static deleteCookie(name: string): void {
    if (typeof document === 'undefined') return; // Server-side check

    let cookieString = `${name}=; Max-Age=0; Path=/`;
    
    // Add domain if in production
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_DOMAIN) {
      cookieString += `; Domain=${process.env.NEXT_PUBLIC_DOMAIN}`;
    }

    document.cookie = cookieString;
  }

  /**
   * Save user login information securely
   */
  static saveLoginInfo(user: any, token: string): void {
    try {
      // Save user data (excluding sensitive information)
      const userToSave = {
        UserId: user.UserId || user.UserID,
        UserName: user.UserName,
        Email: user.Email,
        FirstName: user.FirstName,
        LastName: user.LastName,
        PhoneNumber: user.PhoneNumber,
        Pointno: user.Pointno,
        // Add other non-sensitive user fields as needed
      };

      this.setCookie(this.USER_COOKIE, JSON.stringify(userToSave));
      this.setCookie(this.TOKEN_COOKIE, token);
      this.setCookie(this.LOGIN_STATUS_COOKIE, 'true');

      console.log('Login info saved to secure cookies');
    } catch (error) {
      console.error('Error saving login info to cookies:', error);
    }
  }

  /**
   * Get user information from cookies
   */
  static getLoginInfo(): { user: any | null; token: string | null; isLoggedIn: boolean } {
    try {
      const userCookie = this.getCookie(this.USER_COOKIE);
      const tokenCookie = this.getCookie(this.TOKEN_COOKIE);
      const isLoggedInCookie = this.getCookie(this.LOGIN_STATUS_COOKIE);

      const user = userCookie ? JSON.parse(userCookie) : null;
      const token = tokenCookie;
      const isLoggedIn = isLoggedInCookie === 'true' && user && token;

      return { user, token, isLoggedIn };
    } catch (error) {
      console.error('Error reading login info from cookies:', error);
      return { user: null, token: null, isLoggedIn: false };
    }
  }

  /**
   * Clear all login information
   */
  static clearLoginInfo(): void {
    try {
      this.deleteCookie(this.USER_COOKIE);
      this.deleteCookie(this.TOKEN_COOKIE);
      this.deleteCookie(this.LOGIN_STATUS_COOKIE);
      this.clearUserPoints(); // Clear points when logging out

      // Clear localStorage for complete cleanup (migration from old implementation)
      if (typeof window !== 'undefined') {
        localStorage.removeItem('user');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('token');
        localStorage.removeItem('authToken');
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('userId');
        localStorage.removeItem('userID');
      }

      console.log('🔐 Login info cleared from cookies and localStorage');
    } catch (error) {
      console.error('Error clearing login info:', error);
    }
  }

  /**
   * Check if user is logged in based on cookies
   */
  static isUserLoggedIn(): boolean {
    const { isLoggedIn } = this.getLoginInfo();
    return isLoggedIn;
  }

  /**
   * Update user information in cookies
   */
  static updateUserInfo(updatedUser: any): void {
    try {
      const { token } = this.getLoginInfo();
      if (token) {
        this.saveLoginInfo(updatedUser, token);
      }
    } catch (error) {
      console.error('Error updating user info in cookies:', error);
    }
  }

  /**
   * Save user points to cookies
   */
  static saveUserPoints(points: string | number): void {
    try {
      const pointValue = typeof points === 'string' ? points : points.toString();
      this.setCookie(this.POINT_COOKIE, pointValue);
      console.log('User points saved to cookies:', pointValue);
    } catch (error) {
      console.error('Error saving user points to cookies:', error);
    }
  }

  /**
   * Get user points from cookies
   */
  static getUserPoints(): string | null {
    try {
      return this.getCookie(this.POINT_COOKIE);
    } catch (error) {
      console.error('Error getting user points from cookies:', error);
      return null;
    }
  }

  /**
   * Clear user points from cookies
   */
  static clearUserPoints(): void {
    try {
      this.deleteCookie(this.POINT_COOKIE);
      console.log('User points cleared from cookies');
    } catch (error) {
      console.error('Error clearing user points from cookies:', error);
    }
  }

  /**
   * Migrate from localStorage to cookies (for existing users)
   */
  static migrateFromLocalStorage(): void {
    try {
      // Check if we already have cookies
      const { isLoggedIn } = this.getLoginInfo();
      if (isLoggedIn) {
        return; // Already using cookies
      }

      // Try to migrate from localStorage
      const storedUser = localStorage.getItem('user') || localStorage.getItem('userInfo');
      const storedToken = localStorage.getItem('token') || localStorage.getItem('authToken');
      const isLoggedInFlag = localStorage.getItem('isLoggedIn');

      if (storedUser && storedToken && isLoggedInFlag === 'true') {
        const user = JSON.parse(storedUser);
        this.saveLoginInfo(user, storedToken);
        
        // Clear localStorage after successful migration
        localStorage.removeItem('user');
        localStorage.removeItem('userInfo');
        localStorage.removeItem('token');
        localStorage.removeItem('authToken');
        localStorage.removeItem('isLoggedIn');

        console.log('Successfully migrated login info from localStorage to cookies');
      }
    } catch (error) {
      console.error('Error migrating from localStorage to cookies:', error);
    }
  }
}
