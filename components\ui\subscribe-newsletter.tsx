'use client';

import { useState } from 'react';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { Button } from './button';
import { Input } from './input';
import { Send, CheckCircle } from 'lucide-react';

export function SubscribeNewsletter() {
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const successColor = '#10b981';
  const destructiveColor = '#dc2626'; // Red color for error states
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset states
    setError('');
    setLoading(true);

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      setLoading(false);
      return;
    }

    // Simulate API call
    try {
      // Replace with actual API call when available
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSuccess(true);
      setEmail('');
    } catch (err) {
      setError('Failed to subscribe. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-muted/50 py-8 sm:py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-2xl sm:text-3xl font-bold mb-3">Subscribe to Our Newsletter</h2>
          <p className="text-muted-foreground mb-6">
            Stay updated with our latest offers and products
          </p>

          {success ? (
            <div className="flex flex-col items-center justify-center p-6 bg-background rounded-lg shadow-sm">
              <div 
                className="w-12 h-12 rounded-full flex items-center justify-center mb-4"
                style={{ backgroundColor: `${successColor}20` }}
              >
                <CheckCircle className="h-6 w-6" style={{ color: successColor }} />
              </div>
              <h3 className="text-xl font-medium mb-2">Thank You!</h3>
              <p className="text-muted-foreground mb-4">
                You have successfully subscribed to our newsletter.
              </p>
              <Button
                variant="outline"
                onClick={() => setSuccess(false)}
              >
                Subscribe Another Email
              </Button>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
              <div className="flex-1">
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Your email address"
                  className={error ? '' : ''}
                  style={error ? { borderColor: destructiveColor } : {}}
                  disabled={loading}
                  required
                />
                {error && <p className="text-sm mt-1 text-left" style={{ color: destructiveColor }}>{error}</p>}
              </div>
              <Button
                type="submit"
                disabled={loading}
                className="min-w-[120px]"
              >
                {loading ? (
                  <span className="flex items-center gap-2">
                    <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Subscribing...
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <Send className="h-4 w-4" />
                    Subscribe
                  </span>
                )}
              </Button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}