'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';

type TranslationKey = 'styleVariant' | keyof typeof import('@/lib/translations').translations.en;

interface VariantOption {
  id: string;
  name: string;
  value: string;
  type: 'style';
  inStock: boolean;
}

interface VariantGroup {
  name: string;
  type: 'style';
  options: VariantOption[];
}

interface ProductVariantsProps {
  variants: VariantGroup[];
  onVariantSelect: (type: string, value: string) => void;
  selectedVariants: Record<string, string>;
}

interface UseSettingsReturn {
  t: (key: TranslationKey) => string;
}

export function ProductVariants({ variants, onVariantSelect, selectedVariants }: ProductVariantsProps) {
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const [error, setError] = useState<string>('');

  const handleVariantSelect = (type: string, value: string) => {
    setError('');
    onVariantSelect(type, value);
  };

  const getVariantButtonClass = (option: VariantOption, isSelected: boolean) => {
    let baseClass = 'min-w-[40px] h-10 px-3 rounded-md ';

    if (!option.inStock) {
      return `${baseClass} opacity-50 cursor-not-allowed bg-muted`;
    }

    if (isSelected) {
      return `${baseClass} border-2`;
    }

    return `${baseClass} border border-border hover:border-2`;
  };

  const getVariantButtonStyle = (option: VariantOption, isSelected: boolean) => {
    if (!option.inStock) {
      return {};
    }

    if (isSelected) {
      return {
        borderColor: primaryColor,
        backgroundColor: `${primaryColor}1A`
      };
    }

    return {
      borderColor: 'var(--border)'
    };
  };

  return (
    <div className="space-y-6">
      {variants.map((variant) => (
        <div key={variant.name} className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">{variant.name}</span>
            {selectedVariants[variant.type] && (
              <span className="text-sm text-muted-foreground">
                {variant.options.find(opt => opt.value === selectedVariants[variant.type])?.name}
              </span>
            )}
          </div>

          <div className="flex flex-wrap gap-2">
            {variant.options.map((option) => {
              const isSelected = selectedVariants[variant.type] === option.value;
              return (
                <Button
                  key={option.id}
                  type="button"
                  variant="outline"
                  className={getVariantButtonClass(option, isSelected)}
                  style={getVariantButtonStyle(option, isSelected)}
                  disabled={!option.inStock}
                  onClick={() => handleVariantSelect(variant.type, option.value)}
                  onMouseEnter={(e) => {
                    if (!option.inStock || isSelected) return;
                    e.currentTarget.style.borderColor = primaryColor;
                  }}
                  onMouseLeave={(e) => {
                    if (!option.inStock || isSelected) return;
                    e.currentTarget.style.borderColor = 'var(--border)';
                  }}
                >
                  {option.name}
                </Button>
              );
            })}
          </div>
        </div>
      ))}

      {error && (
        <p className="text-sm text-destructive mt-2">{error}</p>
      )}
    </div>
  );
}