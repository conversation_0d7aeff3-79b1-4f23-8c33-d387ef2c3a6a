'use client';

import { useTheme } from 'next-themes';
import { Toaster as Sonner } from 'sonner';
import { useColorThemeContext } from '@/contexts/color-theme-context';

type ToasterProps = React.ComponentProps<typeof Sonner>;

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = 'system' } = useTheme();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;

  return (
    <>
      <style>{`
        .group-\[\.toast\]:bg-primary-dynamic {
          background-color: ${primaryColor} !important;
        }
        .group-\[\.toaster\]:bg-primary-dynamic {
          background-color: ${primaryColor} !important;
        }
        .group-\[\.toaster\]:border-primary-dynamic {
          border-color: ${primaryColor} !important;
        }
      `}</style>
      <Sonner
        theme={theme as ToasterProps['theme']}
        className="toaster group"
        duration={2000}
        toastOptions={{
          duration: 2000,
          classNames: {
            toast:
              'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',
            description: 'group-[.toast]:text-muted-foreground',
            actionButton:
              'group-[.toast]:bg-primary-dynamic group-[.toast]:text-white',
            cancelButton:
              'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground',
            success:
              'group toast group-[.toaster]:bg-success group-[.toaster]:text-success-foreground group-[.toaster]:border-success group-[.toaster]:shadow-lg',
            error:
              'group toast group-[.toaster]:bg-destructive group-[.toaster]:text-destructive-foreground group-[.toaster]:border-destructive group-[.toaster]:shadow-lg',
            warning:
              'group toast group-[.toaster]:bg-warning group-[.toaster]:text-warning-foreground group-[.toaster]:border-warning group-[.toaster]:shadow-lg',
            info:
              'group toast group-[.toaster]:bg-primary-dynamic group-[.toaster]:text-white group-[.toaster]:border-primary-dynamic group-[.toaster]:shadow-lg',
          },
        }}
        {...props}
      />
    </>
  );
};

export { Toaster };
