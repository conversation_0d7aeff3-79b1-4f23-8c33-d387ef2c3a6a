'use client'

/**
 * Connection Speed Detection Utility
 * Helps determine optimal video quality based on network conditions
 */

export interface ConnectionInfo {
  speed: number // in kbps
  latency: number // in ms
  quality: 'slow' | 'medium' | 'fast' | 'very-fast'
  recommendedVideoQuality: string
}

export class ConnectionSpeedDetector {
  private static instance: ConnectionSpeedDetector
  private lastTest: ConnectionInfo | null = null
  private testInProgress = false

  static getInstance(): ConnectionSpeedDetector {
    if (!ConnectionSpeedDetector.instance) {
      ConnectionSpeedDetector.instance = new ConnectionSpeedDetector()
    }
    return ConnectionSpeedDetector.instance
  }

  /**
   * Test connection speed using multiple methods
   */
  async testConnectionSpeed(): Promise<ConnectionInfo> {
    if (this.testInProgress) {
      return this.lastTest || this.getDefaultConnection()
    }

    this.testInProgress = true

    try {
      // Use multiple test methods for better accuracy
      const results = await Promise.allSettled([
        this.testWithImage(),
        this.testWithFetch(),
        this.testWithNavigatorConnection()
      ])

      const speeds: number[] = []
      const latencies: number[] = []

      results.forEach(result => {
        if (result.status === 'fulfilled' && result.value) {
          speeds.push(result.value.speed)
          latencies.push(result.value.latency)
        }
      })

      if (speeds.length === 0) {
        return this.getDefaultConnection()
      }

      // Calculate median values for better accuracy
      const medianSpeed = this.calculateMedian(speeds)
      const medianLatency = this.calculateMedian(latencies)

      const connectionInfo: ConnectionInfo = {
        speed: medianSpeed,
        latency: medianLatency,
        quality: this.determineQuality(medianSpeed, medianLatency),
        recommendedVideoQuality: this.getRecommendedVideoQuality(medianSpeed)
      }

      this.lastTest = connectionInfo
      return connectionInfo

    } catch (error) {
      console.error('Connection speed test failed:', error)
      return this.getDefaultConnection()
    } finally {
      this.testInProgress = false
    }
  }

  /**
   * Test speed using image download
   */
  private async testWithImage(): Promise<{ speed: number; latency: number } | null> {
    return new Promise((resolve) => {
      const startTime = performance.now()
      const img = new Image()
      
      const timeout = setTimeout(() => {
        resolve(null)
      }, 10000) // 10 second timeout

      img.onload = () => {
        clearTimeout(timeout)
        const endTime = performance.now()
        const duration = (endTime - startTime) / 1000 // in seconds
        const imageSize = 100 * 1024 // Assume 100KB
        const speed = (imageSize * 8) / duration / 1000 // kbps
        const latency = endTime - startTime

        resolve({ speed, latency })
      }

      img.onerror = () => {
        clearTimeout(timeout)
        resolve(null)
      }

      // Use a cache-busting parameter
      img.src = `data:image/svg+xml;base64,${btoa('<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100"><rect width="100" height="100" fill="#000"/></svg>')}?t=${Date.now()}`
    })
  }

  /**
   * Test speed using fetch API
   */
  private async testWithFetch(): Promise<{ speed: number; latency: number } | null> {
    try {
      const startTime = performance.now()
      
      // Create a small test payload
      const testData = new Uint8Array(50 * 1024) // 50KB
      const blob = new Blob([testData])
      
      // Add timestamp to prevent caching issues on Amplify
      const response = await fetch(`/api/video-proxy?test=speed&t=${Date.now()}`, {
        method: 'HEAD',
        signal: AbortSignal.timeout(8000),
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })

      const endTime = performance.now()
      const duration = (endTime - startTime) / 1000
      const speed = (50 * 8) / duration // kbps (50KB * 8 bits)
      const latency = endTime - startTime

      return { speed, latency }
    } catch (error) {
      console.warn('Connection speed test failed:', error)
      // Fallback to a conservative estimate for Amplify
      return { speed: 1000, latency: 200 }
    }
  }

  /**
   * Use Navigator Connection API if available
   */
  private async testWithNavigatorConnection(): Promise<{ speed: number; latency: number } | null> {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      if (connection && connection.downlink) {
        return {
          speed: connection.downlink * 1000, // Convert Mbps to kbps
          latency: connection.rtt || 100 // Use RTT if available, otherwise default
        }
      }
    }
    return null
  }

  /**
   * Calculate median value from array
   */
  private calculateMedian(values: number[]): number {
    const sorted = values.sort((a, b) => a - b)
    const mid = Math.floor(sorted.length / 2)
    return sorted.length % 2 !== 0 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2
  }

  /**
   * Determine connection quality based on speed and latency
   */
  private determineQuality(speed: number, latency: number): 'slow' | 'medium' | 'fast' | 'very-fast' {
    if (speed < 500 || latency > 500) return 'slow'
    if (speed < 1500 || latency > 200) return 'medium'
    if (speed < 3000 || latency > 100) return 'fast'
    return 'very-fast'
  }

  /**
   * Get recommended video quality based on connection speed
   */
  private getRecommendedVideoQuality(speed: number): string {
    if (speed < 400) return '240p'
    if (speed < 800) return '360p'
    if (speed < 1500) return '480p'
    if (speed < 2500) return '720p'
    return '720p' // Max quality for now
  }

  /**
   * Get default connection info when tests fail
   */
  private getDefaultConnection(): ConnectionInfo {
    return {
      speed: 1000, // Default to 1 Mbps
      latency: 100,
      quality: 'medium',
      recommendedVideoQuality: '360p'
    }
  }

  /**
   * Get cached connection info if available
   */
  getCachedConnectionInfo(): ConnectionInfo | null {
    return this.lastTest
  }

  /**
   * Clear cached connection info
   */
  clearCache(): void {
    this.lastTest = null
  }
}

/**
 * Hook for using connection speed detection in React components
 */
export function useConnectionSpeed() {
  const detector = ConnectionSpeedDetector.getInstance()
  
  return {
    testSpeed: () => detector.testConnectionSpeed(),
    getCachedInfo: () => detector.getCachedConnectionInfo(),
    clearCache: () => detector.clearCache()
  }
}

/**
 * Utility function to get optimal video settings based on connection
 */
export function getOptimalVideoSettings(connectionInfo: ConnectionInfo) {
  const { speed, quality } = connectionInfo
  
  return {
    quality: connectionInfo.recommendedVideoQuality,
    preload: quality === 'slow' ? 'none' : quality === 'medium' ? 'metadata' : 'auto',
    bufferSize: quality === 'slow' ? 'small' : quality === 'medium' ? 'medium' : 'large',
    autoplay: quality !== 'slow', // Disable autoplay for slow connections
    poster: true // Always show poster for better UX
  }
}

export default ConnectionSpeedDetector