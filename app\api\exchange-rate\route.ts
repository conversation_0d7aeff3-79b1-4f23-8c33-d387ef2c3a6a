import { NextRequest, NextResponse } from 'next/server';
import { Config } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    console.log('📊 Exchange rate API route called');
    
    const body = await request.json();
    console.log('📊 Request body:', body);
    
    // Forward the request to the external API
    const apiUrl = `${Config.ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-exchange-rate`;
    console.log('📊 Calling external API:', apiUrl);
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(body),
    });

    console.log('📊 External API response status:', response.status);
    
    const data = await response.json();
    console.log('📊 External API response data:', data);

    if (!response.ok) {
      console.error('📊 External API error:', data);
      // Return a fallback rate in case of API failure
      return NextResponse.json({
        statusCode: 200,
        statusMessage: 'Ok',
        data: '1500', // Fallback rate
        message: 'Using fallback exchange rate due to API error'
      });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('📊 Exchange rate API route error:', error);
    // Return a fallback rate in case of any error
    return NextResponse.json({
      statusCode: 200,
      statusMessage: 'Ok', 
      data: '1500', // Fallback rate
      message: 'Using fallback exchange rate due to internal error'
    });
  }
}

// Also support GET method for testing
export async function GET(request: NextRequest) {
  console.log('📊 Exchange rate GET request - redirecting to POST');
  
  // Default request body for GET
  const defaultBody = {
    requestParameters: {}
  };
  
  // Create a new request with POST method
  const postRequest = new NextRequest(request.url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(defaultBody)
  });
  
  return POST(postRequest);
}