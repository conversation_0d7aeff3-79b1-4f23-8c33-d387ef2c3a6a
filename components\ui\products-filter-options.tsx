'use client';

import { useState } from 'react';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { useSettings } from '@/contexts/settings-context';

interface FilterOption {
  id: string;
  name: string;
  count: number;
}

interface FilterGroup {
  id: string;
  name: string;
  type: 'checkbox' | 'radio' | 'range';
  options?: FilterOption[];
  range?: {
    min: number;
    max: number;
    step: number;
    unit: string;
  };
}

interface FilterState {
  [key: string]: string[] | number[];
}

interface ProductsFilterOptionsProps {
  filters: FilterGroup[];
  onFilterChange: (filters: FilterState) => void;
  initialFilters?: FilterState;
}

export function ProductsFilterOptions({
  filters,
  onFilterChange,
  initialFilters = {}
}: ProductsFilterOptionsProps) {
  const { t } = useSettings();
  const [activeFilters, setActiveFilters] = useState<FilterState>(initialFilters);

  const handleCheckboxChange = (groupId: string, optionId: string, checked: boolean) => {
    const currentValues = (activeFilters[groupId] as string[]) || [];
    let newValues: string[];

    if (checked) {
      newValues = [...currentValues, optionId];
    } else {
      newValues = currentValues.filter(value => value !== optionId);
    }

    const newFilters = {
      ...activeFilters,
      [groupId]: newValues
    };

    setActiveFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleRangeChange = (groupId: string, values: number[]) => {
    const newFilters = {
      ...activeFilters,
      [groupId]: values
    };

    setActiveFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    const emptyFilters = {};
    setActiveFilters(emptyFilters);
    onFilterChange(emptyFilters);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-lg">{t('filters')}</h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={clearFilters}
          className="text-muted-foreground hover:text-primary"
        >
          {t('clearAll')}
        </Button>
      </div>

      {filters.map((group) => (
        <div key={group.id} className="space-y-3">
          <h4 className="font-medium">{group.name}</h4>

          {group.type === 'range' && group.range && (
            <div className="px-2">
              <Slider
                defaultValue={[group.range.min, group.range.max]}
                min={group.range.min}
                max={group.range.max}
                step={group.range.step}
                value={(
                  (activeFilters[group.id] as number[]) ||
                  [group.range.min, group.range.max]
                )}
                onValueChange={(values) => handleRangeChange(group.id, values)}
              />
              <div className="mt-2 flex items-center justify-between text-sm text-muted-foreground">
                <span>
                  {((activeFilters[group.id] as number[]) || [group.range.min])[0]}
                  {group.range.unit}
                </span>
                <span>
                  {((activeFilters[group.id] as number[]) || [0, group.range.max])[1]}
                  {group.range.unit}
                </span>
              </div>
            </div>
          )}

          {group.type === 'checkbox' && group.options && (
            <div className="space-y-2">
              {group.options.map((option) => (
                <div key={option.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${group.id}-${option.id}`}
                    checked={
                      ((activeFilters[group.id] as string[]) || []).includes(option.id)
                    }
                    onCheckedChange={(checked) =>
                      handleCheckboxChange(group.id, option.id, checked as boolean)
                    }
                  />
                  <label
                    htmlFor={`${group.id}-${option.id}`}
                    className="text-sm flex-1 flex items-center justify-between"
                  >
                    <span>{option.name}</span>
                    <span className="text-muted-foreground">({option.count})</span>
                  </label>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}