'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type WishlistItem = {
  productId: number;
  productName: string;
  productUrl: string;
  imageUrl?: string;
  price?: number;
  addedAt: string;
};

type WishlistContextType = {
  wishlistItems: WishlistItem[];
  addToWishlist: (productId: number, productName: string, productUrl: string, imageUrl?: string, price?: number) => void;
  removeFromWishlist: (productId: number) => void;
  isInWishlist: (productId: number) => boolean;
  getWishlistItem: (productId: number) => WishlistItem | undefined;
  totalItems: number;
  isHydrated: boolean;
};

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export function WishlistProvider({ children }: { children: React.ReactNode }) {
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    const savedWishlist = localStorage.getItem('wishlist');
    if (savedWishlist) {
      try {
        const parsed = JSON.parse(savedWishlist);
        // Handle backward compatibility with old format (array of numbers)
        if (Array.isArray(parsed) && parsed.length > 0) {
          if (typeof parsed[0] === 'number') {
            // Old format - convert to new format with minimal data
            const convertedItems: WishlistItem[] = parsed.map((id: number) => ({
              productId: id,
              productName: `Product ${id}`,
              productUrl: `/product/${id}`,
              addedAt: new Date().toISOString(),
            }));
            setWishlistItems(convertedItems);
            // Save in new format
            localStorage.setItem('wishlist', JSON.stringify(convertedItems));
          } else {
            // New format
            setWishlistItems(parsed);
          }
        }
      } catch (error) {
        console.error('Failed to parse wishlist from localStorage:', error);
      }
    }
    setIsHydrated(true);
  }, []);

  useEffect(() => {
    localStorage.setItem('wishlist', JSON.stringify(wishlistItems));
  }, [wishlistItems]);

  const addToWishlist = (productId: number, productName: string, productUrl: string, imageUrl?: string, price?: number) => {
    if (!wishlistItems.some(item => item.productId === productId)) {
      const newItem: WishlistItem = {
        productId,
        productName,
        productUrl,
        imageUrl,
        price,
        addedAt: new Date().toISOString(),
      };
      setWishlistItems([...wishlistItems, newItem]);
    }
  };

  const removeFromWishlist = (productId: number) => {
    setWishlistItems(wishlistItems.filter(item => item.productId !== productId));
  };

  const isInWishlist = (productId: number) => {
    return wishlistItems.some(item => item.productId === productId);
  };

  const getWishlistItem = (productId: number) => {
    return wishlistItems.find(item => item.productId === productId);
  };

  return (
    <WishlistContext.Provider
      value={{
        wishlistItems,
        addToWishlist,
        removeFromWishlist,
        isInWishlist,
        getWishlistItem,
        totalItems: wishlistItems.length,
        isHydrated,
      }}
    >
      {children}
    </WishlistContext.Provider>
  );
}

export function useWishlist() {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
}