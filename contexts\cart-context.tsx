'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useCurrency } from './currency-context';

export interface CartItemAttribute {
  ProductAttributeID: number;
  AttributeName: string;
  DisplayName: string;
  AttributeValueID: number;
  AttributeValueText: string;
  PriceAdjustment?: number;
  PriceAdjustmentType?: number;
}

export type CartItem = {
  id: number;
  name: string;
  price: number; // Original price before any discounts or adjustments
  discountPrice?: number; // Discounted price if any
  iqdPrice?: number; // Price in IQD currency
  adjustedIqdPrice?: number; // Adjusted IQD price after attributes
  image: string;
  quantity: number;
  attributes?: CartItemAttribute[];
  adjustedPrice: number; // Final price after all adjustments
  originalPrice: number; // Original price before any attribute adjustments
};

interface CartContextType {
  items: CartItem[];
  addToCart: (
    item: Omit<CartItem, 'quantity' | 'adjustedPrice' | 'originalPrice' | 'adjustedIqdPrice'> & { originalPrice: number },
    quantity: number,
    attributes?: CartItemAttribute[],
    iqdPrice?: number,
    currencyRate?: number
  ) => void;
  removeFromCart: (id: number) => void;
  updateQuantity: (id: number, quantity: number) => void;
  clearCart: () => void;
  totalItems: number;
  subtotal: number;
  subtotalIQD: number;
  total: number;
  totalIQD: number;
  isHydrated: boolean;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([]);
  const [isHydrated, setIsHydrated] = useState(false);
  const { rate } = useCurrency(); // Get dynamic rate from currency context
  
  console.log('💱 CartProvider: Using dynamic currency rate:', rate);
  
  // Load cart from localStorage on initial render
  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      try {
        setItems(JSON.parse(savedCart));
      } catch (error) {
        console.error('Failed to parse cart from localStorage:', error);
      }
    }
    setIsHydrated(true);
  }, []);
  
  // Save cart to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(items));
  }, [items]);
  
  const addToCart = (
    item: Omit<CartItem, 'quantity' | 'adjustedPrice' | 'originalPrice' | 'adjustedIqdPrice'> & { originalPrice: number },
    quantity: number,
    attributes: CartItemAttribute[] = [],
    iqdPrice?: number,
    currencyRate?: number // Optional override, will use context rate if not provided
  ) => {
    // Use provided rate or fall back to context rate
    const effectiveRate = currencyRate || rate;
    console.log('💱 CartProvider addToCart: Using rate:', effectiveRate);
    setItems(prevItems => {
      // Start with the base price (which could be a discounted price if applicable)
      let adjustedPrice = item.price;

      // Calculate base IQD price
      let baseIqdPrice = iqdPrice || Math.round(item.price * effectiveRate);
      let adjustedIqdPrice = baseIqdPrice;

      // Apply attribute-based price adjustments
      attributes.forEach(attr => {
        if (attr.PriceAdjustment && attr.PriceAdjustmentType) {
          const basePriceForAdjustment = item.originalPrice || item.price;
          switch(attr.PriceAdjustmentType) {
            case 1: // Fixed amount
              adjustedPrice += attr.PriceAdjustment;
              adjustedIqdPrice += Math.round(attr.PriceAdjustment * effectiveRate);
              break;
            case 2: // Percentage
              const percentageAdjustment = (basePriceForAdjustment * attr.PriceAdjustment) / 100;
              adjustedPrice += percentageAdjustment;
              adjustedIqdPrice += Math.round(percentageAdjustment * effectiveRate);
              break;
          }
        }
      });
      
      // Find if item with same ID and attributes already exists
      const existingItemIndex = prevItems.findIndex(i => 
        i.id === item.id && 
        JSON.stringify(i.attributes?.sort((a, b) => a.ProductAttributeID - b.ProductAttributeID)) === 
        JSON.stringify(attributes?.sort((a, b) => a.ProductAttributeID - b.ProductAttributeID))
      );
      
      if (existingItemIndex >= 0) {
        // Item with same attributes exists, update quantity
        const updatedItems = [...prevItems];
        updatedItems[existingItemIndex].quantity += quantity;
        return updatedItems;
      } else {
        // Add new item with all price information
        return [...prevItems, {
          ...item,
          iqdPrice: baseIqdPrice,
          adjustedIqdPrice: Math.max(0, adjustedIqdPrice),
          quantity,
          attributes,
          adjustedPrice: Math.max(0, adjustedPrice), // Ensure price doesn't go below 0
          originalPrice: item.originalPrice
        }];
      }
    });
  };
  
  const removeFromCart = (id: number) => {
    setItems(prevItems => prevItems.filter(item => item.id !== id));
  };
  
  const updateQuantity = (id: number, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(id);
      return;
    }
    
    setItems(prevItems => 
      prevItems.map(item => 
        item.id === id ? { ...item, quantity } : item
      )
    );
  };
  
  const clearCart = () => {
    setItems([]);
  };
  
  const totalItems = items.reduce((total, item) => total + item.quantity, 0);
  
  // Update localStorage and trigger re-render when cart changes
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(items));
  }, [items]);
  
  const subtotal = items.reduce((total, item) => {
    const price = item.discountPrice ? Math.min(item.discountPrice, item.adjustedPrice) : item.adjustedPrice;
    return total + (price * item.quantity);
  }, 0);

  const subtotalIQD = items.reduce((total, item) => {
    const iqdPrice = item.adjustedIqdPrice || item.iqdPrice || 0;
    return total + (iqdPrice * item.quantity);
  }, 0);

  // For now, total is same as subtotal, but could include shipping, tax, etc.
  const total = subtotal;
  const totalIQD = subtotalIQD;
  
  return (
    <CartContext.Provider
      value={{
        items,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        totalItems,
        subtotal,
        subtotalIQD,
        total,
        totalIQD,
        isHydrated
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}