'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@/contexts/user-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';
import { useRouter } from 'next/navigation';
import Swal from 'sweetalert2';

interface ProfileData {
  FirstName: string;
  LastName: string;
  AddressLineOne: string;
  CityId: string;
  StateProvinceId: string;
  PostalCode: string;
  CategoryId: string;
}

interface City {
  CityId: number;
  CityName: string;
}

interface State {
  StateProvinceId: number;
  StateProvinceName: string;
}

interface Category {
  CategoryId: number;
  CategoryName: string;
}

export default function ProfilePage() {
  const { user, isLoggedIn, updateProfile } = useUser();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [cities, setCities] = useState<City[]>([]);
  const [states, setStates] = useState<State[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  
  const [profileData, setProfileData] = useState<ProfileData>({
    FirstName: '',
    LastName: '',
    AddressLineOne: '',
    CityId: '-999',
    StateProvinceId: '-999',
    PostalCode: '',
    CategoryId: '1024'
  });

  // Redirect if not logged in
  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }
  }, [isLoggedIn, router]);

  // Initialize profile data from user context
  useEffect(() => {
    if (user) {
      setProfileData(prev => ({
        ...prev,
        FirstName: user.FirstName || '',
        LastName: user.LastName || '',
        AddressLineOne: user.AddressLineOne || '',
        CityId: user.CityId?.toString() || '-999',
        StateProvinceId: user.StateProvinceId?.toString() || '-999',
        PostalCode: user.PostalCode || '',
        CategoryId: user.CategoryId?.toString() || '1024'
      }));
    }
  }, [user]);

  // Load categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const response = await MakeApiCallAsync(
          Config.END_POINT_NAMES.GET_CATEGORIES_LIST,
          null,
          {},
          {},
          'POST'
        );

        if (response?.data && !response.data.errorMessage) {
          let categoriesData = response.data;
          
          // Handle nested response structure
          if (typeof categoriesData === 'string') {
            categoriesData = JSON.parse(categoriesData);
          }
          
          if (Array.isArray(categoriesData) && categoriesData.length > 0 && categoriesData[0].DATA) {
            const innerData = JSON.parse(categoriesData[0].DATA);
            if (Array.isArray(innerData)) {
              setCategories(innerData);
            }
          }
        }
      } catch (error) {
        console.error('Error loading categories:', error);
      }
    };

    loadCategories();
  }, []);

  const handleInputChange = (field: keyof ProfileData, value: string) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleUpdateProfile = async () => {
    if (!user?.UserId) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'User ID not found. Please log in again.'
      });
      return;
    }

    setLoading(true);
    
    try {
      const headers = {
        Accept: "application/json",
        "Content-Type": "application/json",
      };

      const param = {
        requestParameters: {
          // UserID removed - will be auto-injected from JWT token
          FirstName: profileData.FirstName,
          LastName: profileData.LastName,
          AddressLineOne: profileData.AddressLineOne,
          CityId: profileData.CityId,
          StateProvinceId: profileData.StateProvinceId,
          PostalCode: profileData.PostalCode,
          CategoryId: profileData.CategoryId
        }
      };

      const response = await MakeApiCallAsync(
        Config.END_POINT_NAMES.UPDATE_PROFILE,
        null,
        param,
        headers,
        "POST"
      );

      if (response?.data && !response.data.errorMessage) {
        // Update user context with new profile data
        updateProfile({
          FirstName: profileData.FirstName,
          LastName: profileData.LastName,
          AddressLineOne: profileData.AddressLineOne,
          CityId: parseInt(profileData.CityId),
          StateProvinceId: parseInt(profileData.StateProvinceId),
          PostalCode: profileData.PostalCode,
          CategoryId: parseInt(profileData.CategoryId),
          UserName: `${profileData.FirstName} ${profileData.LastName}`.trim()
        });

        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Profile updated successfully!',
          timer: 2000,
          showConfirmButton: false
        });
      } else {
        throw new Error(response?.data?.errorMessage || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      Swal.fire({
        icon: 'error',
        title: 'Update Failed',
        text: error instanceof Error ? error.message : 'An error occurred while updating your profile'
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isLoggedIn || !user) {
    return (
      <div className="container mx-auto p-8">
        <div className="text-center">
          <p>Please log in to access your profile.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-8">
      <Card className="max-w-2xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Update Profile</h1>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                type="text"
                value={profileData.FirstName}
                onChange={(e) => handleInputChange('FirstName', e.target.value)}
                placeholder="Enter your first name"
              />
            </div>
            
            <div>
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                type="text"
                value={profileData.LastName}
                onChange={(e) => handleInputChange('LastName', e.target.value)}
                placeholder="Enter your last name"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="address">Address</Label>
            <Input
              id="address"
              type="text"
              value={profileData.AddressLineOne}
              onChange={(e) => handleInputChange('AddressLineOne', e.target.value)}
              placeholder="Enter your address"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="cityId">City</Label>
              <Select
                value={profileData.CityId}
                onValueChange={(value) => handleInputChange('CityId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select city" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="-999">Select City</SelectItem>
                  {cities.map((city) => (
                    <SelectItem key={city.CityId} value={city.CityId.toString()}>
                      {city.CityName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="stateId">State/Province</Label>
              <Select
                value={profileData.StateProvinceId}
                onValueChange={(value) => handleInputChange('StateProvinceId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select state" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="-999">Select State</SelectItem>
                  {states.map((state) => (
                    <SelectItem key={state.StateProvinceId} value={state.StateProvinceId.toString()}>
                      {state.StateProvinceName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="postalCode">Postal Code</Label>
            <Input
              id="postalCode"
              type="text"
              value={profileData.PostalCode}
              onChange={(e) => handleInputChange('PostalCode', e.target.value)}
              placeholder="Enter postal code"
            />
          </div>

          <div>
            <Label htmlFor="category">Category</Label>
            <Select
              value={profileData.CategoryId}
              onValueChange={(value) => handleInputChange('CategoryId', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1024">Default Category</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.CategoryId} value={category.CategoryId.toString()}>
                    {category.CategoryName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="pt-4">
            <Button 
              onClick={handleUpdateProfile} 
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Updating...' : 'Update Profile'}
            </Button>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gray-50 rounded">
          <h3 className="font-semibold mb-2">Current User Info:</h3>
          <p><strong>Email:</strong> {user.Email}</p>
          <p><strong>User ID:</strong> {user.UserId}</p>
          <p><strong>Phone:</strong> {user.PhoneNumber}</p>
        </div>
      </Card>
    </div>
  );
}