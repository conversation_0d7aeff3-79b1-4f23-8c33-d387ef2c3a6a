import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber } = await request.json();

    if (!phoneNumber) {
      return NextResponse.json(
        { success: false, error: 'Phone number is required' },
        { status: 400 }
      );
    }

    // Call .NET API to delete verification code
    const rawBase = process.env.ADMIN_BASE_URL || process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'https://admin.codemedicalapps.com';
    const base = rawBase.endsWith('/') ? rawBase.slice(0, -1) : rawBase;
    const requestBody = JSON.stringify({ PhoneNumber: phoneNumber }); // Capital P to match backend
    const apiResponse = await fetch(`${base}/api/v1/verification/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': requestBody.length.toString(),
        'User-Agent': 'NextJS-Frontend/1.0',
        'Accept': 'application/json',
      },
      body: requestBody
    });

    if (!apiResponse.ok) {
      throw new Error('Failed to delete verification code');
    }

    const result = await apiResponse.json();

    return NextResponse.json({
      success: result.success,
      message: result.message || 'Verification code deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting verification code:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete verification code' },
      { status: 500 }
    );
  }
}