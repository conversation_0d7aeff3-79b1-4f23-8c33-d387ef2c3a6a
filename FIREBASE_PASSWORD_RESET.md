# Firebase Password Reset Endpoint

This document describes the new Firebase-based password reset endpoint that doesn't depend on SQL database operations.

## Overview

The new endpoint `reset-password-firebase` uses Firebase Authentication for password management instead of directly updating the SQL database. This provides better security and reduces database dependencies.

## Endpoint Details

- **URL**: `api/v1/common/reset-password-firebase`
- **Method**: POST
- **Authentication**: Required (CustomerApiCallsAuthorization)

## Request Parameters

The endpoint accepts the following parameters in the `requestParameters` object:

```json
{
  "requestParameters": {
    "Email": "<EMAIL>",           // Optional: User's email
    "PhoneNumber": "+1234567890",          // Optional: User's phone number
    "NewPassword": "newpassword123",       // Required: New password (min 6 chars)
    "FirebaseUid": "firebase_user_uid"     // Required: Firebase user UID
  }
}
```

### Parameter Validation

- Either `Email` or `PhoneNumber` must be provided
- `NewPassword` is required and must be at least 6 characters
- `FirebaseUid` is required for authentication

## Response Format

### Success Response
```json
{
  "data": "[]",
  "statusCode": 200,
  "statusMessage": "Ok",
  "message": "Password reset request processed successfully. Please complete the reset using Firebase Authentication.",
  "errorMessage": ""
}
```

### Error Responses

#### Missing Email/Phone (400)
```json
{
  "statusCode": 400,
  "statusMessage": "Error",
  "errorMessage": "Please provide either email or phone number!"
}
```

#### Missing Password (400)
```json
{
  "statusCode": 400,
  "statusMessage": "Error",
  "errorMessage": "Please provide new password!"
}
```

#### Missing Firebase UID (400)
```json
{
  "statusCode": 400,
  "statusMessage": "Error",
  "errorMessage": "Firebase authentication required!"
}
```

#### Password Too Short (400)
```json
{
  "statusCode": 400,
  "statusMessage": "Error",
  "errorMessage": "Password must be at least 6 characters long!"
}
```

## Frontend Integration

### Using the API Helper

```typescript
import { Config, MakeApiCallAsync } from '@/lib/api-helper';

const resetPasswordWithFirebase = async (email: string, newPassword: string, firebaseUid: string) => {
  const requestParameters = {
    Email: email,
    NewPassword: newPassword,
    FirebaseUid: firebaseUid
  };

  const response = await MakeApiCallAsync(
    Config.END_POINT_NAMES.RESET_PASSWORD_FIREBASE,
    Config.COMMON_CONTROLLER_SUB_URL,
    { requestParameters: JSON.stringify(requestParameters) },
    {},
    'POST'
  );

  return response;
};
```

### Complete Firebase Integration Example

```typescript
import { auth } from '@/lib/firebase';
import { updatePassword, signInWithEmailAndPassword } from 'firebase/auth';

const handlePasswordReset = async (email: string, currentPassword: string, newPassword: string) => {
  try {
    // Step 1: Re-authenticate user with current password
    const userCredential = await signInWithEmailAndPassword(auth, email, currentPassword);
    
    // Step 2: Update password in Firebase
    await updatePassword(userCredential.user, newPassword);
    
    // Step 3: Call the backend endpoint
    const response = await resetPasswordWithFirebase(email, newPassword, userCredential.user.uid);
    
    if (response.data && !response.data.errorMessage) {
      console.log('Password reset successful!');
      return { success: true, message: response.data.message };
    } else {
      throw new Error(response.data?.errorMessage || 'Unknown error');
    }
  } catch (error) {
    console.error('Password reset failed:', error);
    return { success: false, error: error.message };
  }
};
```

## Key Benefits

1. **No SQL Dependency**: Password management is handled by Firebase Auth
2. **Enhanced Security**: Firebase provides robust authentication mechanisms
3. **Audit Logging**: All password reset attempts are logged for security purposes
4. **Validation**: Comprehensive input validation and error handling
5. **Flexibility**: Supports both email and phone number identification

## Testing

You can test this endpoint using the test page at `/test-firebase-reset` which provides a complete UI for testing the Firebase password reset functionality.

## Implementation Notes

- The endpoint validates the request but doesn't directly update passwords
- Actual password updates should be handled by Firebase Authentication on the frontend
- The backend logs all password reset attempts for audit purposes
- Firebase UID verification can be enhanced based on security requirements

## Security Considerations

- Always validate Firebase UID authenticity in production
- Implement rate limiting for password reset attempts
- Consider adding additional verification steps for sensitive operations
- Ensure proper error handling to prevent information disclosure