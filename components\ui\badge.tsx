import type * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { useColorThemeContext } from "@/contexts/color-theme-context"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent text-white",
        secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
)

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, style, ...props }: BadgeProps) {
  const { currentTheme } = useColorThemeContext()
  const primaryColor = currentTheme.primary
  
  const getDefaultStyle = () => {
    if (variant === 'default') {
      return {
        backgroundColor: primaryColor,
        ...style
      }
    }
    return style
  }
  
  return (
    <div 
      className={cn(badgeVariants({ variant }), className)} 
      style={getDefaultStyle()}
      onMouseEnter={(e) => {
        if (variant === 'default') {
          e.currentTarget.style.backgroundColor = `${primaryColor}CC`
        }
      }}
      onMouseLeave={(e) => {
        if (variant === 'default') {
          e.currentTarget.style.backgroundColor = primaryColor
        }
      }}
      {...props} 
    />
  )
}

export { Badge, badgeVariants }
