# AWS Amplify Deployment Guide

## Node.js Version Requirements

This project requires **Node.js 20+** as specified in `package.json` engines field.

AWS Amplify will automatically use Node.js 20 based on the `.nvmrc` file and `amplify.yml` configuration.

## Build Configuration

### Files included for AWS Amplify:

1. **`.nvmrc`** - Specifies Node.js version 20
2. **`.npmrc`** - Enables legacy peer dependency resolution
3. **`amplify.yml`** - AWS Amplify build configuration
4. **`package.json`** - Updated with Node.js engine requirements

### Build Process

The build process on AWS Amplify will:

1. Use Node.js 20 (as specified in `.nvmrc`)
2. Install dependencies with `npm ci --legacy-peer-deps`
3. Build the application with `npm run build`
4. Deploy the `.next` directory

## Key Features

- ✅ **React 19 Compatible** - All dependencies updated for React 19
- ✅ **SEO Optimized** - Structured data for all products
- ✅ **Node.js 20+ Ready** - Future-proof deployment
- ✅ **Dependency Conflict Resolved** - Uses legacy peer deps for compatibility

## Environment Variables

Make sure to set these in AWS Amplify Console:

- `NEXT_PUBLIC_ADMIN_BASE_URL`
- `API_KEY` (Firebase)
- `AUTH_DOMAIN` (Firebase)
- `PROJECT_ID` (Firebase)
- `APP_ID` (Firebase)

## Troubleshooting

If you encounter build issues:

1. Ensure Node.js 20+ is being used
2. Check that `.npmrc` file exists with `legacy-peer-deps=true`
3. Verify all environment variables are set
4. Check the build logs for specific dependency conflicts

## Local Development

```bash
# Install dependencies
npm install --legacy-peer-deps

# Run development server
npm run dev

# Build for production
npm run build
```

## Deployment Status

- ✅ Node.js 20+ compatible
- ✅ React 19 ready
- ✅ AWS Amplify optimized
- ✅ Structured data implemented
- ✅ All dependency conflicts resolved