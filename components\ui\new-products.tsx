'use client';

import { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import ProductCard from '@/components/product-card';
import { Config } from '@/lib/config';

// Updated interface to match ProductCard expectations
interface Product {
  ProductId: number;
  ProductName: string;
  Price: number;
  OldPrice?: number;
  DiscountPrice?: number;
  Rating: number;
  ProductImageUrl?: string;
  CategoryName: string;
  StockQuantity: number;
  ProductTypeName?: string;
  IQDPrice?: number;
  IsDiscountAllowed?: boolean;
  MarkAsNew?: boolean;
  SellStartDatetimeUTC?: string;
  SellEndDatetimeUTC?: string;
}

interface Collection {
  nameEn: string;
  nameAr: string;
}

interface NewProductsProps {
  effect?: string;
}

const collections: Collection[] = [
  {
    nameEn: 'new products',
    nameAr: 'منتجات جديدة'
  },
  {
    nameEn: 'on sale',
    nameAr: 'للبيع'
  },
  {
    nameEn: 'hotdeal',
    nameAr: 'صفقة حاسمة'
  },
  {
    nameEn: 'best sellers',
    nameAr: 'أفضل البائعين'
  }
];

export function NewProducts({ effect }: NewProductsProps) {
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const destructiveColor = '#dc2626'; // Red color for error states
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      // Prepare parameters and headers according to API requirements
      const param = {
        "requestParameters": {
          "PageNo": 1,
          "PageSize": 100,
          "recordValueJson": "[]"
        }
      };

      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      // Import MakeApiCallAsync from api-helper
      const { MakeApiCallAsync } = await import('@/lib/api-helper');

      // Use the API helper to make the call
      const response = await MakeApiCallAsync(
        'get-recents-products-list',
        null,
        param,
        headers,
        "POST",
        true
      );

      // Process the response
      if (response?.data?.data) {
        try {
          const parsedData = JSON.parse(response.data.data);
          console.log('New products data:', parsedData);

          if (Array.isArray(parsedData)) {
            // Transform the API data to match ProductCard interface
            const transformedProducts = parsedData.map(item => {
              // Process image URL similar to products page
              const rawUrl = item.ProductImagesUrl || item.ProductImageUrl;
              let imageUrl = null;

              try {
                if (rawUrl) {
                  let cleanUrl = rawUrl;

                  if (typeof rawUrl === 'string' && (rawUrl.startsWith('[') || rawUrl.startsWith('"'))) {
                    try {
                      const parsed = JSON.parse(rawUrl);
                      if (Array.isArray(parsed) && parsed.length > 0) {
                        cleanUrl = parsed[0].AttachmentURL || parsed[0];
                      } else if (typeof parsed === 'string') {
                        cleanUrl = parsed;
                      }
                    } catch (jsonError) {
                      cleanUrl = rawUrl.replace(/^"|"/g, '');
                    }
                  }

                  if (typeof cleanUrl === 'string' && cleanUrl.trim() !== '') {
                    cleanUrl = cleanUrl.replace(/^"|"$/g, '').trim();

                    if (cleanUrl) {
                      const decodedUrl = decodeURIComponent(cleanUrl);
                      const normalizedUrl = decodedUrl.startsWith('/') || decodedUrl.startsWith('http') 
                        ? decodedUrl 
                        : `/${decodedUrl}`;

                      imageUrl = normalizedUrl.startsWith('http') 
                        ? normalizedUrl 
                        : `${Config.ADMIN_BASE_URL}${normalizedUrl}`;
                    }
                  }
                }
              } catch (error) {
                console.error('Error processing URL for product', item.ProductId, ':', error);
              }

              return {
                ProductId: item.ProductId || item.ProductID || 0,
                ProductName: item.ProductName || 'Unnamed Product',
                Price: parseFloat(item.Price) || 0,
                OldPrice: item.OldPrice ? parseFloat(item.OldPrice) : undefined,
                DiscountPrice: item.DiscountPrice ? parseFloat(item.DiscountPrice) : undefined,
                Rating: parseFloat(item.Rating) || 0,
                ProductImageUrl: imageUrl || undefined,
                CategoryName: item.CategoryName || 'Uncategorized',
                StockQuantity: parseInt(item.StockQuantity, 10) || 0,
                ProductTypeName: item.ProductTypeName,
                IQDPrice: parseFloat(item.IQDPrice) || undefined,
                IsDiscountAllowed: Boolean(item.IsDiscountAllowed),
                MarkAsNew: Boolean(item.MarkAsNew),
                SellStartDatetimeUTC: item.SellStartDatetimeUTC,
                SellEndDatetimeUTC: item.SellEndDatetimeUTC
              };
            });

            setProducts(transformedProducts);
          } else {
            console.error('Products data is not an array:', parsedData);
            setProducts([]);
            setErrorMessage('Invalid data format received from server');
          }
        } catch (parseError) {
          console.error('Error parsing products data:', parseError);
          setProducts([]);
          setErrorMessage('Error processing product data');
        }
      } else if (response?.data?.errorMessage) {
        console.error('API Error:', response.data.errorMessage);
        setProducts([]);
        setErrorMessage(response.data.errorMessage || 'An error occurred while fetching products');
      } else {
        console.error('Invalid or empty response from API');
        setProducts([]);
        setErrorMessage('No data received from server');
      }
    } catch (error: unknown) {
      console.error('Error fetching products:', error);
      setProducts([]);

      if (error && typeof error === 'object' && 'message' in error) {
        setErrorMessage((error as Error).message);
      } else {
        setErrorMessage('An unexpected error occurred while fetching products');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  if (errorMessage) {
    return (
      <div className="w-full p-4 mb-4 text-center">
        <div className="p-4 rounded-md" style={{ backgroundColor: `${primaryColor}10`, border: `1px solid ${primaryColor}40` }}>
          <p style={{ color: destructiveColor }}>{errorMessage}</p>
          <button
            onClick={() => {
              setErrorMessage(null);
              fetchProducts();
            }}
            className="mt-2 px-4 py-2 rounded-md text-sm transition-colors"
            style={{ 
              backgroundColor: `${destructiveColor}20`, 
              color: destructiveColor
            }}
            onMouseEnter={(e) => (e.target as HTMLButtonElement).style.backgroundColor = `${primaryColor}30`}
            onMouseLeave={(e) => (e.target as HTMLButtonElement).style.backgroundColor = `${primaryColor}20`}
          >
            {t('tryAgain')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Display three products in a line */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 sm:gap-6">
        {loading ? (
          // Skeleton loading state - show 6 skeletons initially
          Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow overflow-hidden">
              <div className="aspect-square">
                <Skeleton className="h-full w-full" />
              </div>
              <div className="p-4 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-6 w-1/3" />
              </div>
              <div className="p-4 pt-0">
                <div className="flex w-full gap-2">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-10" />
                </div>
              </div>
            </div>
          ))
        ) : (
          // Actual products - show all products returned by API
          products.length > 0 ? (
            products.map((product) => (
              <ProductCard
                key={product.ProductId}
                product={product}
              />
            ))
          ) : (
            <div className="col-span-full text-center py-8">
              <p className="text-muted-foreground">{t('noProductsFound')}</p>
            </div>
          )
        )}
      </div>
    </div>
  );
}