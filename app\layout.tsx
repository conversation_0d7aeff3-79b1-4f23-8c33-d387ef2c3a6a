import './globals.css';
import React from 'react';
import { Inter } from 'next/font/google';
import { Metadata } from 'next';
import { generateCanonicalUrl, generateMetaWithCanonical } from '@/lib/canonical-utils';
import { ClientLayout } from '@/components/client-layout';

const inter = Inter({ subsets: ['latin'] });

// Generate metadata for the root layout
export const metadata: Metadata = generateMetaWithCanonical(
  generateCanonicalUrl(''),
  'Code Medical Apps - Medical Courses & Resources',
  'Professional medical courses, ebooks, printed books and medical accounts for medical field staff worldwide.',
  {
    keywords: 'medical courses, medical books, healthcare education, medical training, medical resources',
    authors: [{ name: 'Code Medical Apps' }],
    creator: 'Code Medical Apps',
    publisher: 'Code Medical Apps',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL('https://www.codemedicalapps.com'),
    openGraph: {
      type: 'website',
      locale: 'en_US',
      siteName: 'Code Medical Apps',
      images: [
        {
          url: '/og-image.jpg',
          width: 1200,
          height: 630,
          alt: 'Code Medical Apps - Medical Education Platform',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      site: '@codemedicalapps',
      creator: '@codemedicalapps',
    },
    verification: {
      google: process.env.GOOGLE_SITE_VERIFICATION,
    },
  }
);

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        {/* Favicon and theme */}
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <meta name="theme-color" content="#0074b2" />
        <meta name="msapplication-TileColor" content="#0074b2" />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}