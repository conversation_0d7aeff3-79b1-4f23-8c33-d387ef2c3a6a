/**
 * Security utilities for SMS verification
 */

import crypto from 'crypto';

/**
 * Validate phone number format and detect suspicious patterns
 */
export function validatePhoneNumber(phoneNumber: string): { valid: boolean; reason?: string } {
  // Remove all non-digit characters except +
  const cleaned = phoneNumber.replace(/[^\d+]/g, '');
  
  // Basic format validation
  if (!/^\+[1-9]\d{9,14}$/.test(cleaned)) {
    return { valid: false, reason: 'Invalid phone number format' };
  }

  // Check for suspicious patterns
  const digits = cleaned.replace(/^\+/, '');
  
  // Check for repeated digits (like +1111111111)
  if (/^(\d)\1{9,}$/.test(digits)) {
    return { valid: false, reason: 'Suspicious phone number pattern' };
  }

  // Check for sequential digits (like +1234567890)
  const isSequential = digits.split('').every((digit, index) => {
    if (index === 0) return true;
    return parseInt(digit) === parseInt(digits[index - 1]) + 1;
  });

  if (isSequential) {
    return { valid: false, reason: 'Suspicious phone number pattern' };
  }

  return { valid: true };
}

/**
 * Generate a secure verification code
 */
export function generateSecureCode(length: number = 6): string {
  const digits = '0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, digits.length);
    result += digits[randomIndex];
  }
  
  return result;
}

/**
 * Hash a phone number for storage (privacy protection)
 */
export function hashPhoneNumber(phoneNumber: string): string {
  return crypto.createHash('sha256').update(phoneNumber).digest('hex');
}

/**
 * Detect if request is from a bot based on headers and patterns
 */
export function detectBot(headers: Headers): { isBot: boolean; reason?: string } {
  const userAgent = headers.get('user-agent')?.toLowerCase() || '';
  const referer = headers.get('referer');
  
  // Common bot user agents
  const botPatterns = [
    'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget', 'python', 'java',
    'postman', 'insomnia', 'httpie', 'axios', 'fetch'
  ];

  for (const pattern of botPatterns) {
    if (userAgent.includes(pattern)) {
      return { isBot: true, reason: `Bot detected: ${pattern}` };
    }
  }

  // Check for missing referer (suspicious for web requests)
  if (!referer && !userAgent.includes('mobile')) {
    return { isBot: true, reason: 'Missing referer header' };
  }

  // Check for suspicious user agent patterns
  if (userAgent.length < 10 || !userAgent.includes('mozilla')) {
    return { isBot: true, reason: 'Suspicious user agent' };
  }

  return { isBot: false };
}

/**
 * Validate request origin
 */
export function validateOrigin(headers: Headers): { valid: boolean; reason?: string } {
  const origin = headers.get('origin');
  const referer = headers.get('referer');
  
  const allowedOrigins = [
    'http://localhost:3000',
    'https://codemedicalapps.com',
    'https://www.codemedicalapps.com'
  ];

  // Check origin header
  if (origin && !allowedOrigins.includes(origin)) {
    return { valid: false, reason: 'Invalid origin' };
  }

  // Check referer header
  if (referer) {
    const refererUrl = new URL(referer);
    const refererOrigin = `${refererUrl.protocol}//${refererUrl.host}`;
    
    if (!allowedOrigins.includes(refererOrigin)) {
      return { valid: false, reason: 'Invalid referer' };
    }
  }

  return { valid: true };
}

/**
 * Check if IP address is suspicious
 */
export function checkSuspiciousIP(ip: string): { suspicious: boolean; reason?: string } {
  // List of known suspicious IP ranges (you can expand this)
  const suspiciousRanges = [
    // Add known VPN/proxy ranges here
    // Example: '10.0.0.0/8', '***********/16'
  ];

  // Check for localhost/private IPs in production
  if (process.env.NODE_ENV === 'production') {
    if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.')) {
      return { suspicious: true, reason: 'Private IP in production' };
    }
  }

  return { suspicious: false };
}

/**
 * Generate a CSRF token for additional security
 */
export function generateCSRFToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Verify CSRF token
 */
export function verifyCSRFToken(token: string, expectedToken: string): boolean {
  return crypto.timingSafeEqual(Buffer.from(token), Buffer.from(expectedToken));
}