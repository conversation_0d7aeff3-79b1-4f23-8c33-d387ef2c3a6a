'use client';

import React from 'react';
import { B<PERSON><PERSON>rumb, Bread<PERSON>rumbI<PERSON>, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Card } from '@/components/ui/card';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { Facebook } from 'lucide-react';
import WhatsAppIcon from '@/components/icons/WhatsAppIcon';
import MessengerIcon from '@/components/icons/MessengerIcon';
import TelegramIcon from '@/components/icons/TelegramIcon';

export default function FollowUsPage() {
  const { t } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;

  const socialLinks = [
    {
      name: 'WhatsApp',
      Icon: WhatsAppIcon,
      url: 'https://wa.me/9647836071686',
      description: 'Chat with us on WhatsApp'
    },
    {
      name: 'Messenger',
      Icon: MessengerIcon,
      url: 'https://m.me/259317334778090',
      description: 'Message us on Facebook Messenger'
    },
    {
      name: 'Facebook',
      Icon: Facebook,
      url: 'https://www.facebook.com/codemedicalapps/',
      description: 'Follow us on Facebook'
    },
    {
      name: 'Telegram',
      Icon: TelegramIcon,
      url: 'https://t.me/codemedicalapps',
      description: 'Join our Telegram channel'
    }
  ];

  return (
    <div className="container mx-auto py-4 sm:py-8 px-2 sm:px-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-4 sm:mb-6 text-sm sm:text-base">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Follow Us</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl sm:text-3xl font-bold mb-6 sm:mb-8 text-center">Follow Us</h1>

        {/* Message */}
        <Card className="p-4 sm:p-6 mb-6 sm:mb-8 text-center">
          <p className="text-base sm:text-lg text-muted-foreground">
            Your message is always our highest priority and we highly appreciate your valuable time - our support team will reply to your message as soon as possible.
          </p>
        </Card>

        {/* Social Media Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
          {socialLinks.map((link) => (
            <Card key={link.name} className="p-4 sm:p-6 hover:shadow-lg transition-shadow">
              <a
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex flex-col sm:flex-row items-center sm:items-start text-center sm:text-left gap-3 sm:gap-4"
              >
                <div
                  className="w-12 h-12 sm:w-14 sm:h-14 rounded-full flex items-center justify-center mb-2 sm:mb-0"
                  style={{ backgroundColor: `${primaryColor}20` }}
                >
                  <link.Icon
                    className="h-6 w-6 sm:h-7 sm:w-7"
                    style={{ color: primaryColor }}
                  />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg sm:text-xl font-semibold mb-1 sm:mb-2">{link.name}</h3>
                  <p className="text-sm sm:text-base text-muted-foreground">{link.description}</p>
                </div>
              </a>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}