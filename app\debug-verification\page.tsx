'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function DebugVerificationPage() {
  const [phoneNumber, setPhoneNumber] = useState('+9647858021300');
  const [verificationCode, setVerificationCode] = useState('123456');
  const [result, setResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const testDirectBackend = async () => {
    setIsLoading(true);
    setResult('Testing direct backend...');
    
    try {
      const testData = {
        PhoneNumber: phoneNumber,
        VerificationCode: verificationCode,
        ExpirationMinutes: 10,
        IPAddress: '127.0.0.1',
        UserAgent: 'Debug-Test'
      };

      const backendUrl = 'https://admin.codemedicalapps.com/api/v1/verification/store';
      console.log('📤 Testing direct backend:', backendUrl);
      console.log('📤 Request data:', testData);

      const response = await fetch(backendUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
      });

      console.log('📥 Response status:', response.status);
      console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

      const responseData = await response.text();
      console.log('📥 Response body:', responseData);

      if (response.ok) {
        try {
          const jsonData = JSON.parse(responseData);
          setResult(`✅ DIRECT BACKEND SUCCESS:\n${JSON.stringify(jsonData, null, 2)}`);
        } catch {
          setResult(`✅ DIRECT BACKEND SUCCESS (non-JSON):\n${responseData}`);
        }
      } else {
        setResult(`❌ DIRECT BACKEND FAILED:\nStatus: ${response.status}\nResponse: ${responseData}`);
      }

    } catch (error) {
      console.error('❌ Error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setResult(`❌ DIRECT BACKEND ERROR: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testFrontendAPI = async () => {
    setIsLoading(true);
    setResult('Testing frontend API...');
    
    try {
      const testData = {
        phoneNumber: phoneNumber,
        verificationCode: verificationCode,
        expirationMinutes: 10
      };

      console.log('📤 Testing frontend API: /api/verification/store');
      console.log('📤 Request data:', testData);

      const response = await fetch('/api/verification/store', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
      });

      console.log('📥 Response status:', response.status);
      console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

      const responseData = await response.text();
      console.log('📥 Response body:', responseData);

      if (response.ok) {
        try {
          const jsonData = JSON.parse(responseData);
          setResult(`✅ FRONTEND API SUCCESS:\n${JSON.stringify(jsonData, null, 2)}`);
        } catch {
          setResult(`✅ FRONTEND API SUCCESS (non-JSON):\n${responseData}`);
        }
      } else {
        setResult(`❌ FRONTEND API FAILED:\nStatus: ${response.status}\nResponse: ${responseData}`);
      }

    } catch (error) {
      console.error('❌ Error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setResult(`❌ FRONTEND API ERROR: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSignupFlow = async () => {
    setIsLoading(true);
    setResult('Testing signup flow...');
    
    try {
      const testData = {
        phoneNumber: phoneNumber,
        useWhatsApp: true
      };

      console.log('📤 Testing signup flow: /api/sms/send-verification');
      console.log('📤 Request data:', testData);

      const response = await fetch('/api/sms/send-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
      });

      console.log('📥 Response status:', response.status);
      console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

      const responseData = await response.text();
      console.log('📥 Response body:', responseData);

      if (response.ok) {
        try {
          const jsonData = JSON.parse(responseData);
          setResult(`✅ SIGNUP FLOW SUCCESS:\n${JSON.stringify(jsonData, null, 2)}`);
        } catch {
          setResult(`✅ SIGNUP FLOW SUCCESS (non-JSON):\n${responseData}`);
        }
      } else {
        setResult(`❌ SIGNUP FLOW FAILED:\nStatus: ${response.status}\nResponse: ${responseData}`);
      }

    } catch (error) {
      console.error('❌ Error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setResult(`❌ SIGNUP FLOW ERROR: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">Verification Debug Tool</h1>
      
      <div className="space-y-6">
        {/* Test Configuration */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Test Configuration</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                placeholder="+9647858021300"
              />
            </div>
            <div>
              <Label htmlFor="code">Verification Code</Label>
              <Input
                id="code"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                placeholder="123456"
              />
            </div>
          </div>
        </Card>

        {/* Test Buttons */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Tests</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              onClick={testDirectBackend}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? 'Testing...' : 'Test Direct Backend'}
            </Button>
            
            <Button
              onClick={testFrontendAPI}
              disabled={isLoading}
              className="w-full"
              variant="outline"
            >
              {isLoading ? 'Testing...' : 'Test Frontend API'}
            </Button>
            
            <Button
              onClick={testSignupFlow}
              disabled={isLoading}
              className="w-full"
              variant="secondary"
            >
              {isLoading ? 'Testing...' : 'Test Signup Flow'}
            </Button>
          </div>
        </Card>

        {/* Results */}
        {result && (
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto whitespace-pre-wrap">
              {result}
            </pre>
          </Card>
        )}

        {/* Environment Info */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Environment Information</h2>
          <div className="space-y-2 text-sm">
            <p><strong>NEXT_PUBLIC_ADMIN_BASE_URL:</strong> {process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'Not set'}</p>
            <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.origin : 'Server-side'}</p>
          </div>
        </Card>

        {/* Instructions */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Debug Instructions</h2>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>Open browser console (F12) to see detailed logs</li>
            <li>Test "Direct Backend" first to verify backend API works</li>
            <li>Test "Frontend API" to check if the issue is in the API route</li>
            <li>Test "Signup Flow" to check the complete signup process</li>
            <li>Check the console logs for detailed error information</li>
          </ol>
        </Card>
      </div>
    </div>
  );
}
