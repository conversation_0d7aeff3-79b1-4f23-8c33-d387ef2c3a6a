'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import Link from 'next/link';
import { useUser } from '@/contexts/user-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';

import { MapPin, Plus, Edit, Trash2, Home, Building, ArrowLeft, CreditCard, Package, Mail } from 'lucide-react';

interface Address {
  AddressID: number;
  AddressTypeID: number;
  AddressTypeName?: string;
  UserID: number;
  AddressLineOne: string;
  AddressLineTwo: string;
  CountryID: number;
  CountryName: string;
  CityID?: number;
  StateProvinceID?: number;
  PostalCode?: string;
  IsActive: boolean;
  CreatedOn: string;
  CreatedBy: number;
  ModifiedOn?: string;
  ModifiedBy?: number;
}

interface Country {
  CountryID: number;
  CountryName: string;
}

interface AddressType {
  AddressTypeID: number;
  AddressTypeName: string;
  IsActive: boolean;
}

export default function AddressesPage() {
  const { user, isLoggedIn, isLoading, token } = useUser();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  const router = useRouter();
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [cities, setCities] = useState<any[]>([]);
  const [addressTypes, setAddressTypes] = useState<AddressType[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [loadingCities, setLoadingCities] = useState(false);
  const [loadingAddressTypes, setLoadingAddressTypes] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [accordionValue, setAccordionValue] = useState<string>('');
  const [formData, setFormData] = useState({
    AddressLineOne: '',
    AddressLineTwo: '',
    CountryID: '107', // Default to Iraq
    CityID: '',
    StateProvinceID: '',
    PostalCode: '',
    AddressTypeID: 1
  });

  // Add dynamic styles for primary color
  useEffect(() => {
    const style = document.createElement('style');
    if (!document.querySelector('#addresses-primary-dynamic-style')) {
      style.id = 'addresses-primary-dynamic-style';
      style.textContent = `
        .bg-primary-dynamic { background-color: ${primaryColor} !important; }
        .bg-primary-dynamic:hover { background-color: ${primaryColor}dd !important; }
      `;
      document.head.appendChild(style);
    }
    return () => {
      const existingStyle = document.querySelector('#addresses-primary-dynamic-style');
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, [primaryColor]);

  // FUNCTION DEFINITIONS (before useEffect that calls them)
  const fetchAddressTypes = async () => {
    console.log('🏠 Fetching address types from API');
    setLoadingAddressTypes(true);
    try {
      const response = await fetch('https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-address-type', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          requestParameters: {
            recordValueJson: "[]"
          }
        })
      });

      console.log('🏠 Address types response status:', response.status);
      const data = await response.json();
      console.log('🏠 Address types API Response:', data);
      
      if (data.statusCode === 200 && data.data) {
        const addressTypesData = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
        console.log('🏠 Parsed address types data:', addressTypesData);
        setAddressTypes(addressTypesData || []);
      } else {
        console.log('🏠 No address types data or error:', data);
        setAddressTypes([]);
      }
    } catch (error) {
      console.error('Error fetching address types:', error);
      setAddressTypes([]);
    } finally {
      setLoadingAddressTypes(false);
    }
  };

  const fetchAddresses = async () => {
    console.log('🏠 Fetching addresses with token:', token ? 'exists' : 'missing');
    try {
      const response = await fetch('/api/addresses/get-user-addresses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          requestParameters: {
            recordValueJson: "[]"
          }
        })
      });

      console.log('🏠 Fetch response status:', response.status);

      const data = await response.json();
      console.log('🏠 Addresses API Response:', data);
      
      // Handle expired token
      if (data.code === 'TOKEN_EXPIRED' || data.error === 'Token is expired') {
        console.log('🏠 Token expired, redirecting to login');
        router.push('/login?redirect=/addresses&reason=token_expired');
        return;
      }
      
      if (data.statusCode === 200 && data.data) {
        const addressesData = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
        console.log('🏠 Parsed addresses data:', addressesData);
        setAddresses(addressesData || []);
        
        // Fetch cities for all unique countries in addresses
        const uniqueCountryIds = Array.from(new Set(addressesData.map((addr: Address) => addr.CountryID))) as number[];
        for (let i = 0; i < uniqueCountryIds.length; i++) {
          const countryId = uniqueCountryIds[i];
          await fetchCities(countryId, i > 0); // Append cities for countries after the first one
        }
      } else {
        console.log('🏠 No addresses data or error:', data);
        setAddresses([]);
      }
    } catch (error) {
      console.error('Error fetching addresses:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCountries = async () => {
    setLoadingCountries(true);
    try {
      const response = await fetch("/api/countries", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          requestParameters: {
            recordValueJson: "[]",
          },
        }),
      });

      const data = await response.json();
      if (data && data.data) {
        const parsedData = JSON.parse(data.data);
        if (Array.isArray(parsedData)) {
          setCountries(parsedData);
        }
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
    } finally {
      setLoadingCountries(false);
    }
  };

  const fetchCities = async (countryId: number, appendToCities = false) => {
    setLoadingCities(true);
    if (!appendToCities) {
      setCities([]); // Clear previous cities only if not appending
    }
    try {
      const response = await fetch("/api/cities", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          requestParameters: {
            StateProvinceId: null,
            CountryId: countryId,
            recordValueJson: "[]",
          },
        }),
      });

      const data = await response.json();
      if (data && data.data) {
        const parsedData = JSON.parse(data.data);
        if (Array.isArray(parsedData)) {
          if (appendToCities) {
            setCities(prevCities => {
              // Avoid duplicates by filtering out cities that already exist
              const existingCityIds = prevCities.map(city => city.CityID);
              const newCities = parsedData.filter(city => !existingCityIds.includes(city.CityID));
              return [...prevCities, ...newCities];
            });
          } else {
            setCities(parsedData);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching cities:", error);
    } finally {
      setLoadingCities(false);
    }
  };

  // ALL HOOKS MUST BE CALLED BEFORE ANY CONDITIONAL RETURNS
  
  // Redirect to login if not authenticated (only after loading is complete)
  useEffect(() => {
    if (!isLoading && !isLoggedIn) {
      router.push('/login?redirect=/addresses');
    }
  }, [isLoading, isLoggedIn, router]);

  // Fetch addresses
  useEffect(() => {
    if (token) {
      fetchAddresses();
      fetchCountries();
      fetchAddressTypes();
      // Auto-fetch cities for default country (Iraq - CountryID: 107)
      fetchCities(107);
    }
  }, [token]);

  // CONDITIONAL RETURNS AFTER ALL HOOKS
  
  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="text-lg text-muted-foreground mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  // Show login message if not authenticated (after loading is complete)
  if (!isLoggedIn) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-center">
            <p className="text-lg text-muted-foreground">
              Redirecting to login...
            </p>
          </div>
        </div>
      </div>
    );
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // If country changes, fetch cities and reset city selection
    if (name === 'CountryID' && value) {
      fetchCities(parseInt(value));
      setFormData(prev => ({ ...prev, CityID: '' })); // Reset city when country changes
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const endpoint = editingAddress ? '/api/addresses/update-address' : '/api/addresses/insert-address';
      const payload = {
        requestParameters: {
          ...formData,
          CountryID: parseInt(formData.CountryID),
          CityID: formData.CityID ? parseInt(formData.CityID) : 1,
          StateProvinceID: formData.StateProvinceID ? parseInt(formData.StateProvinceID) : 1,
          AddressTypeID: parseInt(formData.AddressTypeID.toString()),
          ...(editingAddress && { 
            AddressID: editingAddress.AddressID,
            ModifiedBy: user?.UserId || user?.UserID || 1
          }),
          recordValueJson: "[]"
        }
      };

      console.log('🏠 Submitting address with type:', payload.requestParameters.AddressTypeID, getAddressTypeName(payload.requestParameters.AddressTypeID));

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(payload)
      });

      const data = await response.json();
      if (data.statusCode === 200) {
        // Show success message
        const Swal = (await import('sweetalert2')).default;
        await Swal.fire({
          icon: 'success',
          title: editingAddress ? 'Address Updated!' : 'Address Added!',
          text: `Your address has been ${editingAddress ? 'updated' : 'added'} successfully`,
          timer: 2000,
          showConfirmButton: false
        });

        // Reset form and close
        if (editingAddress) {
          setShowAddForm(false);
        } else {
          // Close accordion by resetting its value
          setAccordionValue('');
        }
        setEditingAddress(null);
        setFormData({
          AddressLineOne: '',
          AddressLineTwo: '',
          CountryID: '107', // Default to Iraq
          CityID: '',
          StateProvinceID: '',
          PostalCode: '',
          AddressTypeID: 1
        });
        // Fetch cities for default country (Iraq)
        fetchCities(107);
        fetchAddresses();
      }
    } catch (error) {
      console.error('Error saving address:', error);
      const Swal = (await import('sweetalert2')).default;
      await Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to save address. Please try again.',
        timer: 3000,
        showConfirmButton: false
      });
    }
  };

  const handleEdit = (address: Address) => {
    console.log('🏠 Editing address with type:', address.AddressTypeID, getAddressTypeName(address.AddressTypeID));
    setEditingAddress(address);
    setFormData({
      AddressLineOne: address.AddressLineOne,
      AddressLineTwo: address.AddressLineTwo,
      CountryID: address.CountryID.toString(),
      CityID: address.CityID?.toString() || '',
      StateProvinceID: address.StateProvinceID?.toString() || '',
      PostalCode: address.PostalCode || '',
      AddressTypeID: address.AddressTypeID
    });
    
    // Fetch cities for the selected country when editing
    if (address.CountryID) {
      fetchCities(address.CountryID);
    }
    
    setShowAddForm(true);
  };

  const handleDelete = async (addressId: number) => {
    const Swal = (await import('sweetalert2')).default;
    const result = await Swal.fire({
      title: 'Delete Address?',
      text: 'Are you sure you want to delete this address?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!'
    });

    if (result.isConfirmed) {
      try {
        // Note: You may need to implement a delete endpoint
        // For now, we'll show a placeholder
        await Swal.fire({
          icon: 'info',
          title: 'Delete functionality',
          text: 'Delete endpoint needs to be implemented',
          timer: 2000,
          showConfirmButton: false
        });
      } catch (error) {
        console.error('Error deleting address:', error);
      }
    }
  };

  const getAddressTypeIcon = (typeId: number) => {
    switch (typeId) {
      case 1:
        return <Home className="h-4 w-4" />;
      case 2:
        return <CreditCard className="h-4 w-4" />;
      case 3:
        return <Package className="h-4 w-4" />;
      case 4:
        return <Mail className="h-4 w-4" />;
      default:
        return <MapPin className="h-4 w-4" />;
    }
  };

  const getAddressTypeName = (typeId: number) => {
    const addressType = addressTypes.find(type => type.AddressTypeID === typeId);
    return addressType ? addressType.AddressTypeName : 'Unknown';
  };

  const getCityName = (cityId: number | undefined) => {
    if (!cityId) return '';
    const city = cities.find(city => city.CityID === cityId);
    return city ? (city.CityName || city.Name) : '';
  };

  // Handle accordion open to initialize form data
  const handleAccordionOpen = (value: string) => {
    setAccordionValue(value);
    if (value === "add-address") {
      // Reset form data when opening accordion
      setFormData({
        AddressLineOne: '',
        AddressLineTwo: '',
        CountryID: '107', // Default to Iraq
        CityID: '',
        StateProvinceID: '',
        PostalCode: '',
        AddressTypeID: 1
      });
      setEditingAddress(null);
      
      // Fetch required data if not already loaded
      if (countries.length === 0) {
        fetchCountries();
      }
      if (addressTypes.length === 0) {
        fetchAddressTypes();
      }
      // Fetch cities for default country (Iraq)
      fetchCities(107);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/account">Account</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Addresses</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <Button variant="outline" className="mb-6" asChild>
        <Link href="/account">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Account
        </Link>
      </Button>

      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">My Addresses</h1>
        </div>

        {/* Add New Address Accordion */}
        <div className="mb-6">
          <Accordion type="single" collapsible className="w-full" value={accordionValue} onValueChange={handleAccordionOpen}>
            <AccordionItem value="add-address" className="border-none">
              <AccordionTrigger className="py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 text-primary shadow-sm hover:shadow group" style={{ background: `linear-gradient(to right, ${primaryColor}10, ${primaryColor}08)` }}>
                <div className="flex items-center gap-2">
                  <Plus className="w-5 h-5 text-primary group-hover:scale-110 transition-transform duration-300" />
                  <span className="font-medium">Add New Address</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pt-4 px-4 pb-2 rounded-b-lg border border-t-0" style={{ background: `linear-gradient(to bottom, ${primaryColor}08, transparent)`, borderColor: `${primaryColor}40` }}>
                <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="AddressLineOne">Address Line 1 *</Label>
                    <Input
                      id="AddressLineOne"
                      name="AddressLineOne"
                      value={formData.AddressLineOne}
                      onChange={handleInputChange}
                      placeholder="Enter street address"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="AddressLineTwo">Address Line 2</Label>
                    <Input
                      id="AddressLineTwo"
                      name="AddressLineTwo"
                      value={formData.AddressLineTwo}
                      onChange={handleInputChange}
                      placeholder="Apartment, suite, etc."
                    />
                  </div>
                  <div>
                    <Label htmlFor="CountryID">Country *</Label>
                    <Select 
                      value={formData.CountryID} 
                      onValueChange={(value) => handleSelectChange('CountryID', value)}
                      disabled={loadingCountries}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={loadingCountries ? "Loading countries..." : "Select country"} />
                      </SelectTrigger>
                      <SelectContent className="max-h-[200px] bg-white">
                        {countries.length > 0 ? (
                          countries.map((country) => (
                            <SelectItem 
                              key={country.CountryID} 
                              value={country.CountryID.toString()}
                              className="text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100"
                            >
                              {country.CountryName}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-sm text-gray-500">
                            {loadingCountries ? "Loading countries..." : "No countries found"}
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="CityID">City</Label>
                    <Select
                      value={formData.CityID}
                      onValueChange={(value) => handleSelectChange('CityID', value)}
                      disabled={!formData.CountryID || loadingCities}
                    >
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            !formData.CountryID
                              ? "Select country first"
                              : loadingCities
                              ? "Loading cities..."
                              : "Select city"
                          }
                        />
                      </SelectTrigger>
                      <SelectContent className="max-h-[200px] bg-white">
                        {!formData.CountryID ? (
                          <div className="p-2 text-sm text-gray-500">
                            Select country first
                          </div>
                        ) : cities.length > 0 ? (
                          cities.map((city) => (
                            <SelectItem
                              key={city.CityID}
                              value={city.CityID.toString()}
                              className="text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100"
                            >
                              {city.CityName || city.Name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-sm text-gray-500">
                            {loadingCities
                              ? "Loading cities..."
                              : "No cities found"}
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="PostalCode">Postal Code</Label>
                    <Input
                      id="PostalCode"
                      name="PostalCode"
                      value={formData.PostalCode}
                      onChange={handleInputChange}
                      placeholder="Enter postal code"
                    />
                  </div>
                  <div>
                    <Label htmlFor="AddressTypeID">Address Type</Label>
                    <Select 
                      value={formData.AddressTypeID.toString()} 
                      onValueChange={(value) => handleSelectChange('AddressTypeID', value)}
                      disabled={loadingAddressTypes}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={loadingAddressTypes ? "Loading address types..." : "Select address type"} />
                      </SelectTrigger>
                      <SelectContent className="max-h-[200px] bg-white">
                        {loadingAddressTypes ? (
                          <div className="p-2 text-sm text-gray-500">
                            Loading address types...
                          </div>
                        ) : addressTypes.length > 0 ? (
                          addressTypes.filter(addressType => addressType && addressType.AddressTypeID).map((addressType) => (
                            <SelectItem
                              key={addressType.AddressTypeID}
                              value={addressType.AddressTypeID.toString()}
                              className="text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100"
                            >
                              {addressType.AddressTypeName}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-sm text-gray-500">
                            No address types available
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex gap-3">
                  <Button type="submit" className="bg-primary-dynamic text-white">
                    Add Address
                  </Button>
                </div>
              </form>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>

        {/* Edit Form */}
        {showAddForm && editingAddress ? (
          <Card className="mb-6">
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">
                Edit Address
              </h2>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="AddressLineOne">Address Line 1 *</Label>
                    <Input
                      id="AddressLineOne"
                      name="AddressLineOne"
                      value={formData.AddressLineOne}
                      onChange={handleInputChange}
                      placeholder="Enter street address"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="AddressLineTwo">Address Line 2</Label>
                    <Input
                      id="AddressLineTwo"
                      name="AddressLineTwo"
                      value={formData.AddressLineTwo}
                      onChange={handleInputChange}
                      placeholder="Apartment, suite, etc."
                    />
                  </div>
                  <div>
                    <Label htmlFor="CountryID">Country *</Label>
                    <Select 
                      value={formData.CountryID} 
                      onValueChange={(value) => handleSelectChange('CountryID', value)}
                      disabled={loadingCountries}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={loadingCountries ? "Loading countries..." : "Select country"} />
                      </SelectTrigger>
                      <SelectContent className="max-h-[200px] bg-white">
                        {countries.length > 0 ? (
                          countries.map((country) => (
                            <SelectItem 
                              key={country.CountryID} 
                              value={country.CountryID.toString()}
                              className="text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100"
                            >
                              {country.CountryName}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-sm text-gray-500">
                            {loadingCountries ? "Loading countries..." : "No countries found"}
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="CityID">City</Label>
                    <Select
                      value={formData.CityID}
                      onValueChange={(value) => handleSelectChange('CityID', value)}
                      disabled={!formData.CountryID || loadingCities}
                    >
                      <SelectTrigger>
                        <SelectValue
                          placeholder={
                            !formData.CountryID
                              ? "Select country first"
                              : loadingCities
                              ? "Loading cities..."
                              : "Select city"
                          }
                        />
                      </SelectTrigger>
                      <SelectContent className="max-h-[200px] bg-white">
                        {!formData.CountryID ? (
                          <div className="p-2 text-sm text-gray-500">
                            Select country first
                          </div>
                        ) : cities.length > 0 ? (
                          cities.map((city) => (
                            <SelectItem
                              key={city.CityID}
                              value={city.CityID.toString()}
                              className="text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100"
                            >
                              {city.CityName || city.Name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-sm text-gray-500">
                            {loadingCities
                              ? "Loading cities..."
                              : "No cities found"}
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="PostalCode">Postal Code</Label>
                    <Input
                      id="PostalCode"
                      name="PostalCode"
                      value={formData.PostalCode}
                      onChange={handleInputChange}
                      placeholder="Enter postal code"
                    />
                  </div>
                  <div>
                    <Label htmlFor="AddressTypeID">Address Type</Label>
                    <Select 
                      value={formData.AddressTypeID.toString()} 
                      onValueChange={(value) => handleSelectChange('AddressTypeID', value)}
                      disabled={loadingAddressTypes}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={loadingAddressTypes ? "Loading address types..." : "Select address type"} />
                      </SelectTrigger>
                      <SelectContent className="max-h-[200px] bg-white">
                        {loadingAddressTypes ? (
                          <div className="p-2 text-sm text-gray-500">
                            Loading address types...
                          </div>
                        ) : addressTypes.length > 0 ? (
                          addressTypes.filter(addressType => addressType && addressType.AddressTypeID).map((addressType) => (
                            <SelectItem
                              key={addressType.AddressTypeID}
                              value={addressType.AddressTypeID.toString()}
                              className="text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100"
                            >
                              {addressType.AddressTypeName}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-sm text-gray-500">
                            No address types available
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex gap-3">
                  <Button type="submit" className="bg-primary-dynamic text-white">
                    Update Address
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => {
                      setShowAddForm(false);
                      setEditingAddress(null);
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          </Card>
        ) : null}

        {/* Addresses List */}
        {loading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <Card key={i}>
                <div className="p-6">
                  <Skeleton className="h-6 w-32 mb-2" />
                  <Skeleton className="h-4 w-full mb-1" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </Card>
            ))}
          </div>
        ) : addresses.length > 0 ? (
          <div className="space-y-4">
            {addresses.map((address) => (
              <Card key={address.AddressID}>
                <div className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        {getAddressTypeIcon(address.AddressTypeID)}
                        <span className="font-medium text-primary">
                          {address.AddressTypeName || getAddressTypeName(address.AddressTypeID)}
                        </span>
                      </div>
                      <div className="space-y-1 text-sm">
                        <p className="font-medium">{address.AddressLineOne}</p>
                        {address.AddressLineTwo && (
                          <p className="text-muted-foreground">{address.AddressLineTwo}</p>
                        )}
                        <p className="text-muted-foreground">
                          {getCityName(address.CityID) && `${getCityName(address.CityID)}, `}{address.CountryName}
                          {address.PostalCode && ` - ${address.PostalCode}`}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(address)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">No Addresses Found</h3>
              <p className="text-muted-foreground mb-4">
                You haven't added any addresses yet. Add your first address to get started.
              </p>
              <Button onClick={() => {
                handleAccordionOpen('add-address');
              }}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Address
              </Button>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}