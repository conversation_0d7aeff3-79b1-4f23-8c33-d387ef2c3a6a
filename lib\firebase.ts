// Firebase has been replaced with Twilio SMS verification
// This file is kept for backward compatibility but Firebase is no longer used

console.warn('Firebase authentication has been replaced with Twilio SMS verification');

// Placeholder exports to prevent import errors
export const auth = null;
export const setupRecaptcha = () => null;
export const checkDomainAuthorization = () => true;

// Extend the Window interface to include recaptchaVerifier
declare global {
  interface Window {
    recaptchaVerifier: any;
  }
}