import { NextResponse } from 'next/server';
import { Config } from '@/lib/config';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    console.log("Categories API Route - Received request body:", body);

    const response = await fetch(`${Config.ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-categories-list`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log("Categories API Route - Backend response:", data);

    return NextResponse.json(data);
  } catch (error) {
    console.error("Categories API Route - Error:", error);
    return NextResponse.json(
      { error: "Failed to fetch categories" },
      { status: 500 }
    );
  }
}

export async function GET() {
  // For GET requests, use the same logic as POST with empty body
  return POST(new Request('http://localhost', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      requestParameters: {
        recordValueJson: "[]",
      },
    })
  }));
}