"use client"

import { useState, useEffect, useMemo, useCallback } from 'react'
import { useParams, useSearchParams } from "next/navigation"
import Link from "next/link"
import { Metadata, ResolvingMetadata } from 'next'
import axios from "axios"
import { 
  ShoppingCart, 
  Heart, 
  Share2, 
  Star, 
  ChevronRight, 
  ChevronDown, 
  ArrowLeft, 
  Check,
  Truck, 
  RotateCcw, 
  Award, 
  Clock
} from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { useCart } from "@/contexts/cart-context"
import { useWishlist } from "@/contexts/wishlist-context"
import { ProductSpecifications } from "@/components/products/product-specifications"
import { ProductMediaGallery } from "@/components/products/product-media-gallery"
import ProductLoading from "./product-loading"
import ProductError from "./product-error"

// Types
interface ProductImage {
  AttachmentID: number
  AttachmentName: string
  AttachmentURL: string
  ProductID: number
  IsPrimary?: boolean
}

interface ProductShippingMethod {
  ShippingMethodID: number
  ShippingMethodName: string
}

interface ProductAttribute {
  ProductID: number
  ProductAttributeID: number
  AttributeName: string
  DisplayName: string
  AttributeValueID: number
  AttributeValueText: string
  PriceAdjustment?: number
  PriceAdjustmentType?: number
}

interface Product {
  ProductId: number
  ProductName: string
  ShortDescription?: string
  FullDescription?: string
  Price: number
  DiscountPrice?: number
  OldPrice?: number
  StockQuantity: number
  IsBoundToStockQuantity: boolean
  DisplayStockQuantity: boolean
  MetaTitle?: string
  MetaKeywords?: string
  MetaDescription?: string
  VendorName?: string
  Rating: number
  TotalReviews?: number
  IsShippingFree: boolean
  IsReturnAble: boolean
  MarkAsNew: boolean
  OrderMaximumQuantity: number
  OrderMinimumQuantity: number
  EstimatedShippingDays: number
  IsDiscountAllowed: boolean
  ProductImagesJson: ProductImage[]
  ProductShipMethodsJson?: ProductShippingMethod[]
  CategoryID?: number
  CategoryName?: string
  VideoLink?: string
  AttributesJson?: ProductAttribute[]
}

interface ProductDetailsProps {
  productId: string
}

// Metadata generation for the page
type Props = {
  params: { id: string }
  searchParams: { [key: string]: string | string[] | undefined }
}

export async function generateMetadata(
  { params, searchParams }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  try {
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_API_URL}/api/Product/GetProductDetailsById/${params.id}`
    )
    const product: Product = response.data

    return {
      title: product.MetaTitle || product.ProductName,
      description: product.MetaDescription,
      keywords: product.MetaKeywords,
      openGraph: {
        title: product.MetaTitle || product.ProductName,
        description: product.MetaDescription,
        type: 'website',
        images: product.ProductImagesJson?.map(img => ({
          url: img.AttachmentURL,
          alt: product.ProductName,
        })) || [],
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: 'Product Details',
      description: 'View product details',
    }
  }
}

// Main page component
export default function ProductPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const productId = Array.isArray(params.id) ? params.id[0] : params.id || '';

  if (!productId) {
    return <ProductError error="Product ID not found" retry={() => {}} />;
  }

  return <ProductDetails productId={productId} />;
}

// Product details component
function ProductDetails({ productId }: ProductDetailsProps) {
  // Hooks
  const cart = useCart()
  const wishlist = useWishlist()

  // State
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [quantity, setQuantity] = useState<number>(1)
  const [activeTab, setActiveTab] = useState<string>("description")
  const [error, setError] = useState<string | null>(null)
  const [addingToCart, setAddingToCart] = useState<boolean>(false)
  const [addingToWishlist, setAddingToWishlist] = useState<boolean>(false)

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      setLoading(true)
      setError(null)
      try {
        const data = JSON.stringify({
          requestParameters: {
            ProductId: Number.parseInt(productId, 10),
            recordValueJson: "[]",
          },
        })

        const response = await axios.post(
          "https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail",
          data,
          {
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
          }
        )

        if (response.data) {
          setProduct(response.data)
        } else {
          throw new Error('No product data received')
        }
      } catch (err) {
        console.error('Error fetching product:', err)
        setError('Failed to load product. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    if (productId) {
      fetchProduct()
    }
  }, [productId])

  // Handle add to cart
  const handleAddToCart = async () => {
    if (!product) return
    
    try {
      setAddingToCart(true)
      await cart.addToCart({
        id: product.ProductId,
        name: product.ProductName,
        price: product.Price,
        quantity: quantity,
        image: product.ProductImagesJson?.[0]?.AttachmentURL || '',
      })
      toast.success('Added to cart', {
        duration: 2000
      })
    } catch (err) {
      console.error('Error adding to cart:', err)
      toast.error('Failed to add to cart')
    } finally {
      setAddingToCart(false)
    }
  }

  // Handle add to wishlist
  const handleAddToWishlist = async () => {
    if (!product) return
    
    try {
      setAddingToWishlist(true)
      if (wishlist.isInWishlist(product.ProductId)) {
        wishlist.removeFromWishlist(product.ProductId)
        toast.success('Removed from wishlist')
      } else {
        wishlist.addToWishlist(product.ProductId)
        toast.success('Added to wishlist')
      }
    } catch (err) {
      console.error('Error updating wishlist:', err)
      toast.error('Failed to update wishlist')
    } finally {
      setAddingToWishlist(false)
    }
  }

  // Handle share
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product?.ProductName,
        text: product?.MetaDescription,
        url: window.location.href,
      }).catch(console.error)
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast.success('Link copied to clipboard')
    }
  }

  // Loading and error states
  if (loading) {
    return <ProductLoading />
  }

  if (error || !product) {
    return <ProductError error={error || 'Product not found'} retry={() => window.location.reload()} />
  }

  return (
    <div className="container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          {product.CategoryName && (
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href={`/category/${product.CategoryID}`}>
                  {product.CategoryName}
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
          )}
          <BreadcrumbItem>
            <BreadcrumbPage>{product.ProductName}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Product Media Gallery */}
        <div className="lg:w-1/2">
          <ProductMediaGallery 
            media={product.ProductImagesJson?.map(img => ({
              type: 'image',
              url: img.AttachmentURL,
              alt: img.AttachmentName,
            })) || []} 
          />
        </div>

        {/* Product Info */}
        <div className="lg:w-1/2">
          <h1 className="text-2xl font-bold mb-2">{product.ProductName}</h1>
          
          {/* Price */}
          <div className="my-4">
            <div className="flex items-baseline gap-2">
              <span className="text-3xl font-bold text-primary">
                {product.Price.toFixed(2)} IQD
              </span>
              {product.OldPrice && product.OldPrice > product.Price && (
                <span className="text-lg text-gray-500 line-through">
                  {product.OldPrice.toFixed(2)} IQD
                </span>
              )}
            </div>
            <div className="flex items-center gap-2 mt-1">
              <div className={`w-2 h-2 rounded-full ${
                product.StockQuantity > 0 ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span className="text-sm text-gray-600">
                {product.StockQuantity > 0 
                  ? `In Stock (${product.StockQuantity} available)` 
                  : 'Out of Stock'}
              </span>
            </div>
          </div>

          {/* Badges */}
          <div className="flex flex-wrap gap-2 my-4">
            {product.MarkAsNew && (
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                New
              </Badge>
            )}
            {product.IsShippingFree && (
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                Free Shipping
              </Badge>
            )}
            {product.IsReturnAble && (
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                Free Returns
              </Badge>
            )}
          </div>

          {/* Short Description */}
          {product.ShortDescription && (
            <div className="prose max-w-none my-6">
              <p>{product.ShortDescription}</p>
            </div>
          )}

          {/* Quantity Selector */}
          <div className="my-6">
            <div className="flex items-center">
              <span className="mr-4 font-medium">Quantity:</span>
              <div className="flex items-center border rounded-md">
                <button 
                  className="px-3 py-1 text-lg font-medium"
                  onClick={() => setQuantity(q => Math.max(1, q - 1))}
                  disabled={quantity <= 1}
                >
                  -
                </button>
                <span className="px-4 py-1 border-x">{quantity}</span>
                <button 
                  className="px-3 py-1 text-lg font-medium"
                  onClick={() => setQuantity(q => q + 1)}
                >
                  +
                </button>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 mt-8">
            <Button 
              className="flex-1 py-6 text-lg"
              onClick={handleAddToCart}
              disabled={addingToCart || product.StockQuantity <= 0}
            >
              {addingToCart ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Adding...
                </>
              ) : (
                <>
                  <ShoppingCart className="w-5 h-5 mr-2" />
                  Add to Cart
                </>
              )}
            </Button>
            <Button 
              variant="outline" 
              className="flex-1 py-6 text-lg"
              onClick={handleAddToWishlist}
              disabled={addingToWishlist}
            >
              {addingToWishlist ? (
                <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin mr-2" />
              ) : (
                <Heart 
                  className={`w-5 h-5 mr-2 ${wishlist.isInWishlist(product.ProductId) ? 'fill-current' : ''}`} 
                />
              )}
              {wishlist.isInWishlist(product.ProductId) ? 'Saved' : 'Save'}
            </Button>
            <Button 
              variant="outline" 
              size="icon" 
              className="h-auto p-3"
              onClick={handleShare}
            >
              <Share2 className="w-5 h-5" />
            </Button>
          </div>

          {/* Meta Keywords */}
          {product.MetaKeywords && (
            <div className="mt-8 pt-6 border-t">
              <h3 className="text-sm font-medium text-gray-500 mb-2">Keywords</h3>
              <div className="flex flex-wrap gap-2">
                {product.MetaKeywords.split(',').map((keyword, i) => (
                  <Badge key={i} variant="outline" className="text-xs">
                    {keyword.trim()}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Product Tabs */}
      <div className="mt-12">
        <Tabs 
          defaultValue="description" 
          className="w-full"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 mb-6 gap-2">
            <TabsTrigger value="description" className="px-2 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 text-xs sm:text-sm md:text-base">Description</TabsTrigger>
            <TabsTrigger value="specifications" className="px-2 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 text-xs sm:text-sm md:text-base">Specifications</TabsTrigger>
            <TabsTrigger value="reviews" className="px-2 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 text-xs sm:text-sm md:text-base">Reviews</TabsTrigger>
            <TabsTrigger value="shipping" className="px-2 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 text-xs sm:text-sm md:text-base">Shipping & Returns</TabsTrigger>
          </TabsList>

          <TabsContent value="description" className="mt-2">
            <div className="prose max-w-none">
              {product.FullDescription ? (
                <div dangerouslySetInnerHTML={{ __html: product.FullDescription }} />
              ) : (
                <p className="text-gray-500">No description available.</p>
              )}
            </div>
          </TabsContent>

          <TabsContent value="specifications" className="mt-2">
            <div className="bg-white rounded-lg border">
              {product.AttributesJson && product.AttributesJson.length > 0 ? (
                <ProductSpecifications 
                  attributes={product.AttributesJson} 
                  className="p-6"
                />
              ) : (
                <p className="p-6 text-gray-500">No specifications available.</p>
              )}
            </div>
          </TabsContent>

          <TabsContent value="reviews" className="mt-2">
            <div className="bg-white rounded-lg border p-6">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
                <div>
                  <h3 className="text-lg font-medium">Customer Reviews</h3>
                  <div className="flex items-center mt-1">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`w-5 h-5 ${
                            star <= Math.floor(product.Rating || 0)
                              ? 'text-yellow-400 fill-yellow-400'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="ml-2 text-sm text-gray-600">
                      {product.Rating?.toFixed(1) || '0.0'} out of 5
                      {product.TotalReviews && ` • ${product.TotalReviews} reviews`}
                    </span>
                  </div>
                </div>
                <Button>Write a Review</Button>
              </div>

              {product.TotalReviews ? (
                <div className="space-y-6">
                  <p className="text-gray-500">Reviews will be displayed here.</p>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 mb-4">No reviews yet</p>
                  <Button variant="outline">Be the first to review</Button>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="shipping" className="mt-2">
            <div className="bg-white rounded-lg border p-6">
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <Truck className="w-6 h-6 text-primary mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium">Shipping Information</h3>
                    <p className="text-gray-600 mt-1">
                      {product.IsShippingFree
                        ? "This item qualifies for free shipping."
                        : "Shipping costs will be calculated at checkout based on your location."}
                    </p>
                    {product.EstimatedShippingDays && (
                      <p className="text-sm text-gray-500 mt-2">
                        Estimated delivery: {product.EstimatedShippingDays} {product.EstimatedShippingDays === 1 ? 'day' : 'days'}
                      </p>
                    )}
                  </div>
                </div>

                {product.IsReturnAble && (
                  <div className="pt-4 border-t">
                    <div className="flex items-start gap-4">
                      <RotateCcw className="w-6 h-6 text-primary mt-0.5 flex-shrink-0" />
                      <div>
                        <h3 className="font-medium">Easy Returns</h3>
                        <p className="text-gray-600 mt-1">
                          This item can be returned within 30 days of delivery.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
