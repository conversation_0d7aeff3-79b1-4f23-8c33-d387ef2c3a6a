import { Metadata } from 'next'
import axios from "axios"
import ProductDetailsClient from './product-details-client'
import { generateProductCanonicalUrl, generateMetaWithCanonical } from '@/lib/canonical-utils'


interface ProductImage {
  AttachmentID: number
  AttachmentName: string
  AttachmentURL: string
  ProductID: number
  IsPrimary?: boolean
}

interface Product {
  ProductId: number
  ProductName: string
  ShortDescription?: string
  FullDescription?: string
  Price: number
  DiscountPrice?: number
  StockQuantity: number
  MetaTitle?: string
  MetaKeywords?: string
  MetaDescription?: string
  CategoryName?: string
  ProductImagesJson: ProductImage[]
}

// Helper function to construct image URL
const constructImageUrl = (attachmentUrl: string | null) => {
  if (!attachmentUrl) return "/placeholder.svg?height=400&width=400"

  if (attachmentUrl.startsWith("http")) {
    return attachmentUrl
  }

  const baseUrl = "https://admin.codemedicalapps.com"
  // Normalize path (ensure it starts with exactly one slash)
  let normalizedPath = attachmentUrl.startsWith("/") ? attachmentUrl : `/${attachmentUrl}`
  // Remove any double slashes in the path
  normalizedPath = normalizedPath.replace(/\/+/g, '/')

  return `${baseUrl}${normalizedPath}`
}

// Helper function to fetch product data for metadata
async function fetchProductForMetadata(productId: string): Promise<Product | null> {
  try {
    const data = JSON.stringify({
      requestParameters: {
        ProductId: Number.parseInt(productId, 10),
        recordValueJson: "[]",
      },
    });

    const config = {
      method: "post",
      maxBodyLength: Number.POSITIVE_INFINITY,
      url: `${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-product_detail`,
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      data: data,
    };

    const response = await axios.request(config);

    if (response.data && response.data.data) {
      const parsedData = JSON.parse(response.data.data);
      const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;
      return productData || null;
    }
    return null;
  } catch (error) {
    console.error("Error fetching product for metadata:", error);
    return null;
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const { id } = await params;
  const product = await fetchProductForMetadata(id);

  if (!product) {
    const canonicalUrl = generateProductCanonicalUrl(id);
    return generateMetaWithCanonical(
      canonicalUrl,
      'Product Not Found - Code Medical Apps',
      'The requested product could not be found.',
      {
        robots: {
          index: false,
          follow: false,
        },
      }
    );
  }

  const metaTitle = product.MetaTitle || `${product.ProductName} - Code Medical Apps`;
  const metaDescription = product.MetaDescription || product.ShortDescription || `Buy ${product.ProductName} at the best price. High-quality medical equipment with fast delivery.`;
  const metaKeywords = product.MetaKeywords || `${product.ProductName}, medical equipment, healthcare, ${product.CategoryName || 'medical supplies'}`;
  const canonicalUrl = generateProductCanonicalUrl(id, product.ProductName);

  const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0
    ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL)
    : '/placeholder.svg?height=400&width=400';

  return generateMetaWithCanonical(
    canonicalUrl,
    metaTitle,
    metaDescription,
    {
      keywords: metaKeywords,
      openGraph: {
        type: 'website',
        images: [
          {
            url: productImage,
            width: 400,
            height: 400,
            alt: product.ProductName,
          },
        ],
      },
      twitter: {
        images: [productImage],
      },
      other: {
        'product:price:amount': (product.DiscountPrice || product.Price).toString(),
        'product:price:currency': 'USD',
        'product:availability': product.StockQuantity > 0 ? 'in stock' : 'out of stock',
        'product:condition': 'new',
      },
    }
  );
}

// Server component that renders the client component
export default async function ProductPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  return <ProductDetailsClient productId={id} />
}