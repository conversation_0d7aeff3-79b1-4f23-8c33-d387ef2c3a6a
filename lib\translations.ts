export const translations = {
  en: {
    // Common
    menu: 'Menu',
    newsletter: 'Newsletter',
    enterEmail: 'Enter your email',
    newsletterDisclaimer: 'Subscribe to our newsletter to receive updates and exclusive offers',
    popularCategories: 'Popular Categories!',
    settings: 'Settings',
    language: 'Language',
    theme: 'Theme',
    color: 'Color',
    save: 'Save',
    cancel: 'Cancel',
    close: 'Close',
    phone: '***************',
    phonenumber: 'Phone Number',
    email: '<EMAIL>',
    liveChat: 'Live Chat',
    welcome: 'Welcome',
    logout: 'Logout',
    login: 'Login',
    signup: 'Sign Up',
    signUp: 'Sign Up',
    category: 'Category',
    categories: 'Categories',
    filters: 'Filters',
    clearAll: 'Clear All',
    products: 'Products',
    loadingCategories: 'Loading categories...',
    home: 'Home',
    todayDeals: 'Today\'s Deals',
    followUs: 'Follow Us',
    aboutUs: 'About Us',
    contactUs: 'Contact Us',
    tryAgain: 'Try Again',
    noProductsFound: 'No products found',
    allProducts: 'All Products',
    // Footer
    quickLinks: 'Quick Links',
    about: 'About Us',
    contact: 'Contact Us',
    hotDeals: 'Hot Deals',
    customerArea: 'Customer Area',
    myAccount: 'My Account',
    orders: 'Orders',
    cart: 'Cart',
    wishlist: 'Wishlist',
    paymentMethods: 'Payment Methods',
    location: 'Location',
    callUs: 'Call Us',
    emailUs: 'Email Us',
    subscribe: 'Subscribe',
    // Contact Page
    name: 'Name',
    subject: 'Subject',
    message: 'Message',
    sendMessage: 'Send Message',
    sending: 'Sending...',
    messageSent: 'Message sent successfully!',
    messageError: 'Failed to send message',
    contactInfo: 'Contact Information',
    address: 'Address',
    findUs: 'Find Us',
    searchProducts: 'Search products...'
  },
  ar: {
    // Common
    menu: 'القائمة',
    popularCategories: 'الفئات الشائعة!',
    settings: 'الإعدادات',
    language: 'اللغة',
    theme: 'المظهر',
    color: 'اللون',
    save: 'حفظ',
    cancel: 'إلغاء',
    close: 'إغلاق',
    phone: '***************',
    phonenumber: 'رقم الهاتف',
    email: '<EMAIL>',
    liveChat: 'محادثة مباشرة',
    welcome: 'مرحباً',
    logout: 'تسجيل الخروج',
    login: 'تسجيل الدخول',
    signup: 'تسجيل جديد',
    signUp: 'تسجيل جديد',
    category: 'الفئة',
    categories: 'الفئات',
    products: 'المنتجات',
    loadingCategories: 'جاري تحميل الفئات...',
    filters: 'المرشحات',
    clearAll: 'مسح الكل',
    home: 'الرئيسية',
    todayDeals: 'عروض اليوم',
    followUs: 'تابعنا',
    aboutUs: 'من نحن',
    contactUs: 'اتصل بنا',
    tryAgain: 'حاول مرة أخرى',
    noProductsFound: 'لم يتم العثور على منتجات',
    allProducts: 'جميع المنتجات',
    // Footer
    quickLinks: 'روابط سريعة',
    about: 'من نحن',
    contact: 'اتصل بنا',
    hotDeals: 'عروض ساخنة',
    customerArea: 'منطقة العملاء',
    myAccount: 'حسابي',
    searchProducts: 'ابحث عن المنتجات...',
    orders: 'الطلبات',
    cart: 'السلة',
    wishlist: 'المفضلة',
    paymentMethods: 'طرق الدفع',
    location: 'الموقع',
    callUs: 'اتصل بنا',
    emailUs: 'راسلنا',
    subscribe: 'اشترك',
    // Contact Page
    name: 'الاسم',
    subject: 'الموضوع',
    message: 'الرسالة',
    sendMessage: 'إرسال الرسالة',
    sending: 'جاري الإرسال...',
    messageSent: 'تم إرسال الرسالة بنجاح!',
    messageError: 'فشل في إرسال الرسالة',
    contactInfo: 'معلومات الاتصال',
    address: 'العنوان',
    findUs: 'موقعنا'
  }
} as const;

export type TranslationKey = 
  | keyof typeof translations.en 
  | 'welcome' 
  | 'logout';

export type VariantTranslationKey = 'colorVariant' | 'sizeVariant' | 'styleVariant';

export function getTranslation(key: TranslationKey | VariantTranslationKey, language: 'en' | 'ar'): string {
  const translationSet = translations[language];
  if (key in translationSet) {
    return translationSet[key as keyof typeof translationSet];
  }
  // Fallback to English if translation is missing
  if (language !== 'en' && key in translations.en) {
    return translations.en[key as keyof typeof translations.en];
  }
  return key; // Return the key itself as a last resort
}