export interface ProductImage {
  AttachmentName: string;
  AttachmentURL: string;
  ProductID: number;
}



export interface ProductTag {
  id: number;
  name: string;
}

export interface ProductShipMethod {
  id: number;
  name: string;
  price: number;
  estimatedDays: number;
}

export interface Product {
  TotalRecords: number;
  ProductImagesJson: ProductImage[];
  ProductTagsJson: ProductTag[] | null;
  ProductShipMethodsJson: ProductShipMethod[] | null;
  Rating: number;
  TotalReviews: number | null;
  Quantity: number;
  DiscountId: number | null;
  DiscountedPrice: number | null;
  OrderItemDiscount: number | null;
  ItemSubTotal: number | null;
  IsDiscountCalculated: boolean | null;
  CouponCode: string | null;
  VendorName: string | null;
  ManufacturerName: string | null;
  CategoryID: number;
  CategoryName: string;
  DiscEndDate: string | null;
  ProductAllSelectedAttributes: any | null;
  ProductId: number;
  ProductName: string;
  ShortDescription: string | null;
  FullDescription: string | null;
  VendorId: number;
  ManufacturerId: number | null;
  MetaTitle: string | null;
  MetaKeywords: string | null;
  MetaDescription: string | null;
  Price: number;
  OldPrice: number | null;
  IsTaxExempt: boolean | null;
  IsShippingFree: boolean | null;
  EstimatedShippingDays: number | null;
  ShippingCharges: number | null;
  ShowOnHomePage: boolean | null;
  AllowCustomerReviews: boolean | null;
  ProductViewCount: number | null;
  ProductSalesCount: number | null;
  IsReturnAble: boolean | null;
  IsDigitalProduct: boolean | null;
  IsDiscountAllowed: boolean;
  SellStartDatetimeUtc: string | null;
  SellEndDatetimeUtc: string | null;
  Sku: string | null;
  CreatedOn: string;
  CreatedBy: string | null;
  ModifiedOn: string | null;
  ModifiedBy: string | null;
  WarehouseId: number | null;
  InventoryMethodId: number | null;
  StockQuantity: number;
  IsBoundToStockQuantity: boolean | null;
  DisplayStockQuantity: boolean | null;
  OrderMinimumQuantity: number | null;
  OrderMaximumQuantity: number | null;
  MarkAsNew: boolean;
  DisplaySeqNo: number | null;
  IsActive: boolean;
}

export interface ApiResponse<T> {
  statusCode: number;
  statusMessage: string;
  message: string | null;
  data: T;
}