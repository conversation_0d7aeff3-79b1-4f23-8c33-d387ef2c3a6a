import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
}

/**
 * Facebook Messenger brand icon.
 * Outer path inherits currentColor so it can be tinted via CSS/parent.
 * Inner bolt remains white for correct logo appearance.
 */
const MessengerIcon: React.FC<IconProps> = ({ className, ...props }) => (
  <svg
    viewBox="-1 0 226 226"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    className={className}
    {...props}
  >
    <path d="M41.255 185.52v40.2l37.589-21.37c10.478 3.02 21.616 4.65 33.156 4.65 61.86 0 112-46.79 112-104.5 0-57.714-50.14-104.5-112-104.5-61.856 0-112 46.786-112 104.5 0 32.68 16.078 61.86 41.255 81.02z" />
    <path d="m100.04 75.878-60.401 63.952 54.97-30.16 28.721 30.16 60.06-63.952-54.36 29.632-28.99-29.632z" fill="#ffffff" />
  </svg>
);

export default MessengerIcon;
