'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { motion } from 'framer-motion';
import { Mail, Lock, RefreshCw, CheckCircle, Shield } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import { useUser } from '@/contexts/user-context';

import { cn } from '@/lib/utils';
import { PasswordInput } from '@/components/ui/password-input';
import { useToast } from '@/hooks/use-toast';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';

export default function Login() {
  const { login, isLoading, isLoggedIn } = useUser();
  const { currentTheme } = useColorThemeContext();
  
  const primaryColor = currentTheme.primary;
  const primaryTextColor = currentTheme.primaryForeground;

  const [error, setError] = useState('');
  const [loginDetails, setLoginDetails] = useState({
    email: '',
    password: ''
  });
  const { t } = useSettings();
  const { toast } = useToast();
  const router = useRouter();
  const { executeRecaptcha } = useGoogleReCaptcha();
  const [formErrors, setFormErrors] = useState({
    email: '',
    password: ''
  });
  const [recaptchaLoading, setRecaptchaLoading] = useState(false);

  const validateForm = () => {
    const errors = {
      email: '',
      password: ''
    };
    let isValid = true;

    if (!loginDetails.email.trim()) {
      errors.email = 'Email is required';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(loginDetails.email)) {
      errors.email = 'Please enter a valid email address';
      isValid = false;
    }

    if (!loginDetails.password.trim()) {
      errors.password = 'Password is required';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  // Redirect if already logged in
  useEffect(() => {
    if (isLoggedIn) {
      // Check for redirect parameter
      const urlParams = new URLSearchParams(window.location.search);
      const redirectTo = urlParams.get('redirect');

      if (redirectTo) {
        const decodedUrl = decodeURIComponent(redirectTo);
        if (decodedUrl.startsWith('/') && !decodedUrl.startsWith('//')) {
          console.log('🔄 Already logged in, redirecting to stored URL:', decodedUrl);
          router.push(decodedUrl);
          return;
        }
      }

      // Default to account page
      router.push('/account');
    }
  }, [isLoggedIn, router]);

  // Show loading state while checking authentication
  if (isLoading && !isLoggedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background to-muted/20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Checking authentication...</p>
        </div>
      </div>
    );
  }

  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const isValid = validateForm();
    if (!isValid) {
      return;
    }

    setError('');

    try {
      // Execute reCAPTCHA v3
      if (!executeRecaptcha) {
        setError('reCAPTCHA not available. Please refresh the page.');
        return;
      }

      setRecaptchaLoading(true);
      const recaptchaToken = await executeRecaptcha('login');
      
      if (!recaptchaToken) {
        setError('reCAPTCHA verification failed. Please try again.');
        setRecaptchaLoading(false);
        return;
      }

      console.log('reCAPTCHA token obtained:', recaptchaToken.substring(0, 20) + '...');
      setRecaptchaLoading(false);

      // Clear any existing auth cookies before login
      await fetch('/api/auth/clear-cookies', {
        method: 'POST',
        credentials: 'include'
      });

      const result = await login(loginDetails.email, loginDetails.password, recaptchaToken);

      if (result.success) {
        toast({
          title: "Login Successful",
          description: result.message,
          variant: "default",
        });

        // Force a cookie refresh by making a request to get-token
        try {
          const tokenResponse = await fetch('/api/auth/get-token', {
            method: 'GET',
            credentials: 'include'
          });
          
          if (!tokenResponse.ok) {
            console.warn('Failed to refresh auth token after login');
          }
        } catch (tokenError) {
          console.error('Error refreshing token:', tokenError);
        }

        // Get redirect URL from query params or default to account
        const urlParams = new URLSearchParams(window.location.search);
        const redirectTo = urlParams.get('redirect');

        // Small delay to ensure cookies are set
        await new Promise(resolve => setTimeout(resolve, 300));

        // Force a hard refresh to ensure all auth state is properly loaded
        if (redirectTo) {
          const decodedUrl = decodeURIComponent(redirectTo);
          if (decodedUrl.startsWith('/') && !decodedUrl.startsWith('//')) {
            console.log('🔄 Hard redirecting to stored URL after login:', decodedUrl);
            window.location.href = decodedUrl;
            return;
          }
        }
        
        console.log('🏠 No valid redirect URL, going to account page');
        window.location.href = '/account';
      } else {
        setError(result.message);
        toast({
          title: "Login Failed",
          description: result.message,
          variant: "destructive",
        });
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to login';
      setError(errorMessage);
      toast({
        title: "Login Error",
        description: errorMessage,
        variant: "destructive",
      });
      console.error('Error:', err);
      setRecaptchaLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold">
            Welcome Back
          </h2>
          <p className="mt-2 text-sm text-muted-foreground">
            Sign in to your account
          </p>
        </div>

        <Card className="mt-8 p-8 shadow-xl bg-card">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <form onSubmit={handleLoginSubmit} className="space-y-6">
              <div>
                <Label className="block text-sm font-medium mb-2">Email</Label>
                <div className="relative">
                  <Input
                    type="email"
                    value={loginDetails.email}
                    onChange={(e) => {
                      setLoginDetails({...loginDetails, email: e.target.value});
                      if (formErrors.email) validateForm();
                    }}
                    className={cn("pl-10", formErrors.email && "border-destructive")}
                    required
                    disabled={isLoading}
                  />
                  <Mail className="w-4 h-4 absolute left-3 top-3 text-muted-foreground" />
                </div>
                {formErrors.email && (
                  <p className="mt-1 text-sm text-destructive">{formErrors.email}</p>
                )}
              </div>
              <div>
                <Label className="block text-sm font-medium mb-2">Password</Label>
                <div className="relative">
                  <PasswordInput
                    value={loginDetails.password}
                    onChange={(e) => {
                      setLoginDetails({...loginDetails, password: e.target.value});
                      if (formErrors.password) validateForm();
                    }}
                    className={cn("pl-10", formErrors.password && "border-destructive")}
                    required
                    disabled={isLoading}
                  />
                  <Lock className="w-4 h-4 absolute left-3 top-3 text-muted-foreground" />
                </div>
                {formErrors.password && (
                  <p className="mt-1 text-sm text-destructive">{formErrors.password}</p>
                )}
              </div>

              {error && (
                <div className="text-destructive text-sm text-center">{error}</div>
              )}

              <Button
                type="submit"
                className="w-full h-12 text-lg transition-colors"
                style={{ backgroundColor: primaryColor, color: primaryTextColor }}
                disabled={isLoading || recaptchaLoading}
              >
                {isLoading || recaptchaLoading ? (
                  <div className="flex items-center gap-2">
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    {recaptchaLoading ? 'Verifying...' : 'Signing In...'}
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Shield className="w-4 h-4" />
                    Sign In
                  </div>
                )}
              </Button>

              {/* reCAPTCHA notice */}
              <div className="text-xs text-center text-muted-foreground">
                This site is protected by reCAPTCHA and the Google{' '}
                <a href="https://policies.google.com/privacy" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                  Privacy Policy
                </a>{' '}
                and{' '}
                <a href="https://policies.google.com/terms" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                  Terms of Service
                </a>{' '}
                apply.
              </div>

              <div className="text-center text-sm space-y-2">
                <div>
                  <Link href="/forgot-password" className="text-primary hover:text-primary/80 hover:underline transition-colors">
                    Forgot your password?
                  </Link>
                </div>
                <div>
                  <span className="text-muted-foreground">Don't have an account? </span>
                  <Link href="/signup" className="text-primary hover:text-primary/80 hover:underline transition-colors">
                    Sign up
                  </Link>
                </div>
              </div>
            </form>
          </motion.div>
        </Card>
      </div>
    </div>
  );
}