/**
 * Utility functions for generating structured data (JSON-LD) for SEO
 */

export interface ProductStructuredData {
  name: string;
  description: string;
  image: string[];
  sku?: string;
  mpn?: string;
  brand?: string;
  price?: number;
  priceCurrency?: string;
  priceValidUntil?: string;
  availability?: 'InStock' | 'OutOfStock' | 'PreOrder';
  condition?: 'NewCondition' | 'UsedCondition' | 'RefurbishedCondition';
  url?: string;
  rating?: {
    ratingValue: number;
    bestRating: number;
    reviewCount: number;
  };
  reviews?: Array<{
    author: string;
    ratingValue: number;
    bestRating: number;
    reviewBody?: string;
  }>;
}

export function generateProductStructuredData(product: ProductStructuredData) {
  const structuredData: any = {
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": product.name,
    "description": product.description,
    "image": product.image
  };

  // Add optional fields
  if (product.sku) {
    structuredData.sku = product.sku;
  }

  if (product.mpn) {
    structuredData.mpn = product.mpn;
  }

  if (product.brand) {
    structuredData.brand = {
      "@type": "Brand",
      "name": product.brand
    };
  }

  // Add pricing information
  if (product.price !== undefined) {
    structuredData.offers = {
      "@type": "Offer",
      "priceCurrency": product.priceCurrency || "USD",
      "price": product.price,
      "itemCondition": `https://schema.org/${product.condition || 'NewCondition'}`,
      "availability": `https://schema.org/${product.availability || 'InStock'}`
    };

    if (product.url) {
      structuredData.offers.url = product.url;
    }

    if (product.priceValidUntil) {
      structuredData.offers.priceValidUntil = product.priceValidUntil;
    }
  }

  // Add rating information
  if (product.rating) {
    structuredData.aggregateRating = {
      "@type": "AggregateRating",
      "ratingValue": product.rating.ratingValue,
      "bestRating": product.rating.bestRating,
      "reviewCount": product.rating.reviewCount
    };
  }

  // Add reviews
  if (product.reviews && product.reviews.length > 0) {
    if (product.reviews.length === 1) {
      // Single review
      structuredData.review = {
        "@type": "Review",
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": product.reviews[0].ratingValue,
          "bestRating": product.reviews[0].bestRating
        },
        "author": {
          "@type": "Person",
          "name": product.reviews[0].author
        }
      };

      if (product.reviews[0].reviewBody) {
        structuredData.review.reviewBody = product.reviews[0].reviewBody;
      }
    } else {
      // Multiple reviews
      structuredData.review = product.reviews.map(review => ({
        "@type": "Review",
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": review.ratingValue,
          "bestRating": review.bestRating
        },
        "author": {
          "@type": "Person",
          "name": review.author
        },
        ...(review.reviewBody && { reviewBody: review.reviewBody })
      }));
    }
  }

  return structuredData;
}

export function generateOrganizationStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Code Medical",
    "url": "https://codemedicalapps.com",
    "logo": `${process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'https://admin.codemedicalapps.com/'}content/commonImages/otherImages/18b_logo2x.png`,
    "description": "Professional team specialized in providing well known valuable medical courses, Ebooks, Printed books and popular medical accounts for all medical field staff all around the world with low cost and in short time.",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+964-************",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "sameAs": [
      "https://www.facebook.com/codemedicalapps/",
      "https://t.me/codemedicalapps",
      "https://wa.me/*************"
    ]
  };
}

export function generateWebsiteStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Code Medical",
    "url": "https://codemedicalapps.com",
    "description": "Medical courses, ebooks, printed books and medical accounts for medical professionals worldwide.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://codemedicalapps.com/products?search={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  };
}

/**
 * Utility function to create structured data script content
 */
export function createStructuredDataScript(data: any): string {
  return JSON.stringify(data, null, 2);
}