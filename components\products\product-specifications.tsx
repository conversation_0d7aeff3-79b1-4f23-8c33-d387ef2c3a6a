"use client"

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check } from "lucide-react"
import { useColorThemeContext } from '@/contexts/color-theme-context'

type ProductAttribute = {
  ProductID: number
  ProductAttributeID: number
  AttributeName: string
  DisplayName: string
  AttributeValueID: number
  AttributeValueText: string
  GroupName?: string
}

interface GroupedAttributes {
  [key: string]: ProductAttribute[]
}

interface ProductSpecificationsProps {
  attributes: ProductAttribute[]
  className?: string
}

const COMMON_GROUPS = [
  'General',
  'Technical Specifications',
  'Dimensions',
  'Materials',
  'Colors',
  'Features',
  'Warranty',
  'Package Includes'
]

export function ProductSpecifications({ 
  attributes = [],
  className = "" 
}: ProductSpecificationsProps) {
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  
  if (!attributes || attributes.length === 0) {
    return null
  }

  // Group attributes by their DisplayName (or AttributeName if DisplayName is not available)
  const groupedAttributes = attributes.reduce<GroupedAttributes>((groups, attr) => {
    // Try to find a matching group name from common groups
    const attrName = attr.DisplayName || attr.AttributeName
    let groupName = 'Specifications' // Default group name
    
    // Check if the attribute name contains any of the common group names
    const matchedGroup = COMMON_GROUPS.find(group => 
      attrName.toLowerCase().includes(group.toLowerCase())
    )
    
    if (matchedGroup) {
      groupName = matchedGroup
    } else if (attrName.toLowerCase().includes('dimension') || 
               attrName.toLowerCase().includes('size') ||
               attrName.toLowerCase().includes('weight')) {
      groupName = 'Dimensions'
    } else if (attrName.toLowerCase().includes('color') || 
               attrName.toLowerCase().includes('colour')) {
      groupName = 'Colors'
    } else if (attrName.toLowerCase().includes('material') || 
               attrName.toLowerCase().includes('fabric')) {
      groupName = 'Materials'
    } else if (attrName.toLowerCase().includes('feature') || 
               attrName.toLowerCase().includes('spec')) {
      groupName = 'Features'
    }
    
    if (!groups[groupName]) {
      groups[groupName] = []
    }
    
    groups[groupName].push(attr)
    return groups
  }, {})

  // Sort groups by our preferred order
  const sortedGroups = Object.entries(groupedAttributes).sort(([groupA], [groupB]) => {
    const indexA = COMMON_GROUPS.indexOf(groupA)
    const indexB = COMMON_GROUPS.indexOf(groupB)
    
    if (indexA === -1 && indexB === -1) return groupA.localeCompare(groupB)
    if (indexA === -1) return 1
    if (indexB === -1) return -1
    return indexA - indexB
  })

  // Function to format attribute values
  const formatValue = (value: string) => {
    if (!value) return 'N/A'
    
    // Check if the value is a URL
    try {
      const url = new URL(value)
      return (
        <a 
          href={url.toString()} 
          target="_blank" 
          rel="noopener noreferrer"
          className="text-blue-600 hover:underline"
        >
          View {url.hostname}
        </a>
      )
    } catch {
      // Not a URL, return as is
      return value
    }
  }

  return (
    <div className={className}>
      <h2 className="text-2xl font-bold mb-6">Product Specifications</h2>
      
      <div className="space-y-8">
        {sortedGroups.map(([groupName, groupAttributes]) => (
          <Card key={groupName} className="overflow-hidden">
            <CardHeader className="bg-gray-50 p-4 border-b">
              <CardTitle className="text-lg font-semibold">{groupName}</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y">
                {groupAttributes.map((attr, index) => {
                  const attrName = attr.DisplayName || attr.AttributeName
                  const attrValue = attr.AttributeValueText
                  
                  return (
                    <div 
                      key={`${attr.ProductAttributeID}-${index}`} 
                      className="grid grid-cols-1 md:grid-cols-3 p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="font-medium text-gray-900 flex items-center">
                        <Check className="h-4 w-4 mr-2" style={{ color: primaryColor }} />
                        {attrName}:
                      </div>
                      <div className="md:col-span-2 text-gray-700 mt-1 md:mt-0">
                        {groupName === 'Colors' ? (
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-6 h-6 rounded-full border border-gray-300"
                              style={{ 
                                backgroundColor: attrValue.toLowerCase(),
                                // Add a checkered pattern for white/light colors
                                backgroundImage: 
                                  ['white', '#fff', '#ffffff', 'rgb(255,255,255)'].includes(attrValue.toLowerCase())
                                    ? 'linear-gradient(45deg, #e5e7eb 25%, transparent 25%), linear-gradient(-45deg, #e5e7eb 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #e5e7eb 75%), linear-gradient(-45deg, transparent 75%, #e5e7eb 75%)'
                                    : 'none',
                                backgroundSize: '8px 8px',
                              }}
                            />
                            <span className="capitalize">{attrValue}</span>
                          </div>
                        ) : groupName === 'Dimensions' && (attrName.toLowerCase().includes('dimension') || attrName.toLowerCase().includes('size')) ? (
                          <div className="flex items-center gap-1">
                            {attrValue.split('x').map((dim, i) => (
                              <React.Fragment key={i}>
                                {i > 0 && <span className="mx-1 text-gray-400">×</span>}
                                <span>{dim.trim()}</span>
                              </React.Fragment>
                            ))}
                            {!isNaN(parseFloat(attrValue)) && <span className="ml-1 text-sm text-gray-500">cm</span>}
                          </div>
                        ) : (
                          <span>{formatValue(attrValue)}</span>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
