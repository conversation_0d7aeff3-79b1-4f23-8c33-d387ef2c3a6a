'use client';

import { useEffect, useState } from 'react';
import { Config } from '@/lib/config';
import Link from 'next/link';
import { ChevronRight, Flame } from 'lucide-react';
import { useSettings } from '@/contexts/settings-context';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import ProductCard from '@/components/product-card';
import { Skeleton } from '@/components/ui/skeleton';

// Updated interface to match ProductCard expectations
interface Product {
  ProductId: number;
  ProductName: string;
  Price: number;
  OldPrice?: number;
  DiscountPrice?: number;
  Rating: number;
  ProductImageUrl?: string;
  CategoryName: string;
  StockQuantity: number;
  ProductTypeName?: string;
  IQDPrice?: number;
  IsDiscountAllowed?: boolean;
  MarkAsNew?: boolean;
  SellStartDatetimeUTC?: string;
  SellEndDatetimeUTC?: string;
}

export default function HotDealsPage() {
  const {  } = useSettings();
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchPopularProducts = async () => {
      setLoading(true);
      try {
        // Prepare parameters and headers according to API requirements
        const param = {
          requestParameters: {
            PageNo: 1,
            PageSize: 12 // Get more products for the hot deals page
          }
        };

        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        };

        // Import MakeApiCallAsync from api-helper
        const { MakeApiCallAsync } = await import('@/lib/api-helper');

        // Use the new API endpoint for popular products
        const response = await MakeApiCallAsync(
          'get-popular-products-list',
          null,
          param,
          headers,
          "POST",
          true
        );

        // Process the response
        if (response?.data?.data) {
          try {
            const parsedData = JSON.parse(response.data.data);
            console.log('Popular products data:', parsedData);

            if (Array.isArray(parsedData) && parsedData.length > 0) {
              // Transform the API data to match ProductCard interface
              const transformedProducts = parsedData.map(item => {
                // Process image URL similar to products page
                const rawUrl = item.ProductImagesUrl || item.ProductImageUrl;
                let imageUrl = null;

                try {
                  if (rawUrl) {
                    let cleanUrl = rawUrl;

                    if (typeof rawUrl === 'string' && (rawUrl.startsWith('[') || rawUrl.startsWith('"'))) {
                      try {
                        const parsed = JSON.parse(rawUrl);
                        if (Array.isArray(parsed) && parsed.length > 0) {
                          cleanUrl = parsed[0].AttachmentURL || parsed[0];
                        } else if (typeof parsed === 'string') {
                          cleanUrl = parsed;
                        }
                      } catch (jsonError) {
                        cleanUrl = rawUrl.replace(/^"|"/g, '');
                      }
                    }

                    if (typeof cleanUrl === 'string' && cleanUrl.trim() !== '') {
                      cleanUrl = cleanUrl.replace(/^"|"$/g, '').trim();

                      if (cleanUrl) {
                        const decodedUrl = decodeURIComponent(cleanUrl);
                        const normalizedUrl = decodedUrl.startsWith('/') || decodedUrl.startsWith('http') 
                          ? decodedUrl 
                          : `/${decodedUrl}`;

                        imageUrl = normalizedUrl.startsWith('http') 
                          ? normalizedUrl 
                          : `${Config.ADMIN_BASE_URL}${normalizedUrl}`;
                      }
                    }
                  }
                } catch (error) {
                  console.error('Error processing URL for product', item.ProductId, ':', error);
                }

                return {
                  ProductId: item.ProductId || item.ProductID || 0,
                  ProductName: item.ProductName || 'Popular Product',
                  Price: parseFloat(item.Price) || 0,
                  OldPrice: item.OldPrice ? parseFloat(item.OldPrice) : undefined,
                  DiscountPrice: item.DiscountPrice ? parseFloat(item.DiscountPrice) : undefined,
                  Rating: parseFloat(item.Rating) || 0,
                  ProductImageUrl: imageUrl || undefined,
                  CategoryName: item.CategoryName || 'Popular',
                  StockQuantity: parseInt(item.StockQuantity, 10) || 0,
                  ProductTypeName: item.ProductTypeName,
                  IQDPrice: parseFloat(item.IQDPrice) || undefined,
                  IsDiscountAllowed: Boolean(item.IsDiscountAllowed),
                  MarkAsNew: Boolean(item.MarkAsNew),
                  SellStartDatetimeUTC: item.SellStartDatetimeUTC,
                  SellEndDatetimeUTC: item.SellEndDatetimeUTC
                };
              });

              setProducts(transformedProducts);
            } else {
              console.log('No popular products found');
              setProducts([]);
            }
          } catch (parseError) {
            console.error('Error parsing popular products data:', parseError);
            setProducts([]);
          }
        } else {
          console.error('Invalid or empty response from API');
          setProducts([]);
        }
      } catch (error) {
        console.error('Error fetching popular products:', error);
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPopularProducts();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <div className="flex items-center text-sm mb-6">
        <Link href="/" className="text-gray-500 hover:text-primary">
          Home
        </Link>
        <ChevronRight className="h-4 w-4 mx-2 text-gray-400" />
        <span className="font-medium">Hot Deals</span>
      </div>

      {/* Hot Deals Header */}
      <div className="mb-8 flex items-center">
        <Flame className="h-8 w-8 mr-3" style={{ color: primaryColor }} />
        <div>
          <h1 className="text-3xl font-bold" style={{ color: primaryColor }}>Hot Deals</h1>
          <p className="text-gray-600 mt-2">
            Special offers and discounts on our best products
          </p>
        </div>
      </div>

      {/* Products Grid - matching products page styling */}
      {loading ? (
        <div className="grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 12 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow overflow-hidden">
              <div className="aspect-square">
                <Skeleton className="h-full w-full" />
              </div>
              <div className="p-4 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-6 w-1/3" />
              </div>
              <div className="p-4 pt-0">
                <div className="flex w-full gap-2">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-10" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : products.length > 0 ? (
        <div className="grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {products.map((product) => (
            <ProductCard
              key={product.ProductId}
              product={product}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Flame className="h-8 w-8" style={{ color: '#ef4444' }} />
            <h2 className="text-2xl font-bold">No Hot Deals Available</h2>
          </div>
          <p className="text-gray-500">Check back later for amazing deals!</p>
        </div>
      )}
    </div>
  );
}