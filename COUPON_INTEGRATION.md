# Coupon Integration with Discount Types

## Overview
This document describes the updated coupon system that supports the new API response structure with discount types and value types.

## API Response Structure
```json
{
  "statusCode": 200,
  "statusMessage": "Ok",
  "message": null,
  "data": "[{\"DiscountId\":1019,\"Title\":\"TIA\",\"CouponCode\":\"41EEC1\",\"DiscountValue\":15.00,\"DiscountValueType\":2,\"DiscountTypeId\":1,\"StartDate\":\"2025-05-12T17:06:00\",\"EndDate\":\"2025-11-30T17:06:00\",\"IsActive\":true,\"IsCouponCodeRequired\":true,\"IsBoundToMaxQuantity\":false,\"MaxQuantity\":null,\"ProductId\":null,\"CategoryID\":null}]",
  "isAuthorized": false,
  "token": null,
  "errorMessage": ""
}
```

## Discount Types (DiscountTypeId)
1. **Applied on order total** - Discount applies to the entire order total
2. **Applied on order subtotal** - Discount applies to the order subtotal (before taxes/shipping)
3. **Applied on products** - Discount applies to specific products
4. **Applied on categories** - Discount applies to specific product categories
5. **Applied on manufacturers** - Discount applies to products from specific manufacturers
6. **Applied on cities** - Discount applies based on delivery city
7. **Applied on shipping** - Discount applies to shipping costs

## Discount Value Types (DiscountValueType)
- **1**: Percentage discount (e.g., 15% off)
- **2**: Fixed value discount (e.g., $15 off)

## Implementation Details

### Updated Coupon Type
```typescript
type Coupon = {
  code: string;
  discount: number;
  type: 'percentage' | 'fixed';
  discountTypeId: number;
  title?: string;
  discountId?: number;
  minAmount?: number;
  expiryDate?: Date;
  maxQuantity?: number;
  productId?: number;
  categoryId?: number;
};
```

### Key Features
1. **Enhanced Coupon Context**: Updated to handle the new API response structure
2. **Discount Calculation**: Proper calculation based on discount type and value type
3. **Visual Feedback**: Enhanced UI showing coupon title and application scope
4. **Validation**: Checks for coupon validity, expiry, and applicability

### Usage in Cart
- Coupons are applied in the cart page
- Visual feedback shows the coupon title, code, and application scope
- Discount amount is displayed in both USD and IQD
- Users can remove applied coupons

### Files Modified
1. `contexts/coupon-context.tsx` - Updated coupon logic and API integration
2. `app/cart/page.tsx` - Enhanced coupon display with detailed information

## Testing
To test the coupon functionality:
1. Add items to cart
2. Navigate to cart page
3. Enter a valid coupon code (e.g., "41EEC1")
4. Verify the discount is applied correctly based on the discount type
5. Check that the coupon information is displayed properly