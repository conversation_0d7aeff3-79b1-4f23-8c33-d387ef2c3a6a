'use client';

import { BannerSlider } from "@/components/ui/banner-slider";
import { SidebarCategories } from "@/components/ui/sidebar-categories";
import PopularCategories from "@/components/ui/popular-categories";
import { DiscountBannerOmg } from "@/components/ui/discount-banner-omg";
import { NewProducts } from "@/components/ui/new-products";
import { CompaignSection } from "@/components/ui/compaign-section";
import { TodayHotDeal } from "@/components/ui/today-hot-deal";
import { PopularProducts } from "@/components/ui/popular-products";
import { ContactBanner } from "@/components/ui/contact-banner";

export function HomePageClient() {
  return (
    <div className="flex flex-col lg:flex-row min-h-screen bg-background">
      {/* Sidebar - Visible on tablet and desktop */}
      <div className="w-full md:w-48 lg:w-64 md:flex-shrink-0 px-4 sm:px-6 md:px-0 py-4 md:py-0">
        <SidebarCategories />
      </div>

      {/* Main Content - Better tablet utilization */}
      <div className="flex-1 p-3 sm:p-4 md:p-6 lg:p-8">
        <BannerSlider />
        <PopularCategories />

        {/* Vertically stacked sections instead of tabs */}
        <div className="space-y-6 md:space-y-8 lg:space-y-12 mt-4 md:mt-6 lg:mt-8">
          <section>
            <h2 className="text-lg md:text-xl lg:text-2xl font-bold mb-4 lg:mb-6">New Products</h2>
            <NewProducts effect="icon-inline" />
          </section>

          <section>
            <CompaignSection />
          </section>

          <section>
            <TodayHotDeal />
          </section>
        </div>
      </div>
    </div>
  );
}