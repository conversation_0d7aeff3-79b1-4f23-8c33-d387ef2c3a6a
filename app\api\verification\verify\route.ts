import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber, verificationCode } = await request.json();

    if (!phoneNumber || !verificationCode) {
      return NextResponse.json(
        { success: false, error: 'Phone number and verification code are required' },
        { status: 400 }
      );
    }

    // Call .NET API to verify code
    const apiResponse = await fetch(`${process.env.ADMIN_BASE_URL || process.env.NEXT_PUBLIC_ADMIN_BASE_URL}/api/v1/verification/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        PhoneNumber: phoneNumber,        // Capital P to match backend
        VerificationCode: verificationCode  // Capital V and C to match backend
      })
    });

    if (!apiResponse.ok) {
      throw new Error('Failed to verify code');
    }

    const result = await apiResponse.json();

    return NextResponse.json({
      success: result.success,
      isValid: result.isValid,
      message: result.message,
      attemptCount: result.attemptCount
    });

  } catch (error) {
    console.error('Error verifying code:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to verify code' },
      { status: 500 }
    );
  }
}