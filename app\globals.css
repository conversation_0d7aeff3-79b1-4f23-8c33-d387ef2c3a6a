@tailwind base;
@tailwind components;
@tailwind utilities;

/* SweetAlert2 styles */
@import 'sweetalert2/dist/sweetalert2.min.css';

/* SweetAlert2 theme customization */
.swal2-popup {
  background: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.swal2-title {
  color: hsl(var(--foreground)) !important;
}

.swal2-content {
  color: hsl(var(--muted-foreground)) !important;
}

.swal2-confirm {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  border: none !important;
}

.swal2-cancel {
  background-color: hsl(var(--secondary)) !important;
  color: hsl(var(--secondary-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.swal2-confirm:hover {
  background-color: hsl(var(--primary)) !important;
  opacity: 0.9;
}

.swal2-cancel:hover {
  background-color: hsl(var(--accent)) !important;
}

/* Mobile safe area support */
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Custom positioning override */
.-right-0\.5 {
  right: -0.01rem;
}

/* Mobile bottom navigation styles */
@media (max-width: 768px) {
  body {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Mobile horizontal orientation styles */
@media (max-width: 768px) and (orientation: landscape) {
  body {
    padding-bottom: 55px; /* Reduced height for landscape */
  }
  
  /* Adjust mobile bottom nav for horizontal orientation */
  .mobile-bottom-nav {
    height: 55px !important; /* Reduced from default 70px */
    padding: 6px 0 !important; /* Reduced padding */
    display: flex !important;
    justify-content: center !important; /* Center the navigation items */
    align-items: center !important;
  }
  
  .mobile-bottom-nav > div {
    justify-content: center !important;
    width: 100% !important;
    max-width: 350px !important; /* Optimized width for better centering */
    margin: 0 auto !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important; /* Even spacing between items */
  }
  
  .mobile-nav-item {
    padding: 4px 6px !important; /* Compact padding */
    font-size: 0.7rem !important; /* Smaller text */
    flex: 0 0 auto !important; /* Prevent stretching */
    justify-content: center !important; /* Center icon within item */
    align-items: center !important; /* Center icon vertically */
    margin: 0 !important; /* Remove margins, use gap instead */
    min-width: 50px !important; /* Ensure consistent width */
    max-width: 60px !important;
  }
  
  .mobile-nav-icon {
    width: 18px !important;
    height: 18px !important; /* Smaller icons */
    margin-bottom: 2px !important;
  }
  
  /* Hide text labels in landscape to save space */
  .mobile-nav-text {
    display: none !important;
  }
  
  /* Adjust badge positioning */
  .mobile-nav-badge {
    top: -4px !important;
    right: -4px !important;
    font-size: 0.6rem !important;
    min-width: 14px !important;
    height: 14px !important;
    transform: scale(0.9) !important;
  }
}

/* Tablet vertical orientation styles */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  body {
    padding-bottom: 85px; /* Space for tablet bottom nav */
  }
  
  /* Enhanced tablet navigation */
  .tablet-nav {
    height: 85px !important;
    padding: 8px 0 !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-top: 2px solid #f1f5f9 !important;
  }
  
  .tablet-nav-container {
    justify-content: center !important;
    align-items: center !important;
    gap: 12px !important;
    max-width: 600px !important;
    margin: 0 auto !important;
  }
  
  .tablet-nav-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    min-width: 85px !important;
    padding: 12px 8px !important;
    border-radius: 16px !important;
    margin: 0 6px !important;
  }
  
  .tablet-nav-item:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  }
  
  .tablet-nav-icon {
    width: 28px !important;
    height: 28px !important;
    margin-bottom: 6px !important;
    transition: transform 0.3s ease !important;
  }
  
  .tablet-nav-item:hover .tablet-nav-icon {
    transform: scale(1.1) !important;
  }
  
  .tablet-nav-text {
    font-size: 0.85rem !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }
  
  .tablet-nav-badge {
    top: -6px !important;
    right: -6px !important;
    font-size: 0.7rem !important;
    min-width: 20px !important;
    height: 20px !important;
    border: 2px solid white !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  }
  
  /* Hide categories section in tablet vertical */
  .categories-section,
  [data-testid="categories"],
  .category-list,
  .mobile-categories {
    display: none !important;
  }
  
  /* Category slider animations */
  .category-slide-enter {
    opacity: 0;
    transform: translateY(100%);
  }
  
  .category-slide-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease-out;
  }
  
  .category-slide-exit {
    opacity: 1;
    transform: translateY(0);
  }
  
  .category-slide-exit-active {
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.3s ease-in;
  }
}

/* Enhanced Mobile Portrait Navigation */
@media (max-width: 768px) and (orientation: portrait) {
  .mobile-bottom-nav {
    height: 70px !important;
    padding: 8px 0 !important;
  }
  
  .mobile-nav-item {
    padding: 6px 8px !important;
    min-width: 60px !important;
  }
  
  .mobile-nav-icon {
    width: 24px !important;
    height: 24px !important;
    margin-bottom: 4px !important;
  }
  
  .mobile-nav-text {
    font-size: 0.75rem !important;
    display: block !important;
  }
  
  .mobile-nav-badge {
    top: -2px !important;
    right: -2px !important;
    font-size: 0.65rem !important;
    min-width: 16px !important;
    height: 16px !important;
  }
}

/* Enhanced responsive design for small screens */
@media (max-width: 480px) {
  .mobile-bottom-nav > div {
    gap: 4px !important;
    padding: 0 8px !important;
  }
  
  .mobile-nav-item {
    min-width: 55px !important;
    padding: 4px 6px !important;
  }
  
  .mobile-nav-text {
    font-size: 0.7rem !important;
  }
}

/* Enhanced tablet landscape support with dropdown navigation */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  /* Hide mobile bottom navigation in tablet landscape */
  .sm\:hidden {
    display: none !important;
  }
  
  /* Hide tablet bottom nav in landscape */
  .tablet-nav {
    display: none !important;
  }
  
  /* Ensure desktop layout components are visible but optimized */
  .hidden.lg\:flex {
    display: flex !important;
  }
  
  /* Header optimizations for tablet landscape */
  header {
    min-height: auto !important;
    padding: 0.5rem 0 !important;
  }
  
  header .container {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  
  /* Search improvements */
  .header-search {
    max-width: 200px !important;
  }
  
  .header-search .h-8 {
    height: 1.75rem !important;
  }
  
  /* Hide regular navigation menu in tablet landscape */
  .NavigationMenu:not(.dropdown-nav) {
    display: none !important;
  }
  
  /* Show dropdown navigation */
  .responsive-dropdown-nav {
    display: flex !important;
  }
  
  /* Navigation menu improvements */
  .NavigationMenu {
    overflow: visible !important;
  }
  
  /* Button sizes */
  .header-nav button,
  .header-nav a {
    height: 2rem !important; /* Smaller buttons */
    min-height: 2rem !important;
  }
  
  /* Cart/Wishlist icon optimization */
  .hidden.sm\:flex {
    display: flex !important;
  }
  
  .hidden.sm\:flex button {
    width: 2rem !important;
    height: 2rem !important;
    padding: 0.25rem !important;
  }
  
  /* Icon sizes */
  .hidden.sm\:flex svg {
    width: 1rem !important;
    height: 1rem !important;
  }
  
  /* Badge positioning */
  .hidden.sm\:flex .absolute {
    top: -0.125rem !important;
    right: -0.125rem !important;
    width: 0.875rem !important;
    height: 0.875rem !important;
    font-size: 0.625rem !important;
  }
  
  /* Prevent horizontal scrolling */
  body {
    overflow-x: hidden !important;
  }
  
  /* Header container width management */
  header .container {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* Fix any z-index issues */
  header {
    position: sticky !important;
    top: 0 !important;
    z-index: 50 !important;
  }
  
  /* Ensure proper flex behavior */
  .hidden.lg\:flex.items-center.justify-between.gap-2.min-w-0.overflow-hidden {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* Emergency overflow fix */
  * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
}

/* Tablet landscape orientation - ENHANCED VERSION */
@media (min-width: 768px) and (max-width: 1330px) and (orientation: landscape) {
  /* Hide both mobile and tablet navs on tablet landscape */
  .mobile-bottom-nav,
  .tablet-nav {
    display: none !important;
  }
  
  body {
    padding-bottom: 0 !important;
  }
  
  /* FORCE hide desktop layout using multiple selectors */
  header .desktop-layout,
  .container .desktop-layout,
  div.desktop-layout,
  .hidden.lg\:flex.desktop-layout {
    display: none !important;
    visibility: hidden !important;
  }
  
  /* FORCE show tablet landscape layout using multiple selectors */
  header .tablet-landscape-layout,
  .container .tablet-landscape-layout,
  div.tablet-landscape-layout,
  .hidden.md\:flex.lg\:hidden.tablet-landscape-layout {
    display: flex !important;
    visibility: visible !important;
  }
  
  /* Tablet landscape header styling */
  .tablet-landscape-header {
    display: flex !important;
    padding: 0.5rem 1rem !important;
    min-height: 60px !important;
    border-bottom: 1px solid #e5e7eb !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
    background: white !important;
  }
  
  /* Override Tailwind hidden classes specifically for tablet landscape */
  .tablet-landscape-layout.hidden {
    display: flex !important;
  }
  
  .desktop-layout.lg\:flex {
    display: none !important;
  }
  
  /* Side menu overlay styles */
  .tablet-side-menu-overlay {
    z-index: 9999 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
  }
  
  /* Tablet landscape layout improvements */
  .tablet-landscape-layout {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
  }
  
  /* Force 3 products per row in tablet landscape */
  .grid {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }
  
  /* Ensure proper grid spacing for tablet landscape */
  .grid > * {
    min-width: 0 !important;
    gap: 0.75rem !important;
    min-width: 0 !important;
    padding: 0.5rem 1rem !important;
  }
  
  /* Logo adjustments */
  .tablet-landscape-layout .logo {
    flex-shrink: 0 !important;
    height: 2.5rem !important;
  }
  
  /* Navigation dropdown container */
  .tablet-landscape-layout .nav-dropdown-container {
    flex: 1 !important;
    display: flex !important;
    justify-content: center !important;
    max-width: 300px !important;
  }
  
  /* Search and actions container */
  .tablet-landscape-layout .actions-container {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    flex-shrink: 0 !important;
  }
  
  /* Search bar optimizations */
  .tablet-landscape-layout .search-container {
    max-width: 180px !important;
    min-width: 120px !important;
  }
  
  .tablet-landscape-layout .search-container input {
    font-size: 0.75rem !important;
    padding: 0.25rem 0.5rem !important;
  }
  
  /* Action buttons */
  .tablet-landscape-layout .action-button {
    height: 2rem !important;
    width: 2rem !important;
    padding: 0 !important;
  }
  
  .tablet-landscape-layout .action-button svg {
    height: 1rem !important;
    width: 1rem !important;
  }
  
  /* Badge adjustments */
  .tablet-landscape-layout .badge {
    font-size: 0.625rem !important;
    height: 1rem !important;
    width: 1rem !important;
    min-width: 1rem !important;
  }
  
  /* Sticky header */
  header {
    position: sticky !important;
    top: 0 !important;
    z-index: 50 !important;
    background: white !important;
    border-bottom: 1px solid #e5e7eb !important;
  }
}

/* Dropdown navigation specific styles */
.responsive-dropdown-nav {
  position: relative;
}

/* Tablet landscape dropdown specific styles */
.tablet-landscape-layout .responsive-dropdown-nav {
  position: relative;
  z-index: 100;
}

.tablet-landscape-layout .responsive-dropdown-nav button {
  font-weight: 500;
}



/* Sidebar animation improvements */
@keyframes sidebar-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes sidebar-slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.sidebar-enter {
  animation: sidebar-slide-in 0.3s ease-out;
}

.sidebar-exit {
  animation: sidebar-slide-out 0.25s ease-in;
}

/* Sidebar specific styles */
.responsive-dropdown-nav .sidebar-content {
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
}

/* Backdrop styles */
.sidebar-backdrop {
  backdrop-filter: blur(2px);
  transition: opacity 0.3s ease-in-out;
}

/* Small devices dropdown navigation */
@media (max-width: 767px) {
  .mobile-dropdown-nav {
    display: block !important;
  }
  
  .mobile-dropdown-nav .dropdown-content {
    position: fixed !important;
    top: auto !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    max-height: 70vh !important;
    border-radius: 1rem 1rem 0 0 !important;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15) !important;
  }
  
  .mobile-dropdown-nav .nav-item {
    padding: 1rem !important;
    border-bottom: 1px solid #f3f4f6 !important;
  }
  
  .mobile-dropdown-nav .nav-item:last-child {
    border-bottom: none !important;
  }
}

/* Medium devices (small tablets) */
@media (min-width: 768px) and (max-width: 991px) {
  .tablet-dropdown-nav {
    display: block !important;
  }
  
  /* Hide regular navigation on medium tablets */
  .NavigationMenu:not(.dropdown-nav) {
    display: none !important;
  }
}

/* Hide reCAPTCHA badge */
.grecaptcha-badge {
  visibility: hidden !important;
}

/* Account Page Tablet Landscape Fixes (768px-1329px) */
@media (min-width: 768px) and (max-width: 1329px) {
  /* Account page specific fixes */
  .account-page-container {
    max-width: 100% !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Account page grid layout optimization */
  .account-grid {
    grid-template-columns: 220px 1fr !important;
    gap: 1.5rem !important;
  }

  /* Account sidebar adjustments */
  .account-sidebar {
    min-width: 220px !important;
    max-width: 220px !important;
  }

  /* Account sidebar card padding */
  .account-sidebar .card-content {
    padding: 1rem !important;
  }

  /* Account sidebar user info */
  .account-user-info {
    margin-bottom: 1rem !important;
  }

  .account-user-avatar {
    width: 4rem !important;
    height: 4rem !important;
    margin-bottom: 0.75rem !important;
  }

  .account-user-avatar svg {
    width: 2rem !important;
    height: 2rem !important;
  }

  /* Account sidebar navigation buttons */
  .account-nav-button {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }

  .account-nav-button svg {
    width: 1rem !important;
    height: 1rem !important;
  }

  /* Account main content area */
  .account-main-content {
    min-width: 0 !important;
    width: 100% !important;
  }

  /* Account tabs list */
  .account-tabs-list {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 0.5rem !important;
    margin-bottom: 1.5rem !important;
  }

  /* Account tab triggers */
  .account-tab-trigger {
    padding: 0.75rem 1rem !important;
    font-size: 0.875rem !important;
  }

  /* Account tab content */
  .account-tab-content {
    padding: 1.5rem !important;
  }

  /* Account form grid */
  .account-form-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1rem !important;
  }

  /* Account form fields */
  .account-form-field {
    margin-bottom: 1rem !important;
  }

  .account-form-label {
    font-size: 0.875rem !important;
    margin-bottom: 0.5rem !important;
  }

  .account-form-input {
    padding: 0.75rem !important;
    font-size: 0.875rem !important;
  }

  /* Account overview cards */
  .account-overview-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1.5rem !important;
  }

  .account-overview-card {
    padding: 1.5rem !important;
  }

  .account-overview-card h3 {
    font-size: 1.125rem !important;
    margin-bottom: 1rem !important;
  }

  /* Account info rows */
  .account-info-row {
    padding: 0.75rem 0 !important;
    border-bottom: 1px solid #f1f5f9 !important;
  }

  .account-info-label {
    font-size: 0.875rem !important;
  }

  .account-info-value {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
  }

  /* Popular Categories Carousel Tablet Landscape Optimizations */
  .popular-categories-section {
    padding: 2rem 0 !important;
  }

  .popular-categories-container {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
    max-width: 1200px !important;
  }

  .popular-categories-title {
    font-size: 1.75rem !important;
    margin-bottom: 2rem !important;
  }

  /* FORCE 4x2 grid layout for tablet landscape - Override all grid classes */
  .popular-categories-carousel .carousel-item > div > div.grid {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 2rem !important;
    width: 100% !important;
  }

  /* Ensure carousel content doesn't override grid */
  .popular-categories-carousel .carousel-content {
    display: flex !important;
  }

  /* Make sure each carousel item takes full width */
  .popular-categories-carousel .carousel-item {
    flex: 0 0 100% !important;
    min-width: 0 !important;
  }

  /* Carousel item sizing for tablet landscape 4x2 grid */
  .popular-categories-grid-item {
    padding: 1.5rem 1rem !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    min-height: 160px !important;
    justify-content: center !important;
  }

  .popular-categories-image-container {
    width: 8rem !important;
    height: 8rem !important;
    margin-bottom: 1rem !important;
    flex-shrink: 0 !important;
  }

  .popular-categories-image-container img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
  }

  .popular-categories-title-text {
    font-size: 1.125rem !important;
    line-height: 1.4 !important;
    text-align: center !important;
    font-weight: 600 !important;
    max-width: 100% !important;
    word-wrap: break-word !important;
    hyphens: auto !important;
  }

  .popular-categories-parent-text {
    font-size: 0.75rem !important;
    margin-top: 0.25rem !important;
    opacity: 0.7 !important;
  }

  /* Carousel navigation for tablet landscape */
  .popular-categories-carousel .carousel-previous,
  .popular-categories-carousel .carousel-next {
    width: 3rem !important;
    height: 3rem !important;
    border-radius: 50% !important;
  }

  /* Remove conflicting carousel content gap */

  /* Override grid styles for tablet landscape */
  .popular-categories-carousel [class*="grid-cols"] {
    grid-template-columns: repeat(4, 1fr) !important;
  }

  /* Specific tablet landscape viewport fixes */
  .popular-categories-carousel .carousel-item > div > div {
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 1.5rem !important;
  }

  /* Ensure minimum height for category items */
  .popular-categories-carousel .popular-categories-grid-item > a {
    min-height: 120px !important;
    justify-content: space-between !important;
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
  
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
  }
  
  .scrollbar-track-gray-100::-webkit-scrollbar-track {
    background-color: #f3f4f6;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* React Phone Input 2 - Remove white background from country dropdown */
.react-tel-input .flag-dropdown {
  background: transparent !important;
}

.react-tel-input .country-list {
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
}

.react-tel-input .country-list .country {
  background: transparent !important;
  color: hsl(var(--foreground)) !important;
}

.react-tel-input .country-list .country:hover {
  background: hsl(var(--accent)) !important;
}

.react-tel-input .country-list .country.highlight {
  background: hsl(var(--accent)) !important;
}

.react-tel-input .search-box {
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
  color: hsl(var(--foreground)) !important;
}

/* React Phone Input 2 styles */
.react-tel-input .search-box:focus {
  border-color: hsl(var(--ring)) !important;
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 2px hsl(var(--ring)) !important;
}

/* Tablet responsiveness fixes for 1024px-1400px range */
@media (min-width: 1024px) and (max-width: 1400px) {
  /* Header improvements for tablets */
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  
  /* Header logo sizing for tablets */
  .header-logo {
    height: 3.5rem; /* 56px - between mobile and desktop */
  }
  
  /* Header search bar improvements */
  .header-search {
    max-width: 20rem; /* 320px - better proportion for tablets */
  }
  
  /* Header navigation spacing */
  .header-nav {
    gap: 1rem; /* Reduce gap between nav items */
  }
  
  /* Footer grid improvements */
  .footer-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
  
  /* Footer text sizing */
  .footer-text {
    font-size: 0.9rem;
    line-height: 1.4;
  }
  
  /* Footer headings */
  .footer-heading {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }
  
  /* Footer links spacing */
  .footer-links {
    gap: 0.75rem;
  }
  
  /* Newsletter form improvements */
  .newsletter-input {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  /* Social media icons */
  .social-icons {
    gap: 1rem;
  }
}

/* Dialog z-index fixes */
.dialog-overlay-fix {
  z-index: 9990 !important;
}

.dialog-content-fix {
  z-index: 9999 !important;
  background-color: white !important;
  position: relative !important;
}

/* Custom accordion styles */
.address-accordion-trigger {
  transition: all 0.3s ease;
}

.address-accordion-trigger[data-state="open"] {
  background: linear-gradient(to right, #e0f2fe, #e0e7ff);
}

.address-accordion-content {
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

.address-accordion-content[data-state="closed"] {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideDown {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-accordion-content-height);
    opacity: 1;
  }
  to {
    height: 0;
    opacity: 0;
  }
}