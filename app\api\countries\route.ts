import { NextRequest, NextResponse } from 'next/server';
import { Config } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Forward the request to the external API
    const response = await fetch(
      `${Config.ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-countries-list`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(body),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      console.error('External API error:', data);
      return NextResponse.json(
        { error: 'Failed to fetch countries', details: data },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}