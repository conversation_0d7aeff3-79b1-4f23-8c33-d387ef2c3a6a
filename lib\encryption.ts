import CryptoJS from 'crypto-js';

// Secret key for encryption - in production, this should be in environment variables
const SECRET_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'your-secret-key-change-this-in-production';

/**
 * Encrypt a string value
 */
export function encryptValue(value: string | number): string {
  try {
    const stringValue = value.toString();
    const encrypted = CryptoJS.AES.encrypt(stringValue, SECRET_KEY).toString();
    // Make it URL-safe by replacing characters
    return encrypted.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  } catch (error) {
    console.error('Encryption error:', error);
    return value.toString();
  }
}

/**
 * Decrypt a string value
 */
export function decryptValue(encryptedValue: string): string {
  try {
    // Restore the original base64 format
    let restored = encryptedValue.replace(/-/g, '+').replace(/_/g, '/');
    
    // Add padding if needed
    while (restored.length % 4) {
      restored += '=';
    }
    
    const decrypted = CryptoJS.AES.decrypt(restored, SECRET_KEY);
    const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);
    
    if (!decryptedString) {
      throw new Error('Failed to decrypt value');
    }
    
    return decryptedString;
  } catch (error) {
    console.error('Decryption error:', error);
    return encryptedValue; // Return original if decryption fails
  }
}

/**
 * Encrypt an order ID for URL usage
 */
export function encryptOrderId(orderId: string | number): string {
  return encryptValue(orderId);
}

/**
 * Decrypt an order ID from URL
 */
export function decryptOrderId(encryptedOrderId: string): string {
  return decryptValue(encryptedOrderId);
}