'use client';

import { useEffect, useState } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { useSettings } from '@/contexts/settings-context';
import ProductCard from '@/components/product-card';
import { Flame } from 'lucide-react';
import { Config } from '@/lib/config';
import { useColorThemeContext } from '@/contexts/color-theme-context';

// Updated interface to match ProductCard expectations
interface Product {
  ProductId: number;
  ProductName: string;
  Price: number;
  OldPrice?: number;
  DiscountPrice?: number;
  Rating: number;
  ProductImageUrl?: string;
  CategoryName: string;
  StockQuantity: number;
  ProductTypeName?: string;
  IQDPrice?: number;
  IsDiscountAllowed?: boolean;
  MarkAsNew?: boolean;
  SellStartDatetimeUTC?: string;
  SellEndDatetimeUTC?: string;
}

export function TodayHotDeal() {
  const { currentTheme } = useColorThemeContext();
  const primaryColor = currentTheme.primary;
  
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPopularProducts = async () => {
      setLoading(true);
      try {
        // Prepare parameters and headers according to API requirements
        const param = {
          requestParameters: {
            PageNo: 1,
            PageSize: 3 // Get 3 popular products for hot deals
          }
        };

        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        };

        // Import MakeApiCallAsync from api-helper
        const { MakeApiCallAsync } = await import('@/lib/api-helper');

        // Use the new API endpoint for popular products
        const response = await MakeApiCallAsync(
          'get-popular-products-list',
          null,
          param,
          headers,
          "POST",
          true
        );

        // Process the response
        if (response?.data?.data) {
          try {
            const parsedData = JSON.parse(response.data.data);
            console.log('Popular products data:', parsedData);

            if (Array.isArray(parsedData) && parsedData.length > 0) {
              // Transform the API data to match ProductCard interface
              const transformedProducts = parsedData.map(item => {
                // Process image URL similar to products page
                const rawUrl = item.ProductImagesUrl || item.ProductImageUrl;
                let imageUrl = null;

                try {
                  if (rawUrl) {
                    let cleanUrl = rawUrl;

                    if (typeof rawUrl === 'string' && (rawUrl.startsWith('[') || rawUrl.startsWith('"'))) {
                      try {
                        const parsed = JSON.parse(rawUrl);
                        if (Array.isArray(parsed) && parsed.length > 0) {
                          cleanUrl = parsed[0].AttachmentURL || parsed[0];
                        } else if (typeof parsed === 'string') {
                          cleanUrl = parsed;
                        }
                      } catch (jsonError) {
                        cleanUrl = rawUrl.replace(/^"|"/g, '');
                      }
                    }

                    if (typeof cleanUrl === 'string' && cleanUrl.trim() !== '') {
                      cleanUrl = cleanUrl.replace(/^"|"$/g, '').trim();

                      if (cleanUrl) {
                        const decodedUrl = decodeURIComponent(cleanUrl);
                        const normalizedUrl = decodedUrl.startsWith('/') || decodedUrl.startsWith('http') 
                          ? decodedUrl 
                          : `/${decodedUrl}`;

                        imageUrl = normalizedUrl.startsWith('http') 
                          ? normalizedUrl 
                          : `${Config.ADMIN_BASE_URL}${normalizedUrl}`;
                      }
                    }
                  }
                } catch (error) {
                  console.error('Error processing URL for product', item.ProductId, ':', error);
                }

                return {
                  ProductId: item.ProductId || item.ProductID || 0,
                  ProductName: item.ProductName || 'Popular Product',
                  Price: parseFloat(item.Price) || 0,
                  OldPrice: item.OldPrice ? parseFloat(item.OldPrice) : undefined,
                  DiscountPrice: item.DiscountPrice ? parseFloat(item.DiscountPrice) : undefined,
                  Rating: parseFloat(item.Rating) || 0,
                  ProductImageUrl: imageUrl || undefined,
                  CategoryName: item.CategoryName || 'Popular',
                  StockQuantity: parseInt(item.StockQuantity, 10) || 0,
                  ProductTypeName: item.ProductTypeName,
                  IQDPrice: parseFloat(item.IQDPrice) || undefined,
                  IsDiscountAllowed: Boolean(item.IsDiscountAllowed),
                  MarkAsNew: Boolean(item.MarkAsNew),
                  SellStartDatetimeUTC: item.SellStartDatetimeUTC,
                  SellEndDatetimeUTC: item.SellEndDatetimeUTC
                };
              });

              setProducts(transformedProducts);
            } else {
              console.log('No popular products found');
              setProducts([]);
            }
          } catch (parseError) {
            console.error('Error parsing popular products data:', parseError);
            setProducts([]);
          }
        } else {
          console.error('Invalid or empty response from API');
          setProducts([]);
        }
      } catch (error) {
        console.error('Error fetching popular products:', error);
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPopularProducts();
  }, []);

  if (loading) {
    return (
      <div className="w-full">
        <div className="flex items-center space-x-2 mb-6">
          <h2 className="text-2xl font-bold">Today's Hot Deals</h2>
          <Flame className="h-6 w-6" style={{ color: primaryColor }} />
        </div>
        
        {/* Display three products in a line - matching products page styling */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 sm:gap-6">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow overflow-hidden">
              <div className="aspect-square">
                <Skeleton className="h-full w-full" />
              </div>
              <div className="p-4 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-6 w-1/3" />
              </div>
              <div className="p-4 pt-0">
                <div className="flex w-full gap-2">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-10" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="w-full text-center py-8">
        <div className="flex items-center justify-center space-x-2 mb-4">
          <h2 className="text-2xl font-bold">Today's Hot Deals</h2>
          <Flame className="h-6 w-6" style={{ color: primaryColor }} />
        </div>
        <p className="text-muted-foreground">No hot deals available at the moment</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex items-center space-x-2 mb-6">
        <h2 className="text-2xl font-bold">Today's Hot Deals</h2>
        <Flame className="h-6 w-6" style={{ color: primaryColor }} />
      </div>
      
      {/* Display three products in a line - matching products page styling */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 sm:gap-6">
        {products.slice(0, 5).map((product) => (
          <ProductCard
            key={product.ProductId}
            product={product}
          />
        ))}
      </div>
    </div>
  );
}