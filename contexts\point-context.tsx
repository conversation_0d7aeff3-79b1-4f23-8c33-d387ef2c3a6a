"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { <PERSON><PERSON><PERSON>elper } from "@/lib/cookie-helper";
import { MakeApiCallAsync } from "@/lib/api-helper";
import { useUser } from "@/contexts/user-context";

interface PointContextType {
  points: string | null;
  isLoading: boolean;
  error: string | null;
  fetchPoints: () => Promise<void>;
  updatePoints: (newPoints: string) => void;
  clearPoints: () => void;
  refreshPointsFromCookies: () => void;
}

const PointContext = createContext<PointContextType | undefined>(undefined);

interface PointProviderProps {
  children: ReactNode;
}

export function PointProvider({ children }: PointProviderProps) {
  const [points, setPoints] = useState<string>('0');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasInitialLoad, setHasInitialLoad] = useState(false);
  const { isLoggedIn, isLoading: userLoading } = useUser();

  // Load points from cookies on mount
  useEffect(() => {
    try {
      const savedPoints = CookieHelper.getUserPoints();
      if (savedPoints) {
        setPoints(savedPoints);
        console.log("✅ PointProvider: Loaded points from cookies:", savedPoints);
      }
      setHasInitialLoad(true);
    } catch (error) {
      console.error("❌ PointProvider: Error loading points from cookies:", error);
      setHasInitialLoad(true);
    }
  }, []);

  // Set up auto-refresh and initial fetch only for logged-in users
  useEffect(() => {
    if (!userLoading && isLoggedIn && hasInitialLoad) {
      console.log("🔄 PointProvider: Initial points fetch for logged-in user");
      fetchPoints(false); // Manual fetch with loading state
      
      // Set up auto-refresh every 5 minutes (300,000 milliseconds)
      const intervalId = setInterval(() => {
        console.log("🔄 PointProvider: Auto-refreshing points (5-minute interval)");
        fetchPoints(true); // Pass true for auto-refresh
      }, 5 * 60 * 1000); // 5 minutes
      
      // Cleanup interval on unmount or when user logs out
      return () => {
        clearInterval(intervalId);
      };
    }
  }, [hasInitialLoad, isLoggedIn, userLoading]);

  const fetchPoints = async (isAutoRefresh: boolean = false): Promise<void> => {
    // Only fetch points if user is logged in
    if (!isLoggedIn) {
      console.log("⚠️ PointProvider: Skipping points fetch - user not logged in");
      return;
    }

    // Only show loading state for manual refresh, not auto-refresh
    if (!isAutoRefresh) {
      setIsLoading(true);
    }
    setError(null);

    try {
      console.log(`🔄 PointProvider: Fetching points from API ${isAutoRefresh ? '(auto-refresh)' : '(manual)'}`);

      const headers = {
        Accept: "application/json",
        "Content-Type": "application/json",
      };

      const param = {
        requestParameters: {
          recordValueJson: "[]"
        }
      };

      const response = await MakeApiCallAsync(
        "get-point",
        null,
        param,
        headers,
        "POST",
        true
      );

      console.log("📊 PointProvider: API response:", response);

      if (response?.data?.statusCode === 200 && response.data.data !== undefined) {
        const pointValue = response.data.data.toString();
        setPoints(pointValue);
        CookieHelper.saveUserPoints(pointValue);
        console.log("✅ PointProvider: Points fetched and saved:", pointValue);
      } else {
        throw new Error(response?.data?.errorMessage || "Failed to fetch points");
      }
    } catch (error: any) {
      console.error("❌ PointProvider: Error fetching points:", error);
      setError(error.message || "Failed to fetch points");

      // Keep existing points from cookies if API fails
      const savedPoints = CookieHelper.getUserPoints();
      if (savedPoints) {
        setPoints(savedPoints);
        console.log("🔄 PointProvider: Using cached points due to API error:", savedPoints);
      }
    } finally {
      if (!isAutoRefresh) {
        setIsLoading(false);
      }
    }
  };

  const updatePoints = (newPoints: string): void => {
    try {
      setPoints(newPoints);
      CookieHelper.saveUserPoints(newPoints);
      console.log("✅ PointProvider: Points updated:", newPoints);

      // Force a re-render by updating the state
      setError(null); // Clear any previous errors
    } catch (error) {
      console.error("❌ PointProvider: Error updating points:", error);
    }
  };

  const refreshPointsFromCookies = (): void => {
    try {
      const cookiePoints = CookieHelper.getUserPoints();
      if (cookiePoints && cookiePoints !== points) {
        console.log("🔄 PointProvider: Refreshing points from cookies:", cookiePoints);
        setPoints(cookiePoints);
      }
    } catch (error) {
      console.error("❌ PointProvider: Error refreshing from cookies:", error);
    }
  };

  const clearPoints = (): void => {
    try {
      setPoints('0');
      CookieHelper.clearUserPoints();
      console.log("✅ PointProvider: Points cleared");
    } catch (error) {
      console.error("❌ PointProvider: Error clearing points:", error);
    }
  };

  return (
    <PointContext.Provider
      value={{
        points,
        isLoading,
        error,
        fetchPoints,
        updatePoints,
        clearPoints,
        refreshPointsFromCookies,
      }}
    >
      {children}
    </PointContext.Provider>
  );
}

export function usePoints() {
  const context = useContext(PointContext);
  if (context === undefined) {
    throw new Error('usePoints must be used within a PointProvider');
  }
  return context;
}
