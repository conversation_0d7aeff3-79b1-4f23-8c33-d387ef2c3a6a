import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { Config } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Log the request for debugging
    console.log('Order API request body:', JSON.stringify(body, null, 2));

    // Extract JWT token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : authHeader;

    // Prepare headers for the remote API call
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add JWT token to headers if available
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
      // Also add to Token header for backward compatibility
      headers['Token'] = token;
      console.log('🔐 Added JWT token to order placement request');
    }

    // Remove UserID from request body if present (JWT token will be used instead)
    const cleanedBody = { ...body };
    if (cleanedBody.UserID) {
      console.log('🔧 Removing UserID from order request body (will use JWT token instead)');
      delete cleanedBody.UserID;
    }

    // Log the full URL and request details
    const fullUrl = `${Config.ADMIN_BASE_URL}api/v1/common/post-order-direct`;
    console.log('🌐 Making request to:', fullUrl);
    console.log('📋 Request headers:', JSON.stringify(headers, null, 2));
    console.log('📦 Request body:', JSON.stringify(cleanedBody, null, 2));

    // Forward the request to the remote API
    const response = await fetch(fullUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(cleanedBody),
    });

    const data = await response.json();
    console.log('📊 External API response status:', response.status);
    console.log('📊 External API response headers:', Object.fromEntries(response.headers.entries()));
    console.log('📊 External API response data:', JSON.stringify(data, null, 2));

    if (!response.ok) {
      console.error('❌ External API error - Status:', response.status);
      console.error('❌ External API error - Data:', data);
      return NextResponse.json(
        {
          error: 'Failed to place order',
          details: data,
          status: response.status,
          url: fullUrl
        },
        { status: response.status }
      );
    }

    // If order was successful and points were used, update user points in cookies
    if (data.StatusCode === 200 && cleanedBody.Point && cleanedBody.Point > 0) {
      try {
        console.log('🔄 Order successful, updating user points in cookies');

        // Get current user data from cookies
        const cookieStore = await cookies();
        const userCookie = cookieStore.get('auth_user');

        if (userCookie?.value) {
          const userData = JSON.parse(userCookie.value);
          const currentPoints = userData.Pointno || 0;
          const pointsUsed = cleanedBody.Point;
          const newPointBalance = Math.max(0, currentPoints - pointsUsed);

          console.log(`🔢 Points update: Current=${currentPoints}, Used=${pointsUsed}, New=${newPointBalance}`);

          // Update user data with new point balance
          userData.Pointno = newPointBalance;

          // Update the cookie with new user data
          const response = NextResponse.json(data);
          response.cookies.set('auth_user', JSON.stringify(userData), {
            httpOnly: false, // Client-side accessible for React context
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict',
            path: '/',
            maxAge: 7 * 24 * 60 * 60, // 7 days
          });

          console.log('✅ User points updated in cookies successfully');
          return response;
        }
      } catch (error) {
        console.error('❌ Error updating user points in cookies:', error);
        // Don't fail the order if cookie update fails
      }
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('API route error:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}