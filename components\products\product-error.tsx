'use client';

import React from 'react';
import { AlertCircle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useColorThemeContext } from '@/contexts/color-theme-context';
import Link from 'next/link';

interface ProductErrorProps {
  error?: string;
  onRetry?: () => void;
}

const ProductError: React.FC<ProductErrorProps> = ({ 
  error = "Failed to load product details", 
  onRetry 
}) => {
  const { currentTheme } = useColorThemeContext();
  const destructiveColor = '#dc2626'; // Red color for error states
  
  return (
    <div className="container mx-auto py-12 px-4">
      <div className="max-w-md mx-auto text-center">
        <div 
          className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
          style={{ backgroundColor: `${destructiveColor}20` }}
        >
          <AlertCircle className="h-8 w-8" style={{ color: destructiveColor }} />
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Product Not Found
        </h2>
        
        <p className="text-gray-600 mb-6">
          {error}
        </p>
        
        <div className="space-y-3">
          {onRetry && (
            <Button 
              onClick={onRetry}
              className="w-full"
              variant="default"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
          
          <Button 
            asChild
            variant="outline"
            className="w-full"
          >
            <Link href="/">
              <Home className="h-4 w-4 mr-2" />
              Go to Homepage
            </Link>
          </Button>
          
          <Button 
            asChild
            variant="outline"
            className="w-full"
          >
            <Link href="/products">
              Browse All Products
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProductError;
