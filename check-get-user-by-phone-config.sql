-- SQL script to check the get-user-by-phone endpoint configuration
-- This will help us understand how the phone number lookup is configured

-- Check if the get-user-by-phone configuration exists
SELECT 
    Id,
    Title,
    UrlName,
    MethodType,
    SqlQuery,
    DataParams,
    DefaultParams,
    IsActive
FROM APIConfigurations 
WHERE UrlName = 'get-user-by-phone';

-- Also check for any phone-related user lookup configurations
SELECT 
    Id,
    Title,
    UrlName,
    MethodType,
    SqlQuery,
    DataParams,
    DefaultParams,
    IsActive
FROM APIConfigurations 
WHERE UrlName LIKE '%phone%' OR UrlName LIKE '%user%';

-- Check the Users table structure to see phone number column names
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Users' 
AND (COLUMN_NAME LIKE '%phone%' OR COLUMN_NAME LIKE '%mobile%');

-- Sample phone number data from Users table
SELECT TOP 5
    UserId,
    PhoneNo,
    MobileNo,
    EmailAddress
FROM Users 
WHERE PhoneNo IS NOT NULL OR MobileNo IS NOT NULL;