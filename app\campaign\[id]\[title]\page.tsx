import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { MakeApiCallAsync } from '@/lib/api-helper';
import CampaignDetailClient from './client-page';
import { generateCampaignCanonicalUrl, generateMetaWithCanonical } from '@/lib/canonical-utils';

interface CampaignDetail {
  CampaignId: string;
  MainTitle: string;
  DiscountTitle: string;
  CoverPictureUrl: string;
  Body: string;
}

async function getCampaignDetail(id: string): Promise<CampaignDetail | null> {
  try {
    const param = {
      requestParameters: {
        CampaignId: id,
      },
    };

    const headers = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };

    const response = await MakeApiCallAsync(
      'get-web-campaign-detail',
      null,
      param,
      headers,
      'POST',
      true
    );

    if (response?.data?.data) {
      try {
        const parsedData = JSON.parse(response.data.data);
        if (parsedData) {
          return {
            CampaignId: parsedData.CampaignId?.toString() || '',
            MainTitle: parsedData.MainTitle || parsedData.Title || '',
            DiscountTitle: parsedData.DiscountTitle || parsedData.SubTitle || '',
            CoverPictureUrl: parsedData.CoverPictureUrl || parsedData.ImageUrl || '',
            Body: parsedData.Body || '',
          };
        }
      } catch (parseError) {
        console.error('Error parsing campaign data:', parseError);
      }
    }
    return null;
  } catch (error) {
    console.error('Error fetching campaign detail:', error);
    return null;
  }
}

type Props = {
  params: Promise<{ id: string; title: string }>;
  searchParams: Promise<Record<string, string | string[] | undefined>>;
};

// Generate metadata for SEO
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id, title } = await params;
  const campaign = await getCampaignDetail(id);
  const adminPanelBaseURL = process.env.NEXT_PUBLIC_ADMIN_BASE_URL || '';

  if (!campaign) {
    const canonicalUrl = generateCampaignCanonicalUrl(id);
    return generateMetaWithCanonical(
      canonicalUrl,
      'Campaign Not Found - Code Medical Apps',
      'The requested campaign could not be found.',
      {
        robots: {
          index: false,
          follow: false,
        },
      }
    );
  }

  const metaTitle = `${campaign.MainTitle} - Code Medical Apps`;
  const metaDescription = campaign.DiscountTitle || `Discover ${campaign.MainTitle} - Special offers and medical resources.`;
  const canonicalUrl = generateCampaignCanonicalUrl(id, campaign.MainTitle);
  
  const campaignImage = campaign.CoverPictureUrl
    ? `${adminPanelBaseURL}${campaign.CoverPictureUrl}`
    : '/placeholder.svg?height=400&width=400';

  return generateMetaWithCanonical(
    canonicalUrl,
    metaTitle,
    metaDescription,
    {
      keywords: `${campaign.MainTitle}, medical campaign, medical offers, healthcare deals, medical education`,
      openGraph: {
        type: 'website',
        images: [
          {
            url: campaignImage,
            width: 1200,
            height: 630,
            alt: campaign.MainTitle,
          },
        ],
      },
      twitter: {
        images: [campaignImage],
      },
    }
  );
}

export default async function CampaignDetailPage({ params }: Props) {
  const resolvedParams = await params;
  const campaign = await getCampaignDetail(resolvedParams.id);
  const adminPanelBaseURL = process.env.NEXT_PUBLIC_ADMIN_BASE_URL || '';
  
  if (!campaign) {
    notFound();
  }

  return <CampaignDetailClient initialCampaign={campaign} adminPanelBaseURL={adminPanelBaseURL} />;
}